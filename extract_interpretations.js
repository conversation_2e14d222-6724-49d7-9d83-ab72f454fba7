/**
 * Script pour extraire toutes les interprétations du fichier Francais - Transit natal.txt
 * et les formater pour l'intégration dans le code JavaScript
 */

const fs = require('fs');
const path = require('path');

// Lire le fichier d'interprétations
const filePath = path.join(__dirname, 'Interprétations transit natal', 'Francais - Transit natal.txt');

try {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  
  // Nettoyer le texte (supprimer les lignes d'en-tête)
  const cleanedText = fileContent.replace(/^Okay.*?```plaintext/s, '').replace(/```$/s, '');
  
  // Diviser le texte en lignes
  const lines = cleanedText.split('\n');
  
  let interpretations = {};
  let currentKey = null;
  let currentText = '';
  
  // Parcourir chaque ligne
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Si la ligne est vide, passer à la suivante
    if (line === '') continue;
    
    // Si la ligne commence par '[', c'est une nouvelle clé
    if (line.startsWith('[') && line.includes(']')) {
      // Si nous avons déjà une clé, enregistrer l'interprétation précédente
      if (currentKey) {
        interpretations[currentKey] = currentText.trim();
        currentText = '';
      }
      
      // Extraire la nouvelle clé
      currentKey = line.substring(1, line.indexOf(']'));
    }
    // Sinon, c'est une partie du texte d'interprétation
    else if (currentKey && !line.startsWith('//')) {
      currentText += line + ' ';
    }
  }
  
  // Enregistrer la dernière interprétation
  if (currentKey && currentText) {
    interpretations[currentKey] = currentText.trim();
  }
  
  console.log(`Nombre d'interprétations extraites: ${Object.keys(interpretations).length}`);
  
  // Générer le code JavaScript pour la base de données
  let jsCode = '    // Database of interpretations from the French transit file\n';
  jsCode += '    const interpretationDatabase = {\n';
  
  const keys = Object.keys(interpretations).sort();
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const text = interpretations[key];
    
    // Échapper les apostrophes et guillemets
    const escapedText = text.replace(/'/g, "\\'").replace(/"/g, '\\"');
    
    jsCode += `      '[${key}]': '${escapedText}'`;
    
    if (i < keys.length - 1) {
      jsCode += ',';
    }
    jsCode += '\n';
  }
  
  jsCode += '    };\n';
  
  // Sauvegarder le code généré
  fs.writeFileSync('interpretations_database.js', jsCode);
  
  console.log('Code JavaScript généré et sauvegardé dans interpretations_database.js');
  console.log('\nPremières interprétations extraites:');
  
  // Afficher quelques exemples
  const exampleKeys = keys.slice(0, 5);
  exampleKeys.forEach(key => {
    console.log(`[${key}]: ${interpretations[key].substring(0, 100)}...`);
  });
  
} catch (error) {
  console.error('Erreur lors de la lecture du fichier:', error);
}
