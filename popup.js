/**
 * Main functionality for the Sidereal Astrology Chrome Extension
 */

document.addEventListener('DOMContentLoaded', function() {
  // DOM elements
  const birthDateInput = document.getElementById('birth-date');
  const transitDateInput = document.getElementById('transit-date');
  const calculateBtn = document.getElementById('calculate-btn');
  const positionsList = document.getElementById('positions-list');
  const transitsList = document.getElementById('transits-list');
  const chartCanvas = document.getElementById('chart-canvas');
  
  // Set default dates
  const today = new Date().toISOString().split('T')[0];
  birthDateInput.value = today;
  transitDateInput.value = today;
  
  // Load saved dates from storage
  chrome.storage.local.get(['birthDate', 'transitDate'], function(result) {
    if (result.birthDate) {
      birthDateInput.value = result.birthDate;
    }
    if (result.transitDate) {
      transitDateInput.value = result.transitDate;
    }
    
    // Calculate chart with saved dates
    calculateChart();
  });
  
  // Event listeners
  calculateBtn.addEventListener('click', function() {
    calculateChart();
    
    // Save dates to storage
    chrome.storage.local.set({
      birthDate: birthDateInput.value,
      transitDate: transitDateInput.value
    });
  });
  
  // Calculate and display the chart
  function calculateChart() {
    const birthDate = new Date(birthDateInput.value);
    const transitDate = new Date(transitDateInput.value);
    
    // Calculate planetary positions
    const birthPositions = calculatePlanetaryPositions(birthDate);
    const transitPositions = calculatePlanetaryPositions(transitDate);
    
    // Calculate aspects between birth and transit
    const aspects = calculateAspects(birthPositions, transitPositions);
    
    // Display planetary positions
    displayPlanetaryPositions(birthPositions);
    
    // Display transits and aspects
    displayTransits(transitPositions, aspects);
    
    // Draw the chart
    drawChart(birthPositions, transitPositions);
  }
  
  // Display planetary positions in the positions list
  function displayPlanetaryPositions(positions) {
    positionsList.innerHTML = '';
    
    const planets = [
      { key: 'sun', name: 'Sun' },
      { key: 'moon', name: 'Moon' },
      { key: 'mercury', name: 'Mercury' },
      { key: 'venus', name: 'Venus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturn' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];
    
    planets.forEach(planet => {
      const planetData = positions[planet.key];
      const planetItem = document.createElement('div');
      planetItem.className = 'planet-item';
      
      const planetSymbol = PLANET_SYMBOLS[planet.name] || '';
      
      planetItem.innerHTML = `
        <div class="planet-name">
          <span class="planet-symbol">${planetSymbol}</span>
          ${planet.name}
        </div>
        <div class="planet-degree">
          <span class="sign">${planetData.sign}</span> 
          ${planetData.degree}°
        </div>
      `;
      
      positionsList.appendChild(planetItem);
    });
  }
  
  // Display transits and aspects
  function displayTransits(transitPositions, aspects) {
    transitsList.innerHTML = '';
    
    // First, show current planetary positions
    const planets = [
      { key: 'sun', name: 'Sun' },
      { key: 'moon', name: 'Moon' },
      { key: 'mercury', name: 'Mercury' },
      { key: 'venus', name: 'Venus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturn' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];
    
    // Add transit positions
    planets.forEach(planet => {
      const planetData = transitPositions[planet.key];
      const transitItem = document.createElement('div');
      transitItem.className = 'transit-item';
      
      const planetSymbol = PLANET_SYMBOLS[planet.name] || '';
      
      transitItem.innerHTML = `
        <div class="planet-name">
          <span class="planet-symbol">${planetSymbol}</span>
          ${planet.name}
        </div>
        <div class="planet-degree">
          <span class="sign">${planetData.sign}</span> 
          ${planetData.degree}°
        </div>
      `;
      
      transitsList.appendChild(transitItem);
    });
    
    // Add a separator
    const separator = document.createElement('hr');
    transitsList.appendChild(separator);
    
    // Add aspects header
    const aspectsHeader = document.createElement('h3');
    aspectsHeader.textContent = 'Aspects';
    transitsList.appendChild(aspectsHeader);
    
    // Add aspects
    if (aspects.length > 0) {
      aspects.forEach(aspect => {
        const aspectItem = document.createElement('div');
        aspectItem.className = 'transit-item aspect-item';
        
        const birthPlanetName = aspect.birthPlanet.charAt(0).toUpperCase() + aspect.birthPlanet.slice(1);
        const transitPlanetName = aspect.transitPlanet.charAt(0).toUpperCase() + aspect.transitPlanet.slice(1);
        
        aspectItem.innerHTML = `
          <div>${birthPlanetName} ${aspect.aspect} ${transitPlanetName}</div>
          <div>Orb: ${aspect.orb}°</div>
        `;
        
        transitsList.appendChild(aspectItem);
      });
    } else {
      const noAspectsItem = document.createElement('div');
      noAspectsItem.className = 'transit-item';
      noAspectsItem.textContent = 'No significant aspects found.';
      transitsList.appendChild(noAspectsItem);
    }
  }
  
  // Draw the astrological chart on the canvas
  function drawChart(birthPositions, transitPositions) {
    const ctx = chartCanvas.getContext('2d');
    const width = chartCanvas.width = chartCanvas.offsetWidth;
    const height = chartCanvas.height = chartCanvas.offsetHeight;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) - 20;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw zodiac wheel
    drawZodiacWheel(ctx, centerX, centerY, radius);
    
    // Draw planets for birth chart
    drawPlanets(ctx, centerX, centerY, radius * 0.7, birthPositions, false);
    
    // Draw planets for transit chart
    drawPlanets(ctx, centerX, centerY, radius * 0.5, transitPositions, true);
  }
  
  // Draw the zodiac wheel
  function drawZodiacWheel(ctx, centerX, centerY, radius) {
    // Draw outer circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // Draw inner circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.8, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.stroke();
    
    // Draw zodiac signs
    for (let i = 0; i < 12; i++) {
      const angle = (i * 30 - 90) * Math.PI / 180;
      
      // Draw sign division lines
      ctx.beginPath();
      ctx.moveTo(
        centerX + radius * 0.8 * Math.cos(angle),
        centerY + radius * 0.8 * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle)
      );
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 1;
      ctx.stroke();
      
      // Draw sign symbol
      const symbolAngle = (i * 30 - 90 + 15) * Math.PI / 180;
      const symbolX = centerX + radius * 0.9 * Math.cos(symbolAngle);
      const symbolY = centerY + radius * 0.9 * Math.sin(symbolAngle);
      
      ctx.font = '14px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(ZODIAC_SIGNS[i].substring(0, 3), symbolX, symbolY);
    }
  }
  
  // Draw planets on the chart
  function drawPlanets(ctx, centerX, centerY, radius, positions, isTransit) {
    const planets = [
      { key: 'sun', name: 'Sun' },
      { key: 'moon', name: 'Moon' },
      { key: 'mercury', name: 'Mercury' },
      { key: 'venus', name: 'Venus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturn' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];
    
    planets.forEach(planet => {
      const planetData = positions[planet.key];
      const angle = (planetData.siderealLongitude - 90) * Math.PI / 180;
      
      const planetX = centerX + radius * Math.cos(angle);
      const planetY = centerY + radius * Math.sin(angle);
      
      // Draw planet symbol
      ctx.font = isTransit ? '12px Arial' : '14px Arial';
      ctx.fillStyle = isTransit ? '#0071e3' : '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      const planetSymbol = PLANET_SYMBOLS[planet.name] || planet.name.substring(0, 2);
      ctx.fillText(planetSymbol, planetX, planetY);
      
      // Draw a small circle around the planet
      ctx.beginPath();
      ctx.arc(planetX, planetY, isTransit ? 10 : 12, 0, 2 * Math.PI);
      ctx.strokeStyle = isTransit ? '#0071e3' : '#333';
      ctx.lineWidth = 1;
      ctx.stroke();
    });
  }
  
  // Initialize the chart on load
  calculateChart();
});
