# Système de Sauvegarde et Persistance des Données

## Vue d'ensemble

L'extension utilise maintenant un système de sauvegarde robuste qui persiste toutes les données utilisateur même après désinstallation/réinstallation de l'extension Chrome.

## Fonctionnalités

### 🔄 Migration Automatique
- Migration automatique des données de `localStorage` vers `chrome.storage.sync`
- Sauvegarde dans le cloud Google (synchronisée entre appareils)
- Fallback vers `localStorage` en cas d'erreur

### 💾 Données Sauvegardées
- **Règles astrologiques** : Toutes les règles personnalisées avec leurs effets
- **Événements du calendrier** : Commentaires et couleurs personnalisées des jours
- **Interprétations personnalisées** : Textes d'interprétation modifiés
- **Données de naissance** : Date, heure et minute de naissance par défaut
- **Paramètres de l'interface** : Préférences utilisateur

### 📤 Export/Import
- **Bouton d'export** : Télécharge un fichier JSON avec toutes les données
- **Bouton d'import** : Restaure les données depuis un fichier JSON
- **Format de fichier** : `astro-data-backup-YYYY-MM-DD.json`

## Utilisation

### Export des Données
1. Cliquez sur le bouton "💾 Exporter données"
2. Un fichier JSON sera téléchargé automatiquement
3. Conservez ce fichier comme sauvegarde

### Import des Données
1. Cliquez sur le bouton "📁 Importer données"
2. Sélectionnez un fichier JSON d'export précédent
3. Les données seront restaurées automatiquement
4. Rechargez la page pour voir les changements

## Architecture Technique

### Fichiers Impliqués
- `storage-migration.js` : Système de migration et gestion du stockage
- `sidepanel-simple.js` : Interface d'export/import
- `astrological-rules.js` : Sauvegarde des règles astrologiques

### API Chrome Storage
- Utilise `chrome.storage.sync` pour la persistance cloud
- Limite de 100KB par extension
- Synchronisation automatique entre appareils Chrome connectés

### Structure des Données
```json
{
  "astrologicalRules": [...],
  "miniCalendarColors": {...},
  "customInterpretations": {...},
  "birthDate": "1991-12-17",
  "birthHour": 7,
  "birthMinute": 27
}
```

## Avantages

### ✅ Persistance Totale
- Les données survivent à la désinstallation/réinstallation
- Sauvegarde automatique dans le cloud Google
- Synchronisation entre tous les appareils Chrome

### ✅ Sécurité
- Fallback vers localStorage en cas d'erreur
- Validation des données lors de l'import
- Messages d'erreur informatifs

### ✅ Facilité d'Utilisation
- Migration automatique transparente
- Interface simple d'export/import
- Pas de configuration requise

## Dépannage

### Problèmes de Synchronisation
- Vérifiez que vous êtes connecté à Chrome
- Assurez-vous que la synchronisation Chrome est activée
- Les données peuvent prendre quelques minutes à se synchroniser

### Erreurs d'Import
- Vérifiez que le fichier JSON est valide
- Assurez-vous qu'il s'agit d'un fichier d'export de cette extension
- Contactez le support si le problème persiste

### Données Manquantes
- Utilisez la fonction d'export pour créer une sauvegarde
- Vérifiez les paramètres de synchronisation Chrome
- Les données localStorage sont automatiquement migrées au premier lancement

## Notes Techniques

### Limites Chrome Storage
- 100KB maximum par extension
- 8KB maximum par élément
- Quota partagé entre tous les appareils

### Performance
- Chargement asynchrone des données
- Mise en cache locale pour les accès rapides
- Optimisation des écritures pour éviter les conflits

### Compatibilité
- Chrome 88+ requis pour `chrome.storage.sync`
- Fallback localStorage pour les versions antérieures
- Support complet des navigateurs Chromium
