// Planetary positions calculation using <PERSON>'s method
// Based on http://www.stjarnhimlen.se/comp/ppcomp.html

console.log("Planetary positions calculator loaded (<PERSON>'s method)");

const SwissEph = {
  // Constants for zodiac signs
  SIGNS: [
    "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>ir<PERSON>", "Li<PERSON>", "<PERSON><PERSON><PERSON>",
    "Sagittarius", "Capricorn", "Aquarius", "Pisces"
  ],

  // Constants for planets
  PLANETS: {
    SUN: 0,
    MOON: 1,
    MERCURY: 2,
    VENUS: 3,
    MARS: 4,
    JUPITER: 5,
    SATURN: 6,
    URANUS: 7,
    NEPTUNE: 8,
    PLUTO: 9
  },

  // Planet symbols
  PLANET_SYMBOLS: {
    0: "☉", // Sun
    1: "☽", // Moon
    2: "☿", // Mercury
    3: "♀", // Venus
    4: "♂", // Mars
    5: "♃", // Jupiter
    6: "♄", // Saturn
    7: "♅", // Uranus
    8: "♆", // Neptune
    9: "♇"  // Pluto
  },

  // Convert degrees to radians
  toRadians: function(degrees) {
    return degrees * Math.PI / 180;
  },

  // Convert radians to degrees
  toDegrees: function(radians) {
    return radians * 180 / Math.PI;
  },

  // Normalize angle to range 0-360 degrees
  normalizeAngle: function(angle) {
    angle = angle % 360;
    if (angle < 0) angle += 360;
    return angle;
  },

  // Calculate the Julian Day for a given date
  getJulianDay: function(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();

    // Calculate decimal day
    const decimalDay = day + (hour + minute/60 + second/3600)/24;

    // Calculate Julian Day
    let jd;
    if (month <= 2) {
      jd = Math.floor(365.25 * (year - 1)) + Math.floor(30.6001 * (month + 12 + 1)) + decimalDay + 1720981.5;
    } else {
      jd = Math.floor(365.25 * year) + Math.floor(30.6001 * (month + 1)) + decimalDay + 1720981.5;
    }

    return jd;
  },

  // Calculate the number of days since J2000.0 (January 1, 2000, 12:00 UTC)
  getDaysSinceJ2000: function(jd) {
    return jd - 2451545.0;
  },

  // Calculate the Sun's position
  calculateSunPosition: function(d) {
    // Mean anomaly of the Sun
    const M = this.normalizeAngle(357.529 + 0.98560028 * d);

    // Mean longitude of the Sun
    const L = this.normalizeAngle(280.459 + 0.98564736 * d);

    // Ecliptic longitude of the Sun
    const lambda = this.normalizeAngle(L + 1.915 * Math.sin(this.toRadians(M)) + 0.020 * Math.sin(this.toRadians(2 * M)));

    return lambda;
  },

  // Calculate the Moon's position
  calculateMoonPosition: function(d) {
    // Mean longitude of the Moon
    const L = this.normalizeAngle(218.316 + 13.176396 * d);

    // Mean anomaly of the Moon
    const M = this.normalizeAngle(134.963 + 13.064993 * d);

    // Mean anomaly of the Sun
    const Ms = this.normalizeAngle(357.529 + 0.98560028 * d);

    // Moon's argument of latitude
    const F = this.normalizeAngle(93.272 + 13.229350 * d);

    // Longitude of the Moon's ascending node
    const omega = this.normalizeAngle(125.044 - 0.052954 * d);

    // Ecliptic longitude of the Moon
    let lambda = L + 6.289 * Math.sin(this.toRadians(M));
    lambda += 1.274 * Math.sin(this.toRadians(2 * (L - Ms) - M));
    lambda += 0.658 * Math.sin(this.toRadians(2 * (L - Ms)));
    lambda += 0.213 * Math.sin(this.toRadians(2 * M));
    lambda -= 0.185 * Math.sin(this.toRadians(Ms));
    lambda -= 0.114 * Math.sin(this.toRadians(2 * F));
    lambda += 0.059 * Math.sin(this.toRadians(2 * (L - Ms) - 2 * M));
    lambda += 0.057 * Math.sin(this.toRadians(2 * (L - Ms) + M));
    lambda = this.normalizeAngle(lambda);

    return lambda;
  },

  // Calculate Mercury's position
  calculateMercuryPosition: function(d) {
    // Mercury's orbital elements
    const N = this.normalizeAngle(48.3313 + 3.24587E-5 * d); // Long of asc. node
    const i = 7.0047 + 5.00E-8 * d; // Inclination
    const w = this.normalizeAngle(29.1241 + 1.01444E-5 * d); // Argument of perihelion
    const a = 0.387098; // Semi-major axis
    const e = 0.205635 + 5.59E-10 * d; // Eccentricity
    const M = this.normalizeAngle(168.6562 + 4.0923344368 * d); // Mean anomaly

    // Eccentric anomaly (approximate solution using iterative method)
    let E = M;
    for (let i = 0; i < 10; i++) {
      E = M + e * Math.sin(this.toRadians(E)) * 180 / Math.PI;
    }

    // True anomaly
    const xv = a * (Math.cos(this.toRadians(E)) - e);
    const yv = a * Math.sqrt(1 - e * e) * Math.sin(this.toRadians(E));
    const v = this.toDegrees(Math.atan2(yv, xv));

    // Distance from the Sun
    const r = Math.sqrt(xv * xv + yv * yv);

    // Heliocentric longitude
    const lonHelio = this.normalizeAngle(v + w);

    // Convert to geocentric longitude (simplified)
    // For a more accurate calculation, we would need to consider Earth's position
    const sunLon = this.calculateSunPosition(d);
    const mercuryLon = this.normalizeAngle(lonHelio + 180);

    // Adjust for Mercury's position relative to the Sun
    let geocentricLon;
    if (Math.abs(mercuryLon - sunLon) > 180) {
      geocentricLon = this.normalizeAngle(sunLon + (mercuryLon - sunLon) / 10);
    } else {
      geocentricLon = this.normalizeAngle(sunLon - (sunLon - mercuryLon) / 10);
    }

    return geocentricLon;
  },

  // Calculate Venus's position
  calculateVenusPosition: function(d) {
    // Venus's orbital elements
    const N = this.normalizeAngle(76.6799 + 2.46590E-5 * d); // Long of asc. node
    const i = 3.3946 + 2.75E-8 * d; // Inclination
    const w = this.normalizeAngle(54.8910 + 1.38374E-5 * d); // Argument of perihelion
    const a = 0.723330; // Semi-major axis
    const e = 0.006773 - 1.302E-9 * d; // Eccentricity
    const M = this.normalizeAngle(48.0052 + 1.6021302244 * d); // Mean anomaly

    // Eccentric anomaly (approximate solution using iterative method)
    let E = M;
    for (let i = 0; i < 10; i++) {
      E = M + e * Math.sin(this.toRadians(E)) * 180 / Math.PI;
    }

    // True anomaly
    const xv = a * (Math.cos(this.toRadians(E)) - e);
    const yv = a * Math.sqrt(1 - e * e) * Math.sin(this.toRadians(E));
    const v = this.toDegrees(Math.atan2(yv, xv));

    // Distance from the Sun
    const r = Math.sqrt(xv * xv + yv * yv);

    // Heliocentric longitude
    const lonHelio = this.normalizeAngle(v + w);

    // Convert to geocentric longitude (simplified)
    const sunLon = this.calculateSunPosition(d);
    const venusLon = this.normalizeAngle(lonHelio + 180);

    // Adjust for Venus's position relative to the Sun
    let geocentricLon;
    if (Math.abs(venusLon - sunLon) > 180) {
      geocentricLon = this.normalizeAngle(sunLon + (venusLon - sunLon) / 5);
    } else {
      geocentricLon = this.normalizeAngle(sunLon - (sunLon - venusLon) / 5);
    }

    return geocentricLon;
  },

  // Calculate Mars's position
  calculateMarsPosition: function(d) {
    // Mars's orbital elements
    const N = this.normalizeAngle(49.5574 + 2.11081E-5 * d); // Long of asc. node
    const i = 1.8497 - 1.78E-8 * d; // Inclination
    const w = this.normalizeAngle(286.5016 + 2.92961E-5 * d); // Argument of perihelion
    const a = 1.523688; // Semi-major axis
    const e = 0.093405 + 2.516E-9 * d; // Eccentricity
    const M = this.normalizeAngle(18.6021 + 0.5240207766 * d); // Mean anomaly

    // Eccentric anomaly (approximate solution using iterative method)
    let E = M;
    for (let i = 0; i < 10; i++) {
      E = M + e * Math.sin(this.toRadians(E)) * 180 / Math.PI;
    }

    // True anomaly
    const xv = a * (Math.cos(this.toRadians(E)) - e);
    const yv = a * Math.sqrt(1 - e * e) * Math.sin(this.toRadians(E));
    const v = this.toDegrees(Math.atan2(yv, xv));

    // Distance from the Sun
    const r = Math.sqrt(xv * xv + yv * yv);

    // Heliocentric longitude
    const lonHelio = this.normalizeAngle(v + w);

    // Convert to geocentric longitude (simplified)
    const sunLon = this.calculateSunPosition(d);
    const marsLon = lonHelio;

    // Adjust for Mars's position relative to the Sun
    const geocentricLon = this.normalizeAngle(marsLon);

    return geocentricLon;
  },

  // Calculate Jupiter's position
  calculateJupiterPosition: function(d) {
    // Jupiter's orbital elements
    const N = this.normalizeAngle(100.4542 + 2.76854E-5 * d); // Long of asc. node
    const i = 1.3030 - 1.557E-7 * d; // Inclination
    const w = this.normalizeAngle(273.8777 + 1.64505E-5 * d); // Argument of perihelion
    const a = 5.20256; // Semi-major axis
    const e = 0.048498 + 4.469E-9 * d; // Eccentricity
    const M = this.normalizeAngle(19.8950 + 0.0830853001 * d); // Mean anomaly

    // Eccentric anomaly (approximate solution using iterative method)
    let E = M;
    for (let i = 0; i < 10; i++) {
      E = M + e * Math.sin(this.toRadians(E)) * 180 / Math.PI;
    }

    // True anomaly
    const xv = a * (Math.cos(this.toRadians(E)) - e);
    const yv = a * Math.sqrt(1 - e * e) * Math.sin(this.toRadians(E));
    const v = this.toDegrees(Math.atan2(yv, xv));

    // Distance from the Sun
    const r = Math.sqrt(xv * xv + yv * yv);

    // Heliocentric longitude
    const lonHelio = this.normalizeAngle(v + w);

    // Convert to geocentric longitude (simplified)
    const geocentricLon = lonHelio;

    return geocentricLon;
  },

  // Calculate Saturn's position
  calculateSaturnPosition: function(d) {
    // Saturn's orbital elements
    const N = this.normalizeAngle(113.6634 + 2.38980E-5 * d); // Long of asc. node
    const i = 2.4886 - 1.081E-7 * d; // Inclination
    const w = this.normalizeAngle(339.3939 + 2.97661E-5 * d); // Argument of perihelion
    const a = 9.55475; // Semi-major axis
    const e = 0.055546 - 9.499E-9 * d; // Eccentricity
    const M = this.normalizeAngle(316.9670 + 0.0334442282 * d); // Mean anomaly

    // Eccentric anomaly (approximate solution using iterative method)
    let E = M;
    for (let i = 0; i < 10; i++) {
      E = M + e * Math.sin(this.toRadians(E)) * 180 / Math.PI;
    }

    // True anomaly
    const xv = a * (Math.cos(this.toRadians(E)) - e);
    const yv = a * Math.sqrt(1 - e * e) * Math.sin(this.toRadians(E));
    const v = this.toDegrees(Math.atan2(yv, xv));

    // Distance from the Sun
    const r = Math.sqrt(xv * xv + yv * yv);

    // Heliocentric longitude
    const lonHelio = this.normalizeAngle(v + w);

    // Convert to geocentric longitude (simplified)
    const geocentricLon = lonHelio;

    return geocentricLon;
  },

  // Calculate Uranus's position
  calculateUranusPosition: function(d) {
    // Uranus's orbital elements
    const N = this.normalizeAngle(74.0005 + 1.3978E-5 * d); // Long of asc. node
    const i = 0.7733 + 1.9E-8 * d; // Inclination
    const w = this.normalizeAngle(96.6612 + 3.0565E-5 * d); // Argument of perihelion
    const a = 19.18171 - 1.55E-8 * d; // Semi-major axis
    const e = 0.047318 + 7.45E-9 * d; // Eccentricity
    const M = this.normalizeAngle(142.5905 + 0.011725806 * d); // Mean anomaly

    // Eccentric anomaly (approximate solution using iterative method)
    let E = M;
    for (let i = 0; i < 10; i++) {
      E = M + e * Math.sin(this.toRadians(E)) * 180 / Math.PI;
    }

    // True anomaly
    const xv = a * (Math.cos(this.toRadians(E)) - e);
    const yv = a * Math.sqrt(1 - e * e) * Math.sin(this.toRadians(E));
    const v = this.toDegrees(Math.atan2(yv, xv));

    // Distance from the Sun
    const r = Math.sqrt(xv * xv + yv * yv);

    // Heliocentric longitude
    const lonHelio = this.normalizeAngle(v + w);

    // Convert to geocentric longitude (simplified)
    const geocentricLon = lonHelio;

    return geocentricLon;
  },

  // Calculate Neptune's position
  calculateNeptunePosition: function(d) {
    // Neptune's orbital elements
    const N = this.normalizeAngle(131.7806 + 3.0173E-5 * d); // Long of asc. node
    const i = 1.7700 - 2.55E-7 * d; // Inclination
    const w = this.normalizeAngle(272.8461 - 6.027E-6 * d); // Argument of perihelion
    const a = 30.05826 + 3.313E-8 * d; // Semi-major axis
    const e = 0.008606 + 2.15E-9 * d; // Eccentricity
    const M = this.normalizeAngle(260.2471 + 0.005995147 * d); // Mean anomaly

    // Eccentric anomaly (approximate solution using iterative method)
    let E = M;
    for (let i = 0; i < 10; i++) {
      E = M + e * Math.sin(this.toRadians(E)) * 180 / Math.PI;
    }

    // True anomaly
    const xv = a * (Math.cos(this.toRadians(E)) - e);
    const yv = a * Math.sqrt(1 - e * e) * Math.sin(this.toRadians(E));
    const v = this.toDegrees(Math.atan2(yv, xv));

    // Distance from the Sun
    const r = Math.sqrt(xv * xv + yv * yv);

    // Heliocentric longitude
    const lonHelio = this.normalizeAngle(v + w);

    // Convert to geocentric longitude (simplified)
    const geocentricLon = lonHelio;

    return geocentricLon;
  },

  // Calculate Pluto's position (simplified)
  calculatePlutoPosition: function(d) {
    // Pluto's orbital elements (simplified)
    const N = this.normalizeAngle(110.30 + 0.00 * d); // Long of asc. node
    const i = 17.14 + 0.00 * d; // Inclination
    const w = this.normalizeAngle(113.63 + 0.00 * d); // Argument of perihelion
    const a = 39.48; // Semi-major axis
    const e = 0.2488 + 0.00 * d; // Eccentricity
    const M = this.normalizeAngle(14.86 + 0.00396 * d); // Mean anomaly

    // Eccentric anomaly (approximate solution using iterative method)
    let E = M;
    for (let i = 0; i < 10; i++) {
      E = M + e * Math.sin(this.toRadians(E)) * 180 / Math.PI;
    }

    // True anomaly
    const xv = a * (Math.cos(this.toRadians(E)) - e);
    const yv = a * Math.sqrt(1 - e * e) * Math.sin(this.toRadians(E));
    const v = this.toDegrees(Math.atan2(yv, xv));

    // Distance from the Sun
    const r = Math.sqrt(xv * xv + yv * yv);

    // Heliocentric longitude
    const lonHelio = this.normalizeAngle(v + w);

    // Convert to geocentric longitude (simplified)
    const geocentricLon = lonHelio;

    return geocentricLon;
  },

  // Calculate planet positions for a given date and time
  calculatePlanetPositions: function(date, time, latitude, longitude) {
    // Parse date and time
    const dateObj = new Date(`${date}T${time}`);

    // Calculate Julian Day
    const jd = this.getJulianDay(dateObj);

    // Calculate days since J2000.0
    const d = this.getDaysSinceJ2000(jd);

    // Calculate positions for each planet
    return {
      0: this.formatPlanetPosition(this.calculateSunPosition(d)), // Sun
      1: this.formatPlanetPosition(this.calculateMoonPosition(d)), // Moon
      2: this.formatPlanetPosition(this.calculateMercuryPosition(d)), // Mercury
      3: this.formatPlanetPosition(this.calculateVenusPosition(d)), // Venus
      4: this.formatPlanetPosition(this.calculateMarsPosition(d)), // Mars
      5: this.formatPlanetPosition(this.calculateJupiterPosition(d)), // Jupiter
      6: this.formatPlanetPosition(this.calculateSaturnPosition(d)), // Saturn
      7: this.formatPlanetPosition(this.calculateUranusPosition(d)), // Uranus
      8: this.formatPlanetPosition(this.calculateNeptunePosition(d)), // Neptune
      9: this.formatPlanetPosition(this.calculatePlutoPosition(d))  // Pluto
    };
  },

  // Format planet position into a standard object
  formatPlanetPosition: function(longitude) {
    // Calculate sign (0-11) and degree within sign (0-30)
    const sign = Math.floor(longitude / 30);
    const degree = longitude % 30;

    return {
      longitude: longitude,
      sign: sign,
      degree: degree,
      signName: this.SIGNS[sign],
      formattedPosition: `${this.SIGNS[sign]} ${Math.floor(degree)}°${Math.floor((degree % 1) * 60)}'`
    };
  },

  // Calculate current planet positions
  getCurrentPlanetPositions: function() {
    const now = new Date();
    return this.calculatePlanetPositions(
      now.toISOString().split('T')[0],
      now.toTimeString().split(' ')[0],
      0, 0 // Default latitude and longitude
    );
  },

  // Convert tropical positions to sidereal positions
  // In a real implementation, this would use proper ayanamsa calculations
  tropicalToSidereal: function(positions, ayanamsa = 24) {
    const siderealPositions = {};

    for (const planetId in positions) {
      const position = positions[planetId];

      // Adjust longitude by ayanamsa
      let siderealLongitude = position.longitude - ayanamsa;
      if (siderealLongitude < 0) {
        siderealLongitude += 360;
      }

      // Recalculate sign and degree
      const sign = Math.floor(siderealLongitude / 30);
      const degree = siderealLongitude % 30;

      siderealPositions[planetId] = {
        longitude: siderealLongitude,
        sign: sign,
        degree: degree,
        signName: this.SIGNS[sign],
        formattedPosition: `${this.SIGNS[sign]} ${Math.floor(degree)}°${Math.floor((degree % 1) * 60)}'`
      };
    }

    return siderealPositions;
  },

  // Calculate nakshatra (lunar mansion) for a given longitude
  // There are 27 nakshatras in 360 degrees, so each is 13.33 degrees
  calculateNakshatra: function(longitude) {
    const nakshatraNames = [
      "Ashwini", "Bharani", "Krittika", "Rohini", "Mrigashira", "Ardra",
      "Punarvasu", "Pushya", "Ashlesha", "Magha", "Purva Phalguni", "Uttara Phalguni",
      "Hasta", "Chitra", "Swati", "Vishakha", "Anuradha", "Jyeshtha",
      "Mula", "Purva Ashadha", "Uttara Ashadha", "Shravana", "Dhanishta", "Shatabhisha",
      "Purva Bhadrapada", "Uttara Bhadrapada", "Revati"
    ];

    const nakshatraSize = 360 / 27;
    const nakshatra = Math.floor(longitude / nakshatraSize);
    const degreeInNakshatra = longitude % nakshatraSize;

    return {
      nakshatra: nakshatra,
      name: nakshatraNames[nakshatra],
      degree: degreeInNakshatra,
      formattedPosition: `${nakshatraNames[nakshatra]} ${Math.floor(degreeInNakshatra)}°${Math.floor((degreeInNakshatra % 1) * 60)}'`
    };
  }
};

// Make the SwissEph object globally available
window.SwissEph = SwissEph;
