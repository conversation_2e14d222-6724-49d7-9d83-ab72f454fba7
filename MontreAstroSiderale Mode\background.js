// Background script for the Sidereal Astrology Chrome Extension

// Listen for installation
chrome.runtime.onInstalled.addListener(function() {
  console.log('Extension Montre Astro Sidérale installed');

  // Initialize default settings if needed
  chrome.storage.sync.get(['birthDate', 'birthTime', 'birthLocation'], function(data) {
    if (!data.birthDate) {
      // Set default values if none exist
      chrome.storage.sync.set({
        birthDate: '1991-12-17',
        birthTime: '07:27',
        birthLocation: 'Paris, France'
      });
      console.log('Default birth data set: 17/12/1991 7h27, Paris, France');
    } else {
      console.log('Existing birth data found:', data);
    }
  });

  // Log that we're using popup mode
  console.log('Extension configured to use popup mode');
});

// Listen for messages from popup or content scripts
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'getCurrentTransits') {
    // In a real implementation, this would calculate the current planetary positions
    // For now, we'll just return a success message
    sendResponse({ success: true, message: 'Transits calculation requested' });
    return true; // Keep the message channel open for async response
  }
});
