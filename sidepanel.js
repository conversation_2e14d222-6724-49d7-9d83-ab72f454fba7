/**
 * Main functionality for the Sidereal Astrology Chrome Extension Side Panel
 */

// Variables globales pour stocker les données
let globalDailyAspects = [];

// Variables pour stocker les filtres d'aspects
let aspectFilters = {
  maxOrb: 2,
  types: ['conjunction', 'opposition']
};

// Variable pour stocker tous les aspects calculés
let allCalculatedAspects = [];
// Initialisation des aspects futurs avec des valeurs par défaut
let futureAspects = {
  standard: [
    { angle: 0, name: "Conjonction (0°)", selected: true },
    { angle: 16, name: "16° Aspect", selected: true },
    { angle: 30, name: "Semi-sextile (30°)", selected: false },
    { angle: 45, name: "Semi-carré (45°)", selected: true },
    { angle: 60, name: "Sex<PERSON> (60°)", selected: false },
    { angle: 90, name: "<PERSON><PERSON> (90°)", selected: false },
    { angle: 120, name: "<PERSON><PERSON><PERSON> (120°)", selected: false },
    { angle: 135, name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (135°)", selected: false },
    { angle: 150, name: "<PERSON><PERSON><PERSON><PERSON> (150°)", selected: false },
    { angle: 180, name: "Opposition (180°)", selected: true }
  ],
  custom: []
};

document.addEventListener('DOMContentLoaded', function() {
  // DOM elements that exist in the simplified version
  const birthDateInput = document.getElementById('birth-date');
  const birthHourInput = document.getElementById('birth-hour');
  const birthMinuteInput = document.getElementById('birth-minute');
  const transitDateInput = document.getElementById('transit-date');
  const transitHourInput = document.getElementById('transit-hour');
  const transitMinuteInput = document.getElementById('transit-minute');
  const calculateBtn = document.getElementById('calculate-btn');



  // Set default dates
  const today = new Date().toISOString().split('T')[0];
  birthDateInput.value = today;

  // Set transit date to current date and time
  updateTransitToCurrentDateTime();

  // Update transit date and time every second to keep it synchronized with PC time
  // and recalculate chart automatically
  setInterval(function() {
    updateTransitToCurrentDateTime();
    // Recalculate chart with each second update to ensure the chart is always in sync
    // with the current transit time
    calculateChart();
  }, 1000); // 1000 ms = 1 second

  // Add event listener for calculate button
  calculateBtn.addEventListener('click', function() {
    calculateChart();
  });

  // Function to update transit date and time to current date and time
  function updateTransitToCurrentDateTime() {
    const now = new Date();
    transitDateInput.value = now.toISOString().split('T')[0];
    transitHourInput.value = now.getHours();
    transitMinuteInput.value = now.getMinutes();

    // Update seconds field if it exists
    const transitSecondInput = document.getElementById('transit-second');
    if (transitSecondInput) {
      transitSecondInput.value = now.getSeconds();
    }

    // Disable transit date and time inputs to prevent user modification
    transitDateInput.disabled = true;
    transitHourInput.disabled = true;
    transitMinuteInput.disabled = true;
    if (transitSecondInput) {
      transitSecondInput.disabled = true;
    }
  }

  // Load saved dates and times from storage
  chrome.storage.local.get(['birthDate', 'birthHour', 'birthMinute'], function(result) {
    if (result.birthDate) {
      birthDateInput.value = result.birthDate;
    }
    if (result.birthHour !== undefined) {
      birthHourInput.value = result.birthHour;
    }
    if (result.birthMinute !== undefined) {
      birthMinuteInput.value = result.birthMinute;
    }

    // Always use current date and time for transit
    updateTransitToCurrentDateTime();

    // Calculate chart with saved birth date/time and current transit date/time
    calculateChart();

    // Update day comments display
    updateDayCommentsDisplay();
  });

  // Event listeners
  calculateBtn.addEventListener('click', function() {
    calculateChart();

    // Update day comments display
    updateDayCommentsDisplay();

    // Save birth date and time to storage
    chrome.storage.local.set({
      birthDate: birthDateInput.value,
      birthHour: parseInt(birthHourInput.value),
      birthMinute: parseInt(birthMinuteInput.value)
    });
  });

  // Add input validation for hour and minute fields
  birthHourInput.addEventListener('change', function() {
    validateTimeInput(birthHourInput, 0, 23);
  });

  birthMinuteInput.addEventListener('change', function() {
    validateTimeInput(birthMinuteInput, 0, 59);
  });

  transitHourInput.addEventListener('change', function() {
    validateTimeInput(transitHourInput, 0, 23);
  });

  transitMinuteInput.addEventListener('change', function() {
    validateTimeInput(transitMinuteInput, 0, 59);
  });

  // Add event listener for weather forecast calculation
  const masterPlanetSelect = document.getElementById('master-planet');
  const weatherStartDateInput = document.getElementById('weather-start-date');
  const weatherPeriodInput = document.getElementById('weather-period');
  const aspectOrbInput = document.getElementById('aspect-orb');
  const calculateWeatherBtn = document.getElementById('calculate-weather-btn');
  const prevWeekBtn = document.getElementById('prev-week-btn');
  const nextWeekBtn = document.getElementById('next-week-btn');
  const totalAspectsCount = document.getElementById('total-aspects-count');
  const positiveAspectsCount = document.getElementById('positive-aspects-count');
  const negativeAspectsCount = document.getElementById('negative-aspects-count');
  const masterAspectsCount = document.getElementById('master-aspects-count');
  const weatherSummaryText = document.getElementById('weather-summary-text');

  // Définir la date de début par défaut (premier jour du mois en cours)
  const currentDate = new Date();
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
  weatherStartDateInput.value = firstDayOfMonth.toISOString().split('T')[0];

  // Initialiser la date de début de prédiction avec la date actuelle
  const predictionStartDateInput = document.getElementById('prediction-start-date');
  if (predictionStartDateInput) {
    predictionStartDateInput.value = currentDate.toISOString().split('T')[0];
  }

  // Définir les valeurs par défaut pour le bulletin météo
  weatherPeriodInput.value = 30; // 30 jours par défaut
  aspectOrbInput.value = 1; // Orb de 1° par défaut
  masterPlanetSelect.value = 'mercury'; // Mercure comme astre maître par défaut

  // Initialiser le bouton Google Calendar (désactivé par défaut)
  const exportToGCalBtn = document.getElementById('export-to-gcal-btn');
  if (exportToGCalBtn) {
    exportToGCalBtn.disabled = true;
    exportToGCalBtn.style.opacity = '0.5';
    exportToGCalBtn.style.cursor = 'not-allowed';

    // Ajouter un event listener pour le bouton Google Calendar
    exportToGCalBtn.addEventListener('click', exportToGoogleCalendar);
  }

  // Add event listener to save master planet selection when it changes
  masterPlanetSelect.addEventListener('change', function() {
    chrome.storage.local.set({
      masterPlanet: masterPlanetSelect.value
    });
  });

  // Get aspect details modal elements
  const aspectDetailsModal = document.getElementById('aspect-details-modal');
  const aspectDetailsDate = document.getElementById('aspect-details-date');
  const aspectDetailsList = document.getElementById('aspect-details-list');
  const modalTotalAspects = document.getElementById('modal-total-aspects');
  const modalPositiveAspects = document.getElementById('modal-positive-aspects');
  const modalNegativeAspects = document.getElementById('modal-negative-aspects');
  const modalMasterAspects = document.getElementById('modal-master-aspects');
  const closeModalBtn = document.getElementById('close-aspect-details-btn');

  // Get icon customization elements
  const iconLogicRadios = document.querySelectorAll('input[name="icon-logic"]');

  // Get threshold elements
  const thresholdVeryFavorable = document.getElementById('threshold-very-favorable');
  const thresholdFavorable = document.getElementById('threshold-favorable');
  const thresholdFavorableMax = document.getElementById('threshold-favorable-max');
  const thresholdDifficult = document.getElementById('threshold-difficult');
  const thresholdDifficultMax = document.getElementById('threshold-difficult-max');
  const thresholdVeryDifficult = document.getElementById('threshold-very-difficult');

  // Get custom aspect elements
  const customAspectAngle = document.getElementById('custom-aspect-angle');
  const customAspectName = document.getElementById('custom-aspect-name');
  const customAspectType = document.getElementById('custom-aspect-type');
  const addCustomAspectBtn = document.getElementById('add-custom-aspect-btn');
  const customAspectsList = document.getElementById('custom-aspects-list');

  // Get edit aspect modal elements
  const editAspectModal = document.getElementById('edit-aspect-modal');
  const editAspectId = document.getElementById('edit-aspect-id');
  const editAspectName = document.getElementById('edit-aspect-name');
  const editAspectAngle = document.getElementById('edit-aspect-angle');
  const editAspectTypePositive = document.getElementById('edit-aspect-type-positive');
  const editAspectTypeNegative = document.getElementById('edit-aspect-type-negative');
  const saveAspectBtn = document.getElementById('save-aspect-btn');
  const cancelEditBtn = document.getElementById('cancel-edit-btn');
  const editAspectClose = document.getElementById('edit-aspect-close');

  // Get manage aspects modal elements
  const manageAspectsBtn = document.getElementById('manage-aspects-btn');
  const manageAspectsModal = document.getElementById('manage-aspects-modal');
  const manageAspectsList = document.getElementById('manage-aspects-list');
  const manageAspectsClose = document.getElementById('manage-aspects-close');
  const addNewAspectBtn = document.getElementById('add-new-aspect-btn');

  // Array to store custom aspects
  let customAspects = [];

  // Standard aspects data
  const standardAspects = [
    { id: "0", angle: 0, name: "Conjonction", type: "positive" },
    { id: "30", angle: 30, name: "Semi-sextile", type: "positive" },
    { id: "45", angle: 45, name: "Semi-carré", type: "negative" },
    { id: "60", angle: 60, name: "Sextile", type: "positive" },
    { id: "90", angle: 90, name: "Carré", type: "negative" },
    { id: "120", angle: 120, name: "Trigone", type: "positive" },
    { id: "135", angle: 135, name: "Sesqui-carré", type: "negative" },
    { id: "150", angle: 150, name: "Quinconce", type: "positive" },
    { id: "180", angle: 180, name: "Opposition", type: "negative" }
  ];

  // Store the current forecast period
  let currentForecastStartDate = new Date();
  currentForecastStartDate.setHours(0, 0, 0, 0);

  // Variable pour stocker les données météo pour l'exportation vers Google Calendar
  let currentWeatherData = null;

  // Add event listeners for modals
  if (manageAspectsBtn) {
    manageAspectsBtn.addEventListener('click', function() {
      openManageAspectsModal();
    });
  }

  if (manageAspectsClose) {
    manageAspectsClose.onclick = function() {
      manageAspectsModal.style.display = 'none';
    };
  }

  if (addNewAspectBtn) {
    addNewAspectBtn.addEventListener('click', function() {
      // Reset form and open edit modal for new aspect
      editAspectId.value = 'new';
      editAspectName.value = '';
      editAspectAngle.value = '16';
      editAspectTypePositive.checked = true;
      editAspectModal.style.display = 'block';
    });
  }

  if (editAspectClose) {
    editAspectClose.onclick = function() {
      editAspectModal.style.display = 'none';
    };
  }



  if (cancelEditBtn) {
    cancelEditBtn.addEventListener('click', function() {
      editAspectModal.style.display = 'none';
    });
  }

  if (saveAspectBtn) {
    saveAspectBtn.addEventListener('click', function() {
      saveAspect();
    });
  }

  // Add event listener for custom aspect button
  if (addCustomAspectBtn) {
    addCustomAspectBtn.addEventListener('click', function() {
      addCustomAspect();
    });
  }

  // Add event listeners for edit buttons on standard aspects
  document.querySelectorAll('.edit-aspect-btn').forEach(button => {
    button.addEventListener('click', function(e) {
      e.stopPropagation(); // Prevent checkbox toggle
      const aspectId = this.dataset.aspectId;
      const aspectName = this.dataset.aspectName;
      const aspectAngle = this.dataset.aspectAngle;
      const aspectType = this.dataset.aspectType;

      openEditAspectModal(aspectId, aspectName, aspectAngle, aspectType);
    });
  });

  // Add event listeners for toggling aspect type (positive/negative)
  document.querySelectorAll('input[name="positive-aspect"], input[name="negative-aspect"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      const aspectValue = parseFloat(this.value);
      const aspectName = getAspectName(aspectValue);
      const isPositive = this.name === 'positive-aspect';

      // If checked, remove from the other category
      if (this.checked) {
        if (isPositive) {
          // Remove from negative aspects if present
          const negativeCheckbox = document.querySelector(`input[name="negative-aspect"][value="${aspectValue}"]`);
          if (negativeCheckbox) {
            negativeCheckbox.checked = false;
          }
        } else {
          // Remove from positive aspects if present
          const positiveCheckbox = document.querySelector(`input[name="positive-aspect"][value="${aspectValue}"]`);
          if (positiveCheckbox) {
            positiveCheckbox.checked = false;
          }
        }
      }
    });
  });

  // Add event listeners for threshold inputs to update legend
  thresholdVeryFavorable.addEventListener('change', updateLegend);
  thresholdFavorable.addEventListener('change', updateLegend);
  thresholdFavorableMax.addEventListener('change', updateLegend);
  thresholdDifficult.addEventListener('change', updateLegend);
  thresholdDifficultMax.addEventListener('change', updateLegend);
  thresholdVeryDifficult.addEventListener('change', updateLegend);

  // Update legend initially
  updateLegend();

  // Function to update the legend based on threshold values
  function updateLegend() {
    const veryFavorableThreshold = parseInt(thresholdVeryFavorable.value) || 3;
    const favorableMinThreshold = parseInt(thresholdFavorable.value) || 1;
    const favorableMaxThreshold = parseInt(thresholdFavorableMax.value) || 2;
    const difficultMinThreshold = parseInt(thresholdDifficult.value) || 1;
    const difficultMaxThreshold = parseInt(thresholdDifficultMax.value) || 2;
    const veryDifficultThreshold = parseInt(thresholdVeryDifficult.value) || 3;

    // Update legend text
    document.getElementById('legend-very-favorable').textContent = veryFavorableThreshold + '+';
    document.getElementById('legend-favorable').textContent =
      favorableMinThreshold === favorableMaxThreshold ?
      favorableMinThreshold : `${favorableMinThreshold}-${favorableMaxThreshold}`;
    document.getElementById('legend-difficult').textContent =
      difficultMinThreshold === difficultMaxThreshold ?
      difficultMinThreshold : `${difficultMinThreshold}-${difficultMaxThreshold}`;
    document.getElementById('legend-very-difficult').textContent = veryDifficultThreshold + '+';
  }

  if (calculateWeatherBtn) {
    calculateWeatherBtn.addEventListener('click', function() {
      // Use the selected start date or default to current date
      if (weatherStartDateInput.value) {
        currentForecastStartDate = new Date(weatherStartDateInput.value);
      } else {
        currentForecastStartDate = new Date();
      }
      currentForecastStartDate.setHours(0, 0, 0, 0);
      calculateWeatherForecast(currentForecastStartDate);

      // Save settings to storage
      chrome.storage.local.set({
        masterPlanet: masterPlanetSelect.value,
        weatherStartDate: weatherStartDateInput.value,
        weatherPeriod: weatherPeriodInput.value,
        aspectOrb: aspectOrbInput.value
      });
    });
  }

  // Function to open the edit aspect modal
  function openEditAspectModal(id, name, angle, type) {
    editAspectId.value = id;
    editAspectName.value = name;
    editAspectAngle.value = angle;

    if (type === 'positive') {
      editAspectTypePositive.checked = true;
    } else {
      editAspectTypeNegative.checked = true;
    }

    // Assurez-vous que la fenêtre d'édition est au-dessus de toutes les autres
    editAspectModal.style.zIndex = '1100';
    editAspectModal.style.display = 'block';
  }

  // Function to save aspect changes
  function saveAspect() {
    const id = editAspectId.value;
    const name = editAspectName.value.trim();
    const angle = parseFloat(editAspectAngle.value) || 0;
    const type = editAspectTypePositive.checked ? 'positive' : 'negative';

    // Validate inputs
    if (!name) {
      alert('Veuillez entrer un nom pour l\'aspect.');
      return;
    }

    if (angle < 0 || angle > 180) {
      alert('L\'angle doit être compris entre 0 et 180 degrés.');
      return;
    }

    if (id === 'new') {
      // Create new custom aspect
      const newAspect = {
        id: Date.now(),
        name: name,
        angle: angle,
        type: type
      };

      // Add to custom aspects array
      customAspects.push(newAspect);

      // Add to UI
      addCustomAspectToUI(newAspect);

      // Add to appropriate aspect container
      addCustomAspectToContainer(newAspect);

      // Update manage aspects modal if open
      if (manageAspectsModal.style.display === 'block') {
        updateManageAspectsModal();
      }
    } else {
      // Check if it's a standard aspect or custom aspect
      const isStandard = standardAspects.some(a => a.id === id);

      if (isStandard) {
        // Update standard aspect in UI
        updateStandardAspect(id, name, angle, type);
      } else {
        // Update custom aspect
        const aspectIndex = customAspects.findIndex(a => a.id.toString() === id);
        if (aspectIndex !== -1) {
          // Update in array
          customAspects[aspectIndex].name = name;
          customAspects[aspectIndex].angle = angle;
          customAspects[aspectIndex].type = type;

          // Update in UI
          updateCustomAspect(customAspects[aspectIndex]);
        }
      }
    }

    // Close modal
    editAspectModal.style.display = 'none';
  }

  // Function to update a standard aspect
  function updateStandardAspect(id, name, angle, type) {
    // Find the aspect label
    const aspectLabel = document.querySelector(`.aspect-checkbox-label[data-aspect-id="${id}"]`);
    if (!aspectLabel) return;

    // Update the aspect name
    const aspectNameSpan = aspectLabel.querySelector('.aspect-name');
    if (aspectNameSpan) {
      aspectNameSpan.textContent = `${name} (${angle}°)`;
    }

    // Update the edit button data
    const editBtn = aspectLabel.querySelector('.edit-aspect-btn');
    if (editBtn) {
      editBtn.dataset.aspectName = name;
      editBtn.dataset.aspectAngle = angle;
      editBtn.dataset.aspectType = type;
    }

    // Update the checkbox value
    const checkbox = aspectLabel.querySelector('input[type="checkbox"]');
    if (checkbox) {
      checkbox.value = angle;
    }

    // If type changed, move to the other container
    const currentType = aspectLabel.classList.contains('positive') ? 'positive' : 'negative';
    if (currentType !== type) {
      // Remove from current container
      aspectLabel.remove();

      // Update class
      aspectLabel.className = `aspect-checkbox-label ${type}`;

      // Update checkbox name
      if (checkbox) {
        checkbox.name = `${type}-aspect`;
      }

      // Add to new container
      const container = document.getElementById(`${type}-aspects-container`);
      if (container) {
        container.appendChild(aspectLabel);
      }
    }

    // Update the standard aspects array for reference
    const aspectIndex = standardAspects.findIndex(a => a.id === id);
    if (aspectIndex !== -1) {
      standardAspects[aspectIndex].name = name;
      standardAspects[aspectIndex].angle = angle;
      standardAspects[aspectIndex].type = type;
    }
  }

  // Function to open the manage aspects modal
  function openManageAspectsModal() {
    updateManageAspectsModal();
    manageAspectsModal.style.display = 'block';
  }

  // Function to update the manage aspects modal content
  function updateManageAspectsModal() {
    // Clear the list
    manageAspectsList.innerHTML = '';

    // Add standard aspects
    standardAspects.forEach(aspect => {
      addAspectToManageList(aspect, true);
    });

    // Add custom aspects
    customAspects.forEach(aspect => {
      addAspectToManageList(aspect, false);
    });
  }

  // Function to add an aspect to the manage aspects list
  function addAspectToManageList(aspect, isStandard) {
    const aspectItem = document.createElement('div');
    aspectItem.className = `manage-aspect-item ${aspect.type}`;
    aspectItem.dataset.id = aspect.id;

    // Vérifier si l'aspect est activé
    const isActive = aspect.active !== false; // Par défaut, tous les aspects sont actifs

    aspectItem.innerHTML = `
      <div class="manage-aspect-info">
        <span class="manage-aspect-angle">${aspect.angle}°</span>
        <span class="manage-aspect-name">${aspect.name}</span>
        <span class="manage-aspect-type ${aspect.type}">${aspect.type === 'positive' ? 'Positif' : 'Négatif'}</span>
      </div>
      <div class="manage-aspect-actions">
        <button type="button" class="manage-aspect-btn edit" title="Éditer">✎</button>
        <button type="button" class="manage-aspect-btn toggle" title="Changer le type">⟲</button>
        <button type="button" class="manage-aspect-btn toggle-active ${isActive ? 'active' : 'inactive'}" title="${isActive ? 'Désactiver' : 'Activer'}">${isActive ? '✓' : '✗'}</button>
        <button type="button" class="manage-aspect-btn delete" title="Supprimer">×</button>
      </div>
    `;

    // Add event listeners for buttons
    aspectItem.querySelector('.edit').addEventListener('click', function() {
      openEditAspectModal(aspect.id, aspect.name, aspect.angle, aspect.type);
    });

    aspectItem.querySelector('.toggle').addEventListener('click', function() {
      // Toggle type
      const newType = aspect.type === 'positive' ? 'negative' : 'positive';

      if (isStandard) {
        // Update standard aspect
        updateStandardAspect(aspect.id, aspect.name, aspect.angle, newType);
      } else {
        // Toggle custom aspect type
        toggleCustomAspectType(aspect.id);
      }

      // Update the manage aspects modal
      updateManageAspectsModal();
    });

    // Ajouter un gestionnaire d'événements pour le bouton d'activation/désactivation
    aspectItem.querySelector('.toggle-active').addEventListener('click', function() {
      // Récupérer l'état actuel
      const isActive = this.classList.contains('active');

      // Inverser l'état
      if (isStandard) {
        // Mettre à jour l'aspect standard
        const aspectIndex = standardAspects.findIndex(a => a.id === aspect.id);
        if (aspectIndex !== -1) {
          standardAspects[aspectIndex].active = !isActive;
        }
      } else {
        // Mettre à jour l'aspect personnalisé
        const aspectIndex = customAspects.findIndex(a => a.id.toString() === aspect.id.toString());
        if (aspectIndex !== -1) {
          customAspects[aspectIndex].active = !isActive;
        }
      }

      // Mettre à jour l'interface utilisateur
      updateManageAspectsModal();
    });

    // Gestionnaire pour le bouton de suppression
    aspectItem.querySelector('.delete').addEventListener('click', function() {
      if (isStandard) {
        // Pour les aspects standard, on les supprime de la liste
        const aspectIndex = standardAspects.findIndex(a => a.id === aspect.id);
        if (aspectIndex !== -1) {
          // Supprimer l'aspect de l'array
          standardAspects.splice(aspectIndex, 1);

          // Supprimer également de l'interface utilisateur
          const aspectLabel = document.querySelector(`.aspect-checkbox-label[data-aspect-id="${aspect.id}"]`);
          if (aspectLabel) {
            aspectLabel.remove();
          }

          // Sauvegarder la liste des aspects standard mise à jour
          chrome.storage.local.set({
            standardAspects: standardAspects
          });
        }
      } else {
        // Pour les aspects personnalisés, on les supprime
        deleteCustomAspect(aspect.id);
      }

      // Mettre à jour l'interface utilisateur
      updateManageAspectsModal();
    });

    manageAspectsList.appendChild(aspectItem);
  }

  // Function to add a custom aspect
  function addCustomAspect() {
    const angle = parseFloat(customAspectAngle.value) || 16;
    let name = customAspectName.value.trim();
    const type = customAspectType.value;

    // Validate inputs
    if (angle < 0 || angle > 180) {
      alert('L\'angle doit être compris entre 0 et 180 degrés.');
      return;
    }

    // If no name provided, generate one
    if (!name) {
      name = `Aspect de ${angle}°`;
    }

    // Create custom aspect object
    const customAspect = {
      id: Date.now(),
      angle: angle,
      name: name,
      type: type
    };

    // Add to array
    customAspects.push(customAspect);

    // Add to UI
    addCustomAspectToUI(customAspect);

    // Add to appropriate aspect container
    addCustomAspectToContainer(customAspect);

    // Clear form
    customAspectAngle.value = '16';
    customAspectName.value = '';
    customAspectType.value = 'positive';
  }

  // Function to add custom aspect to UI
  function addCustomAspectToUI(aspect) {
    const aspectItem = document.createElement('div');
    aspectItem.className = `custom-aspect-item ${aspect.type}`;
    aspectItem.dataset.id = aspect.id;

    aspectItem.innerHTML = `
      <div class="custom-aspect-info">
        <span class="custom-aspect-angle">${aspect.angle}°</span>
        <span class="custom-aspect-name">${aspect.name}</span>
        <span class="custom-aspect-type ${aspect.type}">${aspect.type === 'positive' ? 'Positif' : 'Négatif'}</span>
      </div>
      <div class="custom-aspect-actions">
        <button type="button" class="custom-aspect-action-btn edit" title="Éditer">✎</button>
        <button type="button" class="custom-aspect-action-btn toggle" title="Changer le type">⟲</button>
        <button type="button" class="custom-aspect-action-btn delete" title="Supprimer">×</button>
      </div>
    `;

    // Add event listeners for buttons
    aspectItem.querySelector('.edit').addEventListener('click', function() {
      openEditAspectModal(aspect.id, aspect.name, aspect.angle, aspect.type);
    });

    aspectItem.querySelector('.toggle').addEventListener('click', function() {
      toggleCustomAspectType(aspect.id);
    });

    aspectItem.querySelector('.delete').addEventListener('click', function() {
      deleteCustomAspect(aspect.id);
    });

    customAspectsList.appendChild(aspectItem);
  }

  // Function to update a custom aspect in the UI
  function updateCustomAspect(aspect) {
    // Update in custom aspects list
    const aspectItem = document.querySelector(`.custom-aspect-item[data-id="${aspect.id}"]`);
    if (aspectItem) {
      // Update class
      aspectItem.className = `custom-aspect-item ${aspect.type}`;

      // Update content
      const angleSpan = aspectItem.querySelector('.custom-aspect-angle');
      if (angleSpan) {
        angleSpan.textContent = `${aspect.angle}°`;
      }

      const nameSpan = aspectItem.querySelector('.custom-aspect-name');
      if (nameSpan) {
        nameSpan.textContent = aspect.name;
      }

      const typeSpan = aspectItem.querySelector('.custom-aspect-type');
      if (typeSpan) {
        typeSpan.className = `custom-aspect-type ${aspect.type}`;
        typeSpan.textContent = aspect.type === 'positive' ? 'Positif' : 'Négatif';
      }
    }

    // Update in aspect container
    const oldLabel = document.querySelector(`label[data-custom-aspect-id="${aspect.id}"]`);
    if (oldLabel) {
      oldLabel.remove();
    }

    // Add to appropriate container
    addCustomAspectToContainer(aspect);
  }

  // Function to add custom aspect to appropriate container
  function addCustomAspectToContainer(aspect) {
    const container = aspect.type === 'positive' ?
      document.getElementById('positive-aspects-container') :
      document.getElementById('negative-aspects-container');

    if (!container) return;

    const label = document.createElement('label');
    label.className = `aspect-checkbox-label ${aspect.type}`;
    label.dataset.customAspectId = aspect.id;

    label.innerHTML = `
      <input type="checkbox" name="${aspect.type === 'positive' ? 'positive' : 'negative'}-aspect" value="${aspect.angle}" checked>
      <span class="aspect-name">${aspect.name} (${aspect.angle}°)</span>
      <button type="button" class="edit-aspect-btn" data-aspect-id="${aspect.id}" data-aspect-name="${aspect.name}" data-aspect-angle="${aspect.angle}" data-aspect-type="${aspect.type}">✎</button>
    `;

    // Add event listener for edit button
    label.querySelector('.edit-aspect-btn').addEventListener('click', function(e) {
      e.stopPropagation(); // Prevent checkbox toggle
      const aspectId = this.dataset.aspectId;
      const aspectName = this.dataset.aspectName;
      const aspectAngle = this.dataset.aspectAngle;
      const aspectType = this.dataset.aspectType;

      openEditAspectModal(aspectId, aspectName, aspectAngle, aspectType);
    });

    container.appendChild(label);
  }

  // Function to toggle custom aspect type
  function toggleCustomAspectType(id) {
    // Find aspect in array
    const aspectIndex = customAspects.findIndex(a => a.id.toString() === id.toString());
    if (aspectIndex === -1) return;

    // Toggle type
    const aspect = customAspects[aspectIndex];
    aspect.type = aspect.type === 'positive' ? 'negative' : 'positive';

    // Update array
    customAspects[aspectIndex] = aspect;

    // Update UI
    updateCustomAspect(aspect);
  }

  // Function to delete custom aspect
  function deleteCustomAspect(id) {
    // Remove from array
    customAspects = customAspects.filter(a => a.id.toString() !== id.toString());

    // Remove from UI
    const aspectItem = document.querySelector(`.custom-aspect-item[data-id="${id}"]`);
    if (aspectItem) {
      aspectItem.remove();
    }

    // Remove from container
    const label = document.querySelector(`label[data-custom-aspect-id="${id}"]`);
    if (label) {
      label.remove();
    }
  }

  // Add event listeners for week navigation
  if (prevWeekBtn) {
    prevWeekBtn.addEventListener('click', function() {
      // Go to previous week
      const forecastDays = parseInt(weatherPeriodInput.value) || 7;
      currentForecastStartDate.setDate(currentForecastStartDate.getDate() - forecastDays);
      calculateWeatherForecast(currentForecastStartDate);
    });
  }

  if (nextWeekBtn) {
    nextWeekBtn.addEventListener('click', function() {
      // Go to next week
      const forecastDays = parseInt(weatherPeriodInput.value) || 7;
      currentForecastStartDate.setDate(currentForecastStartDate.getDate() + forecastDays);
      calculateWeatherForecast(currentForecastStartDate);
    });
  }

  // Validate prediction months input
  predictionMonthsInput.addEventListener('change', function() {
    validateTimeInput(predictionMonthsInput, 1, 12);
  });

  // Bouton pour gérer les aspects futurs
  const manageFutureAspectsBtn = document.getElementById('manage-future-aspects-btn');
  const futureAspectsModal = document.getElementById('future-aspects-modal');
  const futureAspectsClose = document.getElementById('future-aspects-close');
  const saveFutureAspectsBtn = document.getElementById('save-future-aspects-btn');
  const cancelFutureAspectsBtn = document.getElementById('cancel-future-aspects-btn');
  const addCustomFutureAspectBtn = document.getElementById('add-custom-future-aspect-btn');
  const customFutureAspectsList = document.getElementById('custom-future-aspects-list');
  const selectedAspectsSummary = document.getElementById('selected-aspects-summary');

  // Initialiser le résumé des aspects sélectionnés
  if (selectedAspectsSummary) {
    updateSelectedAspectsSummary();
  }

  // Ouvrir la fenêtre modale des aspects futurs
  if (manageFutureAspectsBtn) {
    manageFutureAspectsBtn.addEventListener('click', function() {
      openFutureAspectsModal();
    });
  }

  // Fermer la fenêtre modale des aspects futurs
  if (futureAspectsClose) {
    futureAspectsClose.addEventListener('click', function() {
      futureAspectsModal.style.display = 'none';
    });
  }

  // Annuler les modifications
  if (cancelFutureAspectsBtn) {
    cancelFutureAspectsBtn.addEventListener('click', function() {
      futureAspectsModal.style.display = 'none';
    });
  }

  // Sauvegarder les aspects sélectionnés
  if (saveFutureAspectsBtn) {
    saveFutureAspectsBtn.addEventListener('click', function() {
      saveFutureAspects();
      futureAspectsModal.style.display = 'none';
    });
  }

  // Ajouter un aspect personnalisé
  if (addCustomFutureAspectBtn) {
    addCustomFutureAspectBtn.addEventListener('click', function() {
      addCustomFutureAspect();
    });
  }

  // Add event listener for CSV import
  importCsvBtn.addEventListener('click', function() {
    importEventsFromCSV();
  });

  // Add event listener for adding positions to all events
  addAllPositionsBtn.addEventListener('click', function() {
    addPositionsToAllEvents();
  });

  // Add event listener for toggling between views
  toggleViewBtn.addEventListener('click', function() {
    toggleEventView();
  });

  // Add event listener for exporting to CSV
  exportCsvBtn.addEventListener('click', function() {
    exportEventsToCSV();
  });

  // Add event listener for reset events button
  document.getElementById('reset-events-btn').addEventListener('click', function() {
    resetCalculatedEvents();
  });

  // Add event listener for toggle ruler mode button
  document.getElementById('toggle-ruler-mode-btn').addEventListener('click', function() {
    toggleRulerMode();
  });

  // Add event listener for calculating houses
  calculateHousesBtn.addEventListener('click', function() {
    calculateHouses();
  });

  // Initialiser la fenêtre modale d'interprétation
  const interpretationModal = document.getElementById('interpretation-modal');
  const interpretationModalClose = document.getElementById('interpretation-modal-close');

  if (interpretationModalClose) {
    interpretationModalClose.addEventListener('click', function() {
      interpretationModal.style.display = 'none';
    });
  }

  // Initialiser les contrôles de filtrage des aspects
  // Vérifier que les éléments existent avant d'initialiser
  if (document.getElementById('aspect-orb-filter') &&
      document.getElementById('aspect-orb-value') &&
      document.getElementById('apply-aspects-filter')) {
    initAspectsFilterControls();
  } else {
    console.log('Les éléments de filtrage des aspects ne sont pas encore disponibles');
  }

  // Fermer la fenêtre modale d'interprétation en cliquant à l'extérieur
  window.addEventListener('click', function(event) {
    if (event.target === interpretationModal) {
      interpretationModal.style.display = 'none';
    }
  });

  // Charger les interprétations transit-natal
  loadTransitNatalInterpretations().then(success => {
    if (success) {
      console.log('Interprétations transit-natal chargées avec succès');
    } else {
      console.error('Erreur lors du chargement des interprétations transit-natal');
    }
  });

  // Function to validate time inputs
  function validateTimeInput(input, min, max) {
    let value = parseInt(input.value);
    if (isNaN(value)) {
      value = 0;
    } else {
      value = Math.max(min, Math.min(max, value));
    }
    input.value = value;
  }

  // Resize handler for the canvas
  window.addEventListener('resize', function() {
    if (chartCanvas) {
      // Redraw the chart when the window is resized
      calculateChart();
    }
  });

  // Calculate and display the chart
  function calculateChart() {
    try {
      // Create date objects with time
      const birthDate = new Date(birthDateInput.value);
      birthDate.setHours(
        parseInt(birthHourInput.value) || 0,
        parseInt(birthMinuteInput.value) || 0,
        0
      );

      // Create transit date object with current time
      const transitDate = new Date();

      // Get seconds from the transit second input if it exists
      const transitSecondInput = document.getElementById('transit-second');
      const seconds = transitSecondInput ? parseInt(transitSecondInput.value) || 0 : 0;

      // Set seconds in the transit date
      transitDate.setSeconds(seconds);

      // Calculate planetary positions
      const birthPositions = calculatePlanetaryPositions(birthDate);
      const transitPositions = calculatePlanetaryPositions(transitDate);

      // Draw circular proportional zodiac
      drawCircularProportionalZodiac(birthPositions, transitPositions);

      // Calculate and display proportional aspects
      calculateAndDisplayProportionalAspects(birthPositions, transitPositions);

      // Store positions globally for other functions
      window.birthPositions = birthPositions;
      window.transitPositions = transitPositions;

      console.log('Chart calculated successfully');
    } catch (error) {
      console.error('Error calculating chart:', error);
    }
  }

  // Update chart title with date and time information
  function updateChartTitle(birthDate, transitDate) {
    const birthDateStr = formatDateTime(birthDate);
    const transitDateStr = formatDateTime(transitDate);

    const chartTitle = document.querySelector('.chart-section h2');
    if (chartTitle) {
      chartTitle.innerHTML = `Sidereal Chart<br><span class="chart-subtitle">Birth: ${birthDateStr} | Transit: ${transitDateStr}</span>`;
    }
  }

  // Format date and time for display
  function formatDateTime(date) {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
  }

  // Display planetary positions in the positions list
  function displayPlanetaryPositions(positions) {
    positionsList.innerHTML = '';

    const planets = [
      { key: 'sun', name: 'Sun', frenchName: 'Soleil' },
      { key: 'moon', name: 'Moon', frenchName: 'Lune' },
      { key: 'mercury', name: 'Mercury', frenchName: 'Mercure' },
      { key: 'venus', name: 'Venus', frenchName: 'Vénus' },
      { key: 'mars', name: 'Mars', frenchName: 'Mars' },
      { key: 'jupiter', name: 'Jupiter', frenchName: 'Jupiter' },
      { key: 'saturn', name: 'Saturn', frenchName: 'Saturne' },
      { key: 'uranus', name: 'Uranus', frenchName: 'Uranus' },
      { key: 'neptune', name: 'Neptune', frenchName: 'Neptune' }
    ];

    planets.forEach(planet => {
      const planetData = positions[planet.key];
      const planetItem = document.createElement('div');
      planetItem.className = 'planet-item';

      const planetSymbol = PLANET_SYMBOLS[planet.name] || '';

      planetItem.innerHTML = `
        <div class="planet-name">
          <span class="planet-symbol">${planetSymbol}</span>
          <span class="planet-french-name">${planet.frenchName}</span>
          <span class="planet-english-name">(${planet.name})</span>
        </div>
        <div class="planet-degree">
          <span class="sign">${planetData.sign}</span>
          ${planetData.degree}°
        </div>
      `;

      positionsList.appendChild(planetItem);
    });
  }

  // Display transits
  function displayTransits(transitPositions, aspects) {
    transitsList.innerHTML = '';
    aspectsList.innerHTML = '';

    // Show transit planetary positions
    const planets = [
      { key: 'sun', name: 'Sun', frenchName: 'Soleil' },
      { key: 'moon', name: 'Moon', frenchName: 'Lune' },
      { key: 'mercury', name: 'Mercury', frenchName: 'Mercure' },
      { key: 'venus', name: 'Venus', frenchName: 'Vénus' },
      { key: 'mars', name: 'Mars', frenchName: 'Mars' },
      { key: 'jupiter', name: 'Jupiter', frenchName: 'Jupiter' },
      { key: 'saturn', name: 'Saturn', frenchName: 'Saturne' },
      { key: 'uranus', name: 'Uranus', frenchName: 'Uranus' },
      { key: 'neptune', name: 'Neptune', frenchName: 'Neptune' }
    ];

    // Add transit positions
    planets.forEach(planet => {
      const planetData = transitPositions[planet.key];
      const transitItem = document.createElement('div');
      transitItem.className = 'planet-item';

      const planetSymbol = PLANET_SYMBOLS[planet.name] || '';

      transitItem.innerHTML = `
        <div class="planet-name">
          <span class="planet-symbol">${planetSymbol}</span>
          <span class="planet-french-name">${planet.frenchName}</span>
          <span class="planet-english-name">(${planet.name})</span>
        </div>
        <div class="planet-degree">
          <span class="sign">${planetData.sign}</span>
          ${planetData.degree}°
        </div>
      `;

      transitsList.appendChild(transitItem);
    });

    // Display aspects in the dedicated section
    displayAspects(aspects);
  }

  // Fonctions pour le filtrage des aspects
  function filterAspects(aspects) {
    return aspects.filter(aspect => {
      // Vérifier l'orb
      if (Math.abs(aspect.orb) > aspectFilters.maxOrb) {
        return false;
      }

      // Vérifier le type d'aspect
      const aspectType = aspect.aspect.toLowerCase();
      return aspectFilters.types.includes(aspectType);
    });
  }

  // Display aspects in the dedicated section
  function displayAspects(aspects) {
    // Vérifier si aspects est défini
    if (!aspects) {
      console.error('Aspects is undefined in displayAspects');
      return;
    }

    // Stocker tous les aspects calculés
    allCalculatedAspects = aspects;

    // Vérifier si la fonction filterAspects est définie
    let filteredAspects = aspects;
    try {
      // Appliquer les filtres si la fonction est disponible
      filteredAspects = filterAspects(aspects);
    } catch (error) {
      console.error('Error filtering aspects:', error);
    }

    // Vérifier si aspectsList est défini
    const aspectsList = document.getElementById('aspects-list');
    if (!aspectsList) {
      console.error('aspects-list element not found');
      return;
    }

    // Clear previous aspects
    aspectsList.innerHTML = '';

    // Add aspects
    if (filteredAspects && filteredAspects.length > 0) {
      filteredAspects.forEach(aspect => {
        const aspectItem = document.createElement('div');
        aspectItem.className = 'aspect-item';

        const birthPlanetName = aspect.birthPlanet.charAt(0).toUpperCase() + aspect.birthPlanet.slice(1);
        const transitPlanetName = aspect.transitPlanet.charAt(0).toUpperCase() + aspect.transitPlanet.slice(1);

        // Créer le contenu de l'aspect
        const aspectNameDiv = document.createElement('div');
        aspectNameDiv.textContent = `${birthPlanetName} ${aspect.aspect} ${transitPlanetName}`;

        const aspectOrbDiv = document.createElement('div');
        aspectOrbDiv.textContent = `Orb: ${aspect.orb}°`;

        // Créer le bouton d'interprétation
        const interpretButton = document.createElement('button');
        interpretButton.className = 'interpret-aspect-btn';
        interpretButton.textContent = 'Interprétation';
        interpretButton.addEventListener('click', () => {
          // Afficher l'interprétation
          showTransitNatalInterpretation(
            aspect.transitPlanet.toLowerCase(),
            aspect.aspect.toLowerCase(),
            aspect.birthPlanet.toLowerCase()
          );
        });

        // Ajouter les éléments à l'aspect
        aspectItem.appendChild(aspectNameDiv);
        aspectItem.appendChild(aspectOrbDiv);
        aspectItem.appendChild(interpretButton);

        aspectsList.appendChild(aspectItem);
      });
    } else {
      const noAspectsItem = document.createElement('div');
      noAspectsItem.className = 'aspect-item';
      noAspectsItem.textContent = 'Aucun aspect correspondant aux filtres.';
      aspectsList.appendChild(noAspectsItem);
    }

    // Indiquer que l'affichage a été mis à jour
    console.log('Aspects display updated with', filteredAspects ? filteredAspects.length : 0, 'aspects');
  }

  // Fonction pour mettre à jour l'affichage des aspects avec les filtres actuels
  function updateAspectsDisplay() {
    if (allCalculatedAspects && allCalculatedAspects.length > 0) {
      displayAspects(allCalculatedAspects);
    } else {
      console.log('No aspects to display or allCalculatedAspects is not initialized');
    }
  }

  // Initialiser les contrôles de filtrage des aspects
  function initAspectsFilterControls() {
    const orbFilter = document.getElementById('aspect-orb-filter');
    const orbValue = document.getElementById('aspect-orb-value');
    const applyFilterBtn = document.getElementById('apply-aspects-filter');
    const aspectTypeCheckboxes = document.querySelectorAll('input[name="aspect-type"]');

    // Mettre à jour l'affichage de la valeur de l'orb
    orbFilter.addEventListener('input', function() {
      orbValue.textContent = this.value + '°';
    });

    // Appliquer les filtres
    applyFilterBtn.addEventListener('click', function() {
      // Mettre à jour la valeur maximale de l'orb
      aspectFilters.maxOrb = parseFloat(orbFilter.value);

      // Mettre à jour les types d'aspects sélectionnés
      aspectFilters.types = [];
      aspectTypeCheckboxes.forEach(checkbox => {
        if (checkbox.checked) {
          aspectFilters.types.push(checkbox.value);
        }
      });

      // Mettre à jour l'affichage des aspects
      updateAspectsDisplay();
    });

    // Initialiser les valeurs par défaut
    orbFilter.value = aspectFilters.maxOrb;
    orbValue.textContent = aspectFilters.maxOrb + '°';

    // Cocher les cases à cocher correspondant aux types d'aspects par défaut
    aspectTypeCheckboxes.forEach(checkbox => {
      checkbox.checked = aspectFilters.types.includes(checkbox.value);
    });
  }

  // Draw the Swiss Astro Watch
  function drawSwissAstroWatch(transitPositions) {
    const swissWatchCanvas = document.getElementById('swiss-watch-canvas');
    if (!swissWatchCanvas) return;

    const ctx = swissWatchCanvas.getContext('2d');
    const width = swissWatchCanvas.width = swissWatchCanvas.offsetWidth;
    const height = swissWatchCanvas.height = 500;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) - 40;

    // Rayon pour le cercle extérieur des planètes natales (360°)
    const outerRadius = radius + 25;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw watch title
    ctx.font = 'bold 16px ' + getComputedStyle(document.body).fontFamily;
    ctx.fillStyle = '#333';
    ctx.textAlign = 'center';
    ctx.fillText('Montre Astro Suisse', centerX, 20);

    // Draw outer circle for natal planets (360° circle)
    ctx.beginPath();
    ctx.arc(centerX, centerY, outerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw outer circle background
    ctx.beginPath();
    ctx.arc(centerX, centerY, outerRadius - 2, 0, 2 * Math.PI);
    ctx.fillStyle = 'rgba(240, 240, 255, 0.3)';
    ctx.fill();

    // Dessiner des lignes de séparation entre les signes du zodiaque
    for (let i = 0; i < 12; i++) {
      const angle = ((i * 30) - 90) * Math.PI / 180;

      // Dessiner une ligne du centre vers le bord du cercle
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(
        centerX + outerRadius * Math.cos(angle),
        centerY + outerRadius * Math.sin(angle)
      );
      ctx.strokeStyle = 'rgba(100, 100, 100, 0.2)';
      ctx.lineWidth = 1;
      ctx.stroke();
    }

    // Draw degree markers on the outer circle (every 30°)
    for (let i = 0; i < 12; i++) {
      // Ajuster l'angle pour que 0° soit à l'est (3 heures) et rotation anti-horaire
      // Décaler de 90° pour aligner correctement (0° Bélier à l'est)
      const angle = ((i * 30) - 90) * Math.PI / 180;

      // Draw major markers (sign boundaries)
      ctx.beginPath();
      ctx.moveTo(
        centerX + (outerRadius - 15) * Math.cos(angle),
        centerY + (outerRadius - 15) * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + outerRadius * Math.cos(angle),
        centerY + outerRadius * Math.sin(angle)
      );
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Add zodiac sign at each 30° position
      // Ordre correct des signes: Aries, Taurus, Gemini, etc.
      const signIndex = i;
      const sign = ZODIAC_SIGNS[signIndex];

      // Calculer l'angle du milieu du signe pour centrer le texte
      const middleAngle = (((i * 30) + 15) - 90) * Math.PI / 180;
      const textRadius = outerRadius - 8;
      const textX = centerX + textRadius * Math.cos(middleAngle);
      const textY = centerY + textRadius * Math.sin(middleAngle);

      ctx.font = 'bold 10px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(sign, textX, textY);

      // Ajouter un petit indicateur de degré (0°, 30°, 60°, etc.)
      const degreeText = (i * 30) + "°";
      const degreeRadius = outerRadius - 20;
      const degreeX = centerX + degreeRadius * Math.cos(angle);
      const degreeY = centerY + degreeRadius * Math.sin(angle);

      ctx.font = '8px Arial';
      ctx.fillStyle = '#666';
      ctx.fillText(degreeText, degreeX, degreeY);
    }

    // Draw outer circle (watch case)
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Draw watch face
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius - 5, 0, 2 * Math.PI);
    ctx.fillStyle = '#f5f5f5';
    ctx.fill();
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw hour markers
    drawHourMarkers(ctx, centerX, centerY, radius);

    // Draw minute markers
    drawMinuteMarkers(ctx, centerX, centerY, radius);

    // Draw second markers
    drawSecondMarkers(ctx, centerX, centerY, radius);

    // Utiliser la date fournie dans transitPositions pour les calculs
    // S'assurer que c'est un objet Date valide
    const transitDate = transitPositions.date instanceof Date ? transitPositions.date : new Date();

    // Calculate ascendant and descendant for transit
    const latitude = parseFloat(latitudeInput.value) || 48.0667;
    const longitude = parseFloat(longitudeInput.value) || -0.7667;
    const lst = calculateLocalSiderealTime(transitDate, longitude);
    let ascendantTransit = calculateAscendant(lst, latitude);
    const ayanamsa = getAyanamsa(transitDate);
    ascendantTransit = normalizeAngle(ascendantTransit - ayanamsa);
    const descendantTransit = normalizeAngle(ascendantTransit + 180);

    // Get Moon position in transit
    const moonPosition = transitPositions.moon;
    if (!moonPosition) return;

    // Get Moon's longitude (convert from sign and degree to absolute longitude)
    const moonSignIndex = ZODIAC_SIGNS.indexOf(moonPosition.sign);
    const moonDegree = moonPosition.degree;
    const moonLongitude = moonSignIndex * 30 + moonDegree;

    // Draw natal planets in the hour circle (30° mode)
    drawNatalPlanetsInHourCircle(ctx, centerX, centerY, radius);

    // Draw hour hands (Moon and angles in degrees of sign - 30° circle)
    drawHourHand(ctx, centerX, centerY, radius, moonLongitude % 30, '#888', 'Lune');
    drawHourHand(ctx, centerX, centerY, radius, ascendantTransit % 30, '#FF9900', 'ASC-T', 0.85);
    drawHourHand(ctx, centerX, centerY, radius, descendantTransit % 30, '#00CC66', 'DESC-T', 0.7);

    // Calculate minutes of arc with high precision
    const moonMinutes = (moonDegree % 1) * 60;
    const ascMinutes = (ascendantTransit % 1) * 60;
    const descMinutes = (descendantTransit % 1) * 60;

    // Draw minute hands (Moon and angles in minutes of arc)
    drawMinuteHand(ctx, centerX, centerY, radius, moonMinutes, '#888', 'Lune');
    drawMinuteHand(ctx, centerX, centerY, radius, ascMinutes, '#FF9900', 'ASC-T', 0.85);
    drawMinuteHand(ctx, centerX, centerY, radius, descMinutes, '#00CC66', 'DESC-T', 0.7);

    // Calculate seconds of arc with high precision
    // Pour la Lune, utiliser les millisecondes pour un mouvement plus fluide
    const moonMinutesWithFraction = (moonDegree % 1) * 60;
    const moonSeconds = (moonMinutesWithFraction % 1) * 60;

    // Pour l'ascendant et le descendant, calculer les secondes d'arc
    const ascMinutesWithFraction = (ascendantTransit % 1) * 60;
    const ascSeconds = (ascMinutesWithFraction % 1) * 60;

    const descMinutesWithFraction = (descendantTransit % 1) * 60;
    const descSeconds = (descMinutesWithFraction % 1) * 60;

    // Draw second hands for all elements
    drawSecondHand(ctx, centerX, centerY, radius, moonSeconds, '#888', 'Lune');
    drawSecondHand(ctx, centerX, centerY, radius, ascSeconds, '#FF9900', 'ASC-T', 0.85);
    drawSecondHand(ctx, centerX, centerY, radius, descSeconds, '#00CC66', 'DESC-T', 0.7);

    // Draw center cap
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.05, 0, 2 * Math.PI);
    ctx.fillStyle = '#333';
    ctx.fill();

    // Draw natal planets on the outer circle
    drawNatalPlanetsOnOuterCircle(ctx, centerX, centerY, outerRadius);

    // Draw side circles in separate canvas
    drawSideCircles(transitPositions);
  }

  // Function to draw side circles in separate canvas
  function drawSideCircles(transitPositions) {
    // Draw 360° mode circle
    const canvas360 = document.getElementById('transit-360-canvas');
    if (canvas360) {
      const ctx360 = canvas360.getContext('2d');
      const width360 = canvas360.width = canvas360.offsetWidth;
      const height360 = canvas360.height = 240;
      const centerX360 = width360 / 2;
      const centerY360 = height360 / 2;
      const radius360 = Math.min(centerX360, centerY360) - 20;

      ctx360.clearRect(0, 0, width360, height360);
      drawTransitPlanetsIn360Mode(ctx360, centerX360, centerY360, radius360, transitPositions);
    }

    // Draw 30° mode circle
    const canvas30 = document.getElementById('transit-30-canvas');
    if (canvas30) {
      const ctx30 = canvas30.getContext('2d');
      const width30 = canvas30.width = canvas30.offsetWidth;
      const height30 = canvas30.height = 240;
      const centerX30 = width30 / 2;
      const centerY30 = height30 / 2;
      const radius30 = Math.min(centerX30, centerY30) - 20;

      ctx30.clearRect(0, 0, width30, height30);
      drawTransitPlanetsIn30Mode(ctx30, centerX30, centerY30, radius30, transitPositions);
    }
  }

  // Function to draw natal planets on the outer 360° circle
  function drawNatalPlanetsOnOuterCircle(ctx, centerX, centerY, radius) {
    // Définir les positions des planètes natales (données fournies par l'utilisateur)
    const natalPlanets = [
      { name: 'Sun', symbol: '☉', sign: 'Sgr', degrees: 1, minutes: 3, seconds: 30.31, color: '#FFB900' },
      { name: 'Moon', symbol: '☽', sign: 'Ari', degrees: 5, minutes: 17, seconds: 0.71, color: '#C0C0C0' },
      { name: 'Mercury', symbol: '☿', sign: 'Sco', degrees: 14, minutes: 16, seconds: 53.09, color: '#A9A9A9' },
      { name: 'Venus', symbol: '♀', sign: 'Lib', degrees: 19, minutes: 5, seconds: 9.00, color: '#00FF7F' },
      { name: 'Mars', symbol: '♂', sign: 'Sco', degrees: 19, minutes: 17, seconds: 13.46, color: '#FF4500' },
      { name: 'Jupiter', symbol: '♃', sign: 'Leo', degrees: 20, minutes: 34, seconds: 56.21, color: '#4169E1' },
      { name: 'Saturn', symbol: '♄', sign: 'Cap', degrees: 10, minutes: 32, seconds: 5.62, color: '#708090' }
    ];

    // Convertir les positions en degrés absolus (0-360°)
    natalPlanets.forEach(planet => {
      // Convertir le signe en index (0-11)
      let signIndex;
      switch(planet.sign) {
        case 'Ari': signIndex = 0; break;
        case 'Tau': signIndex = 1; break;
        case 'Gem': signIndex = 2; break;
        case 'Can': signIndex = 3; break;
        case 'Leo': signIndex = 4; break;
        case 'Vir': signIndex = 5; break;
        case 'Lib': signIndex = 6; break;
        case 'Sco': signIndex = 7; break;
        case 'Sgr': signIndex = 8; break;
        case 'Cap': signIndex = 9; break;
        case 'Aqu': signIndex = 10; break;
        case 'Pis': signIndex = 11; break;
        default: signIndex = 0;
      }

      // Calculer la position absolue en degrés (0-360°)
      const absoluteDegrees = signIndex * 30 + planet.degrees + planet.minutes / 60 + planet.seconds / 3600;

      // Convertir en angle pour le dessin (0° est à l'est, rotation anti-horaire)
      // Décaler de 90° pour aligner correctement (0° Bélier à l'est)
      const angle = ((absoluteDegrees - 90) * Math.PI / 180);

      // Dessiner un cercle pour représenter la planète
      ctx.beginPath();
      const planetX = centerX + (radius - 10) * Math.cos(angle);
      const planetY = centerY + (radius - 10) * Math.sin(angle);
      ctx.arc(planetX, planetY, 8, 0, 2 * Math.PI);
      ctx.fillStyle = planet.color;
      ctx.fill();
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Dessiner le symbole de la planète
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = '#fff';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(planet.symbol, planetX, planetY);

      // Dessiner une ligne du centre vers la position de la planète
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(planetX, planetY);
      ctx.strokeStyle = planet.color;
      ctx.lineWidth = 0.5;
      ctx.stroke();

      // Ajouter une étiquette avec le nom de la planète et sa position
      const labelRadius = radius + 15;
      const labelX = centerX + labelRadius * Math.cos(angle);
      const labelY = centerY + labelRadius * Math.sin(angle);

      // Formater la position (degrés°minutes'secondes")
      const positionText = `${planet.degrees}°${planet.minutes}'${planet.seconds.toFixed(0)}"`;
      const labelText = `${planet.name} ${planet.sign} ${positionText}`; // Ajouter le signe dans l'étiquette

      // Dessiner un fond pour l'étiquette
      ctx.font = '9px Arial';
      const textWidth = ctx.measureText(labelText).width;
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.fillRect(labelX - textWidth/2 - 3, labelY - 6, textWidth + 6, 12);

      // Dessiner le texte de l'étiquette
      ctx.fillStyle = planet.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(labelText, labelX, labelY);

      // Ajouter un petit indicateur de position exacte sur le cercle
      const markerSize = 3;
      ctx.beginPath();
      ctx.arc(planetX, planetY + 12, markerSize, 0, 2 * Math.PI);
      ctx.fillStyle = planet.color;
      ctx.fill();
    });
  }

  // Draw hour markers on the watch
  function drawHourMarkers(ctx, centerX, centerY, radius) {
    for (let i = 0; i < 12; i++) {
      // Ajuster l'angle pour que 0° soit à l'est (3 heures) et rotation anti-horaire
      // Décaler de 90° pour aligner correctement (0° Bélier à l'est)
      const angle = ((i * 30) - 90) * Math.PI / 180;
      const innerRadius = radius - 15;
      const outerRadius = radius - 5;

      ctx.beginPath();
      ctx.moveTo(
        centerX + innerRadius * Math.cos(angle),
        centerY + innerRadius * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + outerRadius * Math.cos(angle),
        centerY + outerRadius * Math.sin(angle)
      );
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 3;
      ctx.stroke();

      // Add zodiac sign at each hour position
      const signIndex = i;
      const sign = ZODIAC_SIGNS[signIndex];
      const textRadius = radius - 25;
      const textX = centerX + textRadius * Math.cos(angle);
      const textY = centerY + textRadius * Math.sin(angle);

      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(sign, textX, textY);

      // Add degree markers (0°, 10°, 20°)
      for (let j = 1; j <= 2; j++) {
        // Ajuster l'angle pour que 0° soit à l'est (3 heures) et rotation anti-horaire
        const degreeAngle = ((i * 30 + j * 10) - 90) * Math.PI / 180;
        const innerDegreeRadius = radius - 12;
        const outerDegreeRadius = radius - 5;

        ctx.beginPath();
        ctx.moveTo(
          centerX + innerDegreeRadius * Math.cos(degreeAngle),
          centerY + innerDegreeRadius * Math.sin(degreeAngle)
        );
        ctx.lineTo(
          centerX + outerDegreeRadius * Math.cos(degreeAngle),
          centerY + outerDegreeRadius * Math.sin(degreeAngle)
        );
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 1.5;
        ctx.stroke();
      }
    }
  }

  // Draw minute markers on the watch (0-60 minutes of arc)
  function drawMinuteMarkers(ctx, centerX, centerY, radius) {
    const minuteRadius = radius * 0.75;

    // Draw minute circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, minuteRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#999';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw minute markers
    for (let i = 0; i < 60; i++) {
      const angle = (i * 6) * Math.PI / 180;
      const isMultipleOf5 = i % 5 === 0;
      const innerRadius = isMultipleOf5 ? minuteRadius - 8 : minuteRadius - 5;

      ctx.beginPath();
      ctx.moveTo(
        centerX + innerRadius * Math.cos(angle),
        centerY + innerRadius * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + minuteRadius * Math.cos(angle),
        centerY + minuteRadius * Math.sin(angle)
      );
      ctx.strokeStyle = '#666';
      ctx.lineWidth = isMultipleOf5 ? 1.5 : 0.5;
      ctx.stroke();

      // Add minute numbers at multiples of 5
      if (isMultipleOf5) {
        const textRadius = minuteRadius - 15;
        const textX = centerX + textRadius * Math.cos(angle);
        const textY = centerY + textRadius * Math.sin(angle);

        ctx.font = '10px Arial';
        ctx.fillStyle = '#666';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(i.toString(), textX, textY);
      }
    }
  }

  // Draw second markers on the watch (0-60 seconds of arc)
  function drawSecondMarkers(ctx, centerX, centerY, radius) {
    const secondRadius = radius * 0.5;

    // Draw second circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, secondRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#999';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw second markers
    for (let i = 0; i < 60; i++) {
      const angle = (i * 6) * Math.PI / 180;
      const isMultipleOf5 = i % 5 === 0;
      const innerRadius = isMultipleOf5 ? secondRadius - 6 : secondRadius - 3;

      ctx.beginPath();
      ctx.moveTo(
        centerX + innerRadius * Math.cos(angle),
        centerY + innerRadius * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + secondRadius * Math.cos(angle),
        centerY + secondRadius * Math.sin(angle)
      );
      ctx.strokeStyle = '#999';
      ctx.lineWidth = isMultipleOf5 ? 1 : 0.5;
      ctx.stroke();

      // Add second numbers at multiples of 10
      if (i % 10 === 0) {
        const textRadius = secondRadius - 12;
        const textX = centerX + textRadius * Math.cos(angle);
        const textY = centerY + textRadius * Math.sin(angle);

        ctx.font = '8px Arial';
        ctx.fillStyle = '#999';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(i.toString(), textX, textY);
      }
    }
  }

  // Draw hour hand (degrees of sign - 30° circle)
  function drawHourHand(ctx, centerX, centerY, radius, degrees, color, label, lengthFactor = 1) {
    // Convert degrees to angle (30 degrees = 360 degrees on the watch)
    // Ajuster l'angle pour que 0° soit à l'est (3 heures) et rotation anti-horaire
    const angle = ((degrees * 12) - 90) * Math.PI / 180;
    const handLength = radius * 0.6 * lengthFactor;

    // Draw hand with shadow for better visibility
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 4;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 2;

    // Draw hand with arrow tip
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    const endX = centerX + handLength * Math.cos(angle);
    const endY = centerY + handLength * Math.sin(angle);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = color;
    ctx.lineWidth = 4;
    ctx.stroke();

    // Draw arrow tip
    const arrowSize = 8;
    const arrowAngle = Math.PI / 8; // 22.5 degrees

    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - arrowSize * Math.cos(angle - arrowAngle),
      endY - arrowSize * Math.sin(angle - arrowAngle)
    );
    ctx.lineTo(
      endX - arrowSize * Math.cos(angle + arrowAngle),
      endY - arrowSize * Math.sin(angle + arrowAngle)
    );
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // Add small label near the end of the hand
    const labelRadius = handLength + 15;
    const labelX = centerX + labelRadius * Math.cos(angle);
    const labelY = centerY + labelRadius * Math.sin(angle);

    // Draw label with background for better visibility
    const labelText = `${label} ${degrees.toFixed(2)}°`;
    ctx.font = 'bold 10px Arial';
    const textWidth = ctx.measureText(labelText).width;

    // Draw background
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillRect(labelX - textWidth/2 - 3, labelY - 8, textWidth + 6, 16);

    // Draw text
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(labelText, labelX, labelY);
  }

  // Draw minute hand (minutes of arc - 60 minute circle)
  function drawMinuteHand(ctx, centerX, centerY, radius, minutes, color, label, lengthFactor = 1) {
    // Convert minutes to angle (60 minutes = 360 degrees on the watch)
    const angle = ((minutes * 6) - 90) * Math.PI / 180;
    const handLength = radius * 0.75 * 0.9 * lengthFactor;

    // Draw hand with shadow for better visibility
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 3;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    // Draw hand with arrow tip
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    const endX = centerX + handLength * Math.cos(angle);
    const endY = centerY + handLength * Math.sin(angle);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.stroke();

    // Draw arrow tip
    const arrowSize = 6;
    const arrowAngle = Math.PI / 8; // 22.5 degrees

    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - arrowSize * Math.cos(angle - arrowAngle),
      endY - arrowSize * Math.sin(angle - arrowAngle)
    );
    ctx.lineTo(
      endX - arrowSize * Math.cos(angle + arrowAngle),
      endY - arrowSize * Math.sin(angle + arrowAngle)
    );
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // Add small label near the end of the hand
    if (label) {
      // Calculate offset angle based on label to avoid overlap
      const labelOffset = (label === 'ASC-T' ? 20 : (label === 'DESC-T' ? -20 : 0));
      const labelAngle = ((minutes * 6) - 90 + labelOffset) * Math.PI / 180;
      const labelRadius = radius * 0.75 * 0.7;
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      // Draw label with background for better visibility
      const labelText = `${minutes.toFixed(2)}'`;
      ctx.font = 'bold 9px Arial';
      const textWidth = ctx.measureText(labelText).width;

      // Draw background
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.fillRect(labelX - textWidth/2 - 2, labelY - 7, textWidth + 4, 14);

      // Draw text
      ctx.fillStyle = color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(labelText, labelX, labelY);
    }
  }

  // Draw second hand (seconds of arc - 60 second circle)
  function drawSecondHand(ctx, centerX, centerY, radius, seconds, color, label = null, lengthFactor = 1) {
    // Convert seconds to angle (60 seconds = 360 degrees on the watch)
    const angle = ((seconds * 6) - 90) * Math.PI / 180;
    const handLength = radius * 0.5 * 0.9 * lengthFactor;

    // Draw hand with shadow for better visibility
    ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
    ctx.shadowBlur = 2;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    // Draw hand with arrow tip
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    const endX = centerX + handLength * Math.cos(angle);
    const endY = centerY + handLength * Math.sin(angle);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw arrow tip
    const arrowSize = 4;
    const arrowAngle = Math.PI / 8; // 22.5 degrees

    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - arrowSize * Math.cos(angle - arrowAngle),
      endY - arrowSize * Math.sin(angle - arrowAngle)
    );
    ctx.lineTo(
      endX - arrowSize * Math.cos(angle + arrowAngle),
      endY - arrowSize * Math.sin(angle + arrowAngle)
    );
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // Add small label near the end of the hand
    if (label) {
      // Calculate offset angle based on label to avoid overlap
      const labelOffset = (label === 'ASC-T' ? 20 : (label === 'DESC-T' ? -20 : 0));
      const labelAngle = ((seconds * 6) - 90 + labelOffset) * Math.PI / 180;
      const labelRadius = radius * 0.5 * 0.7;
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      // Draw label with background for better visibility
      const labelText = `${seconds.toFixed(2)}"`;
      ctx.font = 'bold 8px Arial';
      const textWidth = ctx.measureText(labelText).width;

      // Draw background
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.fillRect(labelX - textWidth/2 - 2, labelY - 6, textWidth + 4, 12);

      // Draw text
      ctx.fillStyle = color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(labelText, labelX, labelY);
    }
  }

  // Function to draw transit planets in 30° mode
  function drawTransitPlanetsIn30Mode(ctx, centerX, centerY, radius, transitPositions) {

    // Dessiner le cercle extérieur avec un contour plus visible
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Dessiner le fond du cercle avec une couleur plus visible
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius - 1, 0, 2 * Math.PI);
    ctx.fillStyle = '#e6f7ff'; // Couleur de fond légèrement bleutée pour plus de visibilité
    ctx.fill();

    // Dessiner les marqueurs des signes (tous les 30°)
    for (let i = 0; i < 12; i++) {
      // Ajuster l'angle pour que 0° soit à l'est (3 heures) et rotation anti-horaire
      const angle = ((i * 30) - 90) * Math.PI / 180;

      // Dessiner les marqueurs
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle)
      );
      ctx.strokeStyle = 'rgba(100, 100, 100, 0.2)';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Ajouter les noms des signes
      const middleAngle = (((i * 30) + 15) - 90) * Math.PI / 180;
      const textRadius = radius - 10;
      const textX = centerX + textRadius * Math.cos(middleAngle);
      const textY = centerY + textRadius * Math.sin(middleAngle);

      ctx.font = 'bold 8px Arial';
      ctx.fillStyle = '#666';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(ZODIAC_SIGNS[i], textX, textY);
    }

    // Dessiner les planètes de transit
    const planets = [
      { key: 'sun', name: 'Sun', symbol: '☉', color: '#FFB900' },
      { key: 'moon', name: 'Moon', symbol: '☽', color: '#C0C0C0' },
      { key: 'mercury', name: 'Mercury', symbol: '☿', color: '#A9A9A9' },
      { key: 'venus', name: 'Venus', symbol: '♀', color: '#00FF7F' },
      { key: 'mars', name: 'Mars', symbol: '♂', color: '#FF4500' },
      { key: 'jupiter', name: 'Jupiter', symbol: '♃', color: '#4169E1' },
      { key: 'saturn', name: 'Saturn', symbol: '♄', color: '#708090' },
      { key: 'uranus', name: 'Uranus', symbol: '♅', color: '#40E0D0' },
      { key: 'neptune', name: 'Neptune', symbol: '♆', color: '#9370DB' }
    ];

    planets.forEach(planet => {
      const planetData = transitPositions[planet.key];
      if (!planetData) return;

      // Calculer la position dans le signe (0-30°)
      const degreesInSign = planetData.degree % 30;

      // Convertir en angle pour le dessin (30° = 360° sur le cercle)
      const angle = ((degreesInSign * 12) - 90) * Math.PI / 180;

      // Calculer la position sur le cercle
      const planetRadius = radius * 0.7;
      const planetX = centerX + planetRadius * Math.cos(angle);
      const planetY = centerY + planetRadius * Math.sin(angle);

      // Dessiner un cercle plus grand et plus visible pour la planète
      ctx.beginPath();
      ctx.arc(planetX, planetY, 8, 0, 2 * Math.PI);
      ctx.fillStyle = planet.color || '#4285f4'; // Couleur par défaut si non définie
      ctx.fill();
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 1.5;
      ctx.stroke();

      // Dessiner le symbole de la planète avec une police plus grande
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = '#fff';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(planet.symbol, planetX, planetY);

      // Ajouter une étiquette avec le nom de la planète et sa position
      const labelRadius = planetRadius - 15;
      const labelAngle = angle + 0.1;
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      // Créer l'étiquette
      const labelText = `${planet.name} ${planetData.sign} ${planetData.degree.toFixed(1)}°`;

      // Dessiner un fond pour l'étiquette
      ctx.font = '7px Arial';
      const textWidth = ctx.measureText(labelText).width;
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.fillRect(labelX - textWidth/2 - 2, labelY - 5, textWidth + 4, 10);

      // Dessiner le texte
      ctx.fillStyle = planet.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(labelText, labelX, labelY);
    });
  }

  // Function to draw transit planets in 360° mode
  function drawTransitPlanetsIn360Mode(ctx, centerX, centerY, radius, transitPositions) {

    // Dessiner le cercle extérieur avec un contour plus visible
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Dessiner le fond du cercle avec une couleur plus visible
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius - 1, 0, 2 * Math.PI);
    ctx.fillStyle = '#fff0e6'; // Couleur de fond légèrement orangée pour différencier du cercle 30°
    ctx.fill();

    // Dessiner les marqueurs des signes (tous les 30°)
    for (let i = 0; i < 12; i++) {
      // Ajuster l'angle pour que 0° soit à l'est (3 heures) et rotation anti-horaire
      const angle = ((i * 30) - 90) * Math.PI / 180;

      // Dessiner les marqueurs
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle)
      );
      ctx.strokeStyle = 'rgba(100, 100, 100, 0.2)';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Ajouter les noms des signes
      const middleAngle = (((i * 30) + 15) - 90) * Math.PI / 180;
      const textRadius = radius - 10;
      const textX = centerX + textRadius * Math.cos(middleAngle);
      const textY = centerY + textRadius * Math.sin(middleAngle);

      ctx.font = 'bold 8px Arial';
      ctx.fillStyle = '#666';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(ZODIAC_SIGNS[i], textX, textY);
    }

    // Dessiner les planètes de transit
    const planets = [
      { key: 'sun', name: 'Sun', symbol: '☉', color: '#FFB900' },
      { key: 'moon', name: 'Moon', symbol: '☽', color: '#C0C0C0' },
      { key: 'mercury', name: 'Mercury', symbol: '☿', color: '#A9A9A9' },
      { key: 'venus', name: 'Venus', symbol: '♀', color: '#00FF7F' },
      { key: 'mars', name: 'Mars', symbol: '♂', color: '#FF4500' },
      { key: 'jupiter', name: 'Jupiter', symbol: '♃', color: '#4169E1' },
      { key: 'saturn', name: 'Saturn', symbol: '♄', color: '#708090' },
      { key: 'uranus', name: 'Uranus', symbol: '♅', color: '#40E0D0' },
      { key: 'neptune', name: 'Neptune', symbol: '♆', color: '#9370DB' }
    ];

    planets.forEach(planet => {
      const planetData = transitPositions[planet.key];
      if (!planetData) return;

      // Calculer la position absolue (0-360°)
      const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);
      const absoluteDegrees = signIndex * 30 + planetData.degree;

      // Convertir en angle pour le dessin (0° est à l'est, rotation anti-horaire)
      const angle = ((absoluteDegrees - 90) * Math.PI / 180);

      // Calculer la position sur le cercle
      const planetRadius = radius * 0.7;
      const planetX = centerX + planetRadius * Math.cos(angle);
      const planetY = centerY + planetRadius * Math.sin(angle);

      // Dessiner un cercle plus grand et plus visible pour la planète
      ctx.beginPath();
      ctx.arc(planetX, planetY, 8, 0, 2 * Math.PI);
      ctx.fillStyle = planet.color || '#ff7043'; // Couleur par défaut orangée si non définie
      ctx.fill();
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 1.5;
      ctx.stroke();

      // Dessiner le symbole de la planète avec une police plus grande
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = '#fff';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(planet.symbol, planetX, planetY);

      // Ajouter une étiquette avec le nom de la planète et sa position
      const labelRadius = planetRadius - 15;
      const labelAngle = angle + 0.1;
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      // Créer l'étiquette
      const labelText = `${planet.name} ${absoluteDegrees.toFixed(1)}°`;

      // Dessiner un fond pour l'étiquette
      ctx.font = '7px Arial';
      const textWidth = ctx.measureText(labelText).width;
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.fillRect(labelX - textWidth/2 - 2, labelY - 5, textWidth + 4, 10);

      // Dessiner le texte
      ctx.fillStyle = planet.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(labelText, labelX, labelY);
    });
  }

  // Function to draw natal planets in the hour circle (30° mode)
  function drawNatalPlanetsInHourCircle(ctx, centerX, centerY, radius) {
    // Définir les positions des planètes natales (données fournies par l'utilisateur)
    const natalPlanets = [
      { name: 'Sun', symbol: '☉', sign: 'Sgr', degrees: 1, minutes: 3, seconds: 30.31, color: '#FFB900' },
      { name: 'Moon', symbol: '☽', sign: 'Ari', degrees: 5, minutes: 17, seconds: 0.71, color: '#C0C0C0' },
      { name: 'Mercury', symbol: '☿', sign: 'Sco', degrees: 14, minutes: 16, seconds: 53.09, color: '#A9A9A9' },
      { name: 'Venus', symbol: '♀', sign: 'Lib', degrees: 19, minutes: 5, seconds: 9.00, color: '#00FF7F' },
      { name: 'Mars', symbol: '♂', sign: 'Sco', degrees: 19, minutes: 17, seconds: 13.46, color: '#FF4500' },
      { name: 'Jupiter', symbol: '♃', sign: 'Leo', degrees: 20, minutes: 34, seconds: 56.21, color: '#4169E1' },
      { name: 'Saturn', symbol: '♄', sign: 'Cap', degrees: 10, minutes: 32, seconds: 5.62, color: '#708090' }
    ];

    // Pour chaque planète, dessiner un point sur le cercle des heures (30°)
    natalPlanets.forEach(planet => {
      // Utiliser uniquement les degrés dans le signe (0-30°)
      const degreesInSign = planet.degrees + planet.minutes / 60 + planet.seconds / 3600;

      // Convertir en angle pour le dessin (30° = 360° sur la montre)
      // Ajuster l'angle pour que 0° soit à l'est (3 heures) et rotation anti-horaire
      const angle = ((degreesInSign * 12) - 90) * Math.PI / 180;

      // Calculer la position sur le cercle des heures
      const planetRadius = radius * 0.85; // Légèrement à l'intérieur du cercle des heures
      const planetX = centerX + planetRadius * Math.cos(angle);
      const planetY = centerY + planetRadius * Math.sin(angle);

      // Dessiner un petit cercle pour représenter la planète
      ctx.beginPath();
      ctx.arc(planetX, planetY, 5, 0, 2 * Math.PI);
      ctx.fillStyle = planet.color;
      ctx.fill();
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 0.5;
      ctx.stroke();

      // Dessiner le symbole de la planète
      ctx.font = 'bold 8px Arial';
      ctx.fillStyle = '#fff';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(planet.symbol, planetX, planetY);

      // Ajouter une petite étiquette avec le nom de la planète et son signe
      const labelRadius = planetRadius - 15;
      const labelAngle = angle + 0.1; // Léger décalage pour éviter les chevauchements
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      // Créer l'étiquette avec le nom de la planète et son signe
      const labelText = `${planet.name} ${planet.sign}`;

      // Dessiner un fond pour l'étiquette
      ctx.font = '7px Arial';
      const textWidth = ctx.measureText(labelText).width;
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.fillRect(labelX - textWidth/2 - 2, labelY - 5, textWidth + 4, 10);

      // Dessiner le texte de l'étiquette
      ctx.fillStyle = planet.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(labelText, labelX, labelY);
    });
  }

  // Draw legend for the watch
  function drawWatchLegend(ctx, width, height) {
    const legendY = height - 20; // Positionner la légende en bas du canvas
    const startX = 40;

    // Draw legend background
    ctx.fillStyle = 'rgba(245, 245, 245, 0.9)';
    ctx.fillRect(10, legendY - 85, width - 20, 95);
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 1;
    ctx.strokeRect(10, legendY - 85, width - 20, 95);

    // Explanation for inner watch
    ctx.font = 'bold 11px Arial';
    ctx.fillStyle = '#333';
    ctx.textAlign = 'center';
    ctx.fillText('Cercle intérieur: Heures = degrés dans le signe (0-30°) | Minutes = minutes d\'arc (0-60\') | Secondes = secondes d\'arc (0-60")', width/2, legendY - 70);

    // Explanation for natal planets in hour circle
    ctx.fillText('Cercle des heures: Planètes natales en mode 30° (position dans leur signe)', width/2, legendY - 55);

    // Explanation for outer circle
    ctx.fillText('Cercle extérieur: Positions des planètes natales (360°)', width/2, legendY - 40);

    // Explanation for transit planets in 30° mode
    ctx.fillText('Cercle droit supérieur: Planètes de transit en mode 30° (position dans leur signe)', width/2, legendY - 25);

    // Explanation for transit planets in 360° mode
    ctx.fillText('Cercle droit inférieur: Planètes de transit en mode 360° (position absolue)', width/2, legendY - 10);

    // Moon legend with symbol
    ctx.beginPath();
    ctx.moveTo(startX, legendY);
    ctx.lineTo(startX + 20, legendY);
    ctx.strokeStyle = '#888';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Draw arrow tip for Moon
    const arrowSize = 6;
    ctx.beginPath();
    ctx.moveTo(startX + 20, legendY);
    ctx.lineTo(startX + 20 - arrowSize, legendY - arrowSize/2);
    ctx.lineTo(startX + 20 - arrowSize, legendY + arrowSize/2);
    ctx.closePath();
    ctx.fillStyle = '#888';
    ctx.fill();

    ctx.font = 'bold 12px Arial';
    ctx.fillStyle = '#333';
    ctx.textAlign = 'left';
    ctx.fillText('Lune ☽', startX + 25, legendY + 4);

    // ASC-T legend with symbol
    ctx.beginPath();
    ctx.moveTo(startX + 100, legendY);
    ctx.lineTo(startX + 120, legendY);
    ctx.strokeStyle = '#FF9900';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Draw arrow tip for ASC-T
    ctx.beginPath();
    ctx.moveTo(startX + 120, legendY);
    ctx.lineTo(startX + 120 - arrowSize, legendY - arrowSize/2);
    ctx.lineTo(startX + 120 - arrowSize, legendY + arrowSize/2);
    ctx.closePath();
    ctx.fillStyle = '#FF9900';
    ctx.fill();

    ctx.fillStyle = '#FF9900';
    ctx.fillText('ASC-T ↑', startX + 125, legendY + 4);

    // DESC-T legend with symbol
    ctx.beginPath();
    ctx.moveTo(startX + 200, legendY);
    ctx.lineTo(startX + 220, legendY);
    ctx.strokeStyle = '#00CC66';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Draw arrow tip for DESC-T
    ctx.beginPath();
    ctx.moveTo(startX + 220, legendY);
    ctx.lineTo(startX + 220 - arrowSize, legendY - arrowSize/2);
    ctx.lineTo(startX + 220 - arrowSize, legendY + arrowSize/2);
    ctx.closePath();
    ctx.fillStyle = '#00CC66';
    ctx.fill();

    ctx.fillStyle = '#00CC66';
    ctx.fillText('DESC-T ↓', startX + 225, legendY + 4);

    // Current time display with milliseconds
    const now = new Date();
    const timeStr = now.toLocaleTimeString() + '.' + now.getMilliseconds().toString().padStart(3, '0');
    ctx.font = '10px Arial';
    ctx.fillStyle = '#666';
    ctx.textAlign = 'right';
    ctx.fillText(`Heure: ${timeStr}`, width - 20, legendY + 4);
  }

  // Draw the astrological chart on the canvas
  function drawChart(birthPositions, transitPositions) {
    const ctx = chartCanvas.getContext('2d');

    // Set canvas dimensions based on its container
    const width = chartCanvas.width = chartCanvas.offsetWidth;
    const height = chartCanvas.height = chartCanvas.offsetHeight;
    const centerX = width / 2;

    // Calcul adaptatif pour éviter la troncature
    const legendHeight = 60; // Espace pour la légende et le titre
    const margin = 20; // Marge de sécurité
    const availableHeight = height - legendHeight - margin;
    const availableWidth = width - (margin * 2);

    // Centrer verticalement dans l'espace disponible
    const centerY = legendHeight + (availableHeight / 2);

    // Rayon adaptatif basé sur l'espace disponible
    const radius = Math.min(availableWidth / 2, availableHeight / 2) - 10;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw chart title
    ctx.font = 'bold 14px ' + getComputedStyle(document.body).fontFamily;
    ctx.fillStyle = '#333';
    ctx.textAlign = 'center';
    ctx.fillText('Sidereal Chart', centerX, 20);

    // Draw legend at the top
    drawSiderealLegend(ctx, centerX, 35);

    // Draw zodiac wheel
    drawZodiacWheel(ctx, centerX, centerY, radius);

    // Calculate and draw the four angles (natal)
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );
    const latitude = parseFloat(latitudeInput.value) || 48.0667;
    const longitude = parseFloat(longitudeInput.value) || -0.7667;

    // Calculate local sidereal time for birth
    const lstBirth = calculateLocalSiderealTime(birthDate, longitude);

    // Calculate the four angles for birth chart
    let ascendantBirth = calculateAscendant(lstBirth, latitude);
    let midheavenBirth = calculateMidheaven(lstBirth);

    // Apply sidereal correction
    const ayanamsa = getAyanamsa(birthDate);
    ascendantBirth = normalizeAngle(ascendantBirth - ayanamsa);
    midheavenBirth = normalizeAngle(midheavenBirth - ayanamsa);

    // Calculate descendant (opposite to ascendant)
    const descendantBirth = normalizeAngle(ascendantBirth + 180);

    // Calculate imum coeli (opposite to midheaven)
    const imumCoeliBirth = normalizeAngle(midheavenBirth + 180);

    // Calculate transit angles (using current date and time)
    const transitDate = new Date(); // Date actuelle du PC

    // Calculate local sidereal time for transit
    const lstTransit = calculateLocalSiderealTime(transitDate, longitude);

    // Calculate ascendant for transit
    let ascendantTransit = calculateAscendant(lstTransit, latitude);
    let midheavenTransit = calculateMidheaven(lstTransit);

    // Apply sidereal correction
    ascendantTransit = normalizeAngle(ascendantTransit - ayanamsa);
    midheavenTransit = normalizeAngle(midheavenTransit - ayanamsa);

    // Calculate descendant (opposite to ascendant) for transit
    const descendantTransit = normalizeAngle(ascendantTransit + 180);

    // Draw the four angles (avec inversion ASC/DESC et MC/IC) - Natal
    drawAngleOnChart(ctx, centerX, centerY, radius, descendantBirth, 'ASC', '#FF0000');
    drawAngleOnChart(ctx, centerX, centerY, radius, ascendantBirth, 'DESC', '#00AA00');
    drawAngleOnChart(ctx, centerX, centerY, radius, imumCoeliBirth, 'MC', '#0000FF');
    drawAngleOnChart(ctx, centerX, centerY, radius, midheavenBirth, 'IC', '#AA00AA');

    // Draw transit ascendant and descendant
    drawAngleOnChart(ctx, centerX, centerY, radius, descendantTransit, 'ASC-T', '#FF9900', true);
    drawAngleOnChart(ctx, centerX, centerY, radius, ascendantTransit, 'DESC-T', '#00CC66', true);

    // Draw planets for birth chart (cercle intérieur)
    drawPlanets(ctx, centerX, centerY, radius * 0.55, birthPositions, false);

    // Draw planets for transit chart (cercle du milieu)
    drawPlanets(ctx, centerX, centerY, radius * 0.75, transitPositions, true);
  }

  // Draw the sidereal legend at the top
  function drawSiderealLegend(ctx, centerX, legendY) {
    ctx.font = '11px ' + getComputedStyle(document.body).fontFamily;

    // Calculer la largeur totale de la légende pour la centrer
    const legendItems = [
      { type: 'circle', color: '#FF0000', text: 'Birth Chart' },
      { type: 'circle', color: '#4A90E2', text: 'Transit Chart' },
      { type: 'line', color: '#FF0000', text: 'ASC' },
      { type: 'line', color: '#00AA00', text: 'DESC' },
      { type: 'line', color: '#0000FF', text: 'MC' },
      { type: 'line', color: '#AA00AA', text: 'IC' },
      { type: 'line', color: '#FF9900', text: 'ASC-T' },
      { type: 'line', color: '#00CC66', text: 'DESC-T' }
    ];

    // Calculer l'espacement entre les éléments (plus compact)
    const itemSpacing = 70;
    const totalWidth = (legendItems.length - 1) * itemSpacing;
    const startX = centerX - totalWidth / 2;

    legendItems.forEach((item, index) => {
      const x = startX + index * itemSpacing;

      if (item.type === 'circle') {
        // Dessiner un cercle pour les planètes
        ctx.beginPath();
        ctx.arc(x, legendY, 5, 0, 2 * Math.PI);
        ctx.strokeStyle = item.color;
        ctx.lineWidth = 1.5;
        ctx.stroke();

        // Texte
        ctx.fillStyle = item.color;
        ctx.textAlign = 'left';
        ctx.fillText(item.text, x + 10, legendY + 3);
      } else {
        // Dessiner une ligne pour les angles
        ctx.beginPath();
        ctx.moveTo(x, legendY - 2);
        ctx.lineTo(x + 12, legendY - 2);
        ctx.strokeStyle = item.color;
        ctx.lineWidth = 1.5;
        ctx.stroke();

        // Texte
        ctx.fillStyle = item.color;
        ctx.textAlign = 'left';
        ctx.fillText(item.text, x + 16, legendY + 3);
      }
    });
  }

  // Draw the zodiac wheel
  function drawZodiacWheel(ctx, centerX, centerY, radius) {
    // Draw outer circle (signes du zodiaque)
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw middle circle (planètes de transit)
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.75, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw inner circle (planètes natales)
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.55, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw center point
    ctx.beginPath();
    ctx.arc(centerX, centerY, 3, 0, 2 * Math.PI);
    ctx.fillStyle = '#333';
    ctx.fill();

    // Draw zodiac signs
    for (let i = 0; i < 12; i++) {
      const angle = (i * 30 - 90) * Math.PI / 180;

      // Draw sign division lines (traversent les 3 cercles)
      ctx.beginPath();
      ctx.moveTo(
        centerX + radius * 0.55 * Math.cos(angle),
        centerY + radius * 0.55 * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle)
      );
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Draw sign symbol (dans la couronne extérieure)
      const symbolAngle = (i * 30 - 90 + 15) * Math.PI / 180;
      const symbolX = centerX + radius * 0.875 * Math.cos(symbolAngle);
      const symbolY = centerY + radius * 0.875 * Math.sin(symbolAngle);

      ctx.font = 'bold 16px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(ZODIAC_SIGNS[i].substring(0, 3), symbolX, symbolY);

      // Draw degree markers (every 5 degrees) dans la couronne extérieure
      for (let j = 1; j < 6; j++) {
        const markerAngle = (i * 30 + j * 5 - 90) * Math.PI / 180;
        const markerLength = j % 2 === 0 ? 0.05 : 0.03; // Longer markers for 10, 20 degrees

        ctx.beginPath();
        ctx.moveTo(
          centerX + radius * (1 - markerLength) * Math.cos(markerAngle),
          centerY + radius * (1 - markerLength) * Math.sin(markerAngle)
        );
        ctx.lineTo(
          centerX + radius * Math.cos(markerAngle),
          centerY + radius * Math.sin(markerAngle)
        );
        ctx.strokeStyle = '#999';
        ctx.lineWidth = 0.5;
        ctx.stroke();
      }
    }

    // Draw outer decorative circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 1.02, 0, 2 * Math.PI);
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 1;
    ctx.stroke();
  }

  // Draw planets on the chart
  function drawPlanets(ctx, centerX, centerY, radius, positions, isTransit) {
    const planets = [
      { key: 'sun', name: 'Sun' },
      { key: 'moon', name: 'Moon' },
      { key: 'mercury', name: 'Mercury' },
      { key: 'venus', name: 'Venus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturn' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    planets.forEach(planet => {
      const planetData = positions[planet.key];
      const angle = (planetData.siderealLongitude - 90) * Math.PI / 180;

      const planetX = centerX + radius * Math.cos(angle);
      const planetY = centerY + radius * Math.sin(angle);

      // Draw a circle background for the planet
      ctx.beginPath();
      ctx.arc(planetX, planetY, isTransit ? 12 : 14, 0, 2 * Math.PI);
      ctx.fillStyle = 'white';
      ctx.fill();

      // Draw a small circle around the planet
      ctx.beginPath();
      ctx.arc(planetX, planetY, isTransit ? 12 : 14, 0, 2 * Math.PI);
      ctx.strokeStyle = isTransit ? '#4A90E2' : '#FF0000'; // Bleu pour transit, rouge pour natal
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw planet symbol
      ctx.font = isTransit ? 'bold 14px Arial' : 'bold 16px Arial';
      ctx.fillStyle = isTransit ? '#4A90E2' : '#FF0000'; // Bleu pour transit, rouge pour natal
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      const planetSymbol = PLANET_SYMBOLS[planet.name] || planet.name.substring(0, 2);
      ctx.fillText(planetSymbol, planetX, planetY);



      // Draw degree marker (ajusté selon le cercle)
      const markerRadius = isTransit ? radius * 0.85 : radius * 0.45; // Transit au milieu, natal à l'intérieur
      const markerX = centerX + markerRadius * Math.cos(angle);
      const markerY = centerY + markerRadius * Math.sin(angle);
      ctx.font = '9px Arial';
      ctx.fillStyle = isTransit ? '#4A90E2' : '#FF0000'; // Même couleur que les planètes
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(Math.round(planetData.siderealLongitude % 30) + '°', markerX, markerY);
    });
  }

  // Draw the standard zodiac (30° divisions)
  function drawStandardZodiac(birthPositions, transitPositions) {
    if (!standardZodiacCanvas) return;

    const ctx = standardZodiacCanvas.getContext('2d');
    const width = standardZodiacCanvas.width = standardZodiacCanvas.offsetWidth;
    const height = standardZodiacCanvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Stocker les positions des planètes pour les interactions
    standardZodiacCanvas.birthPositions = birthPositions;
    standardZodiacCanvas.transitPositions = transitPositions;

    // Calculer les aspects entre les planètes de transit et les planètes natales
    const aspects = calculateTransitNatalAspects(transitPositions, birthPositions);

    // Afficher les aspects dans la section dédiée avec les filtres par défaut
    displayZodiacAspectsWithFilters('standard-zodiac-aspects', transitPositions, birthPositions);

    // Calculate dimensions
    const zodiacWidth = width - 40;
    const zodiacHeight = height - 20;
    const startX = 20;
    const startY = 10;
    const signHeight = zodiacHeight / 12;

    // Calculate ascendant
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );
    const latitude = parseFloat(latitudeInput.value) || 48.0667;
    const longitude = parseFloat(longitudeInput.value) || -0.7667;

    // Calculate ascendant
    const lst = calculateLocalSiderealTime(birthDate, longitude);
    let ascendant = calculateAscendant(lst, latitude);

    // Apply sidereal correction
    const ayanamsa = getAyanamsa(birthDate);
    ascendant = normalizeAngle(ascendant - ayanamsa);

    // Draw zodiac background
    ctx.fillStyle = '#f9f9f9';
    ctx.fillRect(startX, startY, zodiacWidth, zodiacHeight);
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 1;
    ctx.strokeRect(startX, startY, zodiacWidth, zodiacHeight);

    // Draw zodiac signs
    for (let i = 0; i < 12; i++) {
      const y = startY + i * signHeight;

      // Draw sign background (alternating colors)
      ctx.fillStyle = i % 2 === 0 ? '#f5f5f5' : '#ffffff';
      ctx.fillRect(startX, y, zodiacWidth, signHeight);

      // Draw horizontal line
      ctx.beginPath();
      ctx.moveTo(startX, y);
      ctx.lineTo(startX + zodiacWidth, y);
      ctx.strokeStyle = '#ddd';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Draw sign name
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'middle';
      ctx.fillText(ZODIAC_SIGNS[i], startX + 5, y + signHeight / 2);

      // Draw degree markers
      for (let j = 0; j < 30; j += 5) {
        const markerX = startX + zodiacWidth / 30 * j;

        ctx.beginPath();
        ctx.moveTo(markerX, y);
        ctx.lineTo(markerX, y + 3);
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 1;
        ctx.stroke();

        // Add degree numbers for multiples of 10
        if (j % 10 === 0) {
          ctx.font = '10px Arial';
          ctx.fillStyle = '#999';
          ctx.textAlign = 'center';
          ctx.fillText(j.toString(), markerX, y + 10);
        }
      }
    }

    // Draw planets for birth chart
    drawVerticalPlanets(ctx, startX, startY, zodiacWidth, signHeight, birthPositions, false);

    // Draw planets for transit chart
    drawVerticalPlanets(ctx, startX, startY, zodiacWidth, signHeight, transitPositions, true);

    // Calculate descendant (opposite to ascendant)
    const descendant = normalizeAngle(ascendant + 180);

    // Calculate midheaven and imum coeli
    let midheaven = calculateMidheaven(lst);
    midheaven = normalizeAngle(midheaven - ayanamsa);
    const imumCoeli = normalizeAngle(midheaven + 180);

    // Calculate transit ascendant (using current date and time)
    const transitDate = new Date(); // Date actuelle du PC

    // Calculate local sidereal time for transit
    const lstTransit = calculateLocalSiderealTime(transitDate, longitude);

    // Calculate ascendant for transit
    let ascendantTransit = calculateAscendant(lstTransit, latitude);

    // Apply sidereal correction
    ascendantTransit = normalizeAngle(ascendantTransit - ayanamsa);

    // Calculate descendant (opposite to ascendant) for transit
    const descendantTransit = normalizeAngle(ascendantTransit + 180);

    // Draw the four angles (avec inversion ASC/DESC et MC/IC)
    drawVerticalAngle(ctx, startX, startY, zodiacWidth, signHeight, descendant, 'ASC', '#FF0000');
    drawVerticalAngle(ctx, startX, startY, zodiacWidth, signHeight, ascendant, 'DESC', '#00AA00');
    drawVerticalAngle(ctx, startX, startY, zodiacWidth, signHeight, imumCoeli, 'MC', '#0000FF');
    drawVerticalAngle(ctx, startX, startY, zodiacWidth, signHeight, midheaven, 'IC', '#AA00AA');

    // Draw transit ascendant and descendant
    drawVerticalAngle(ctx, startX, startY, zodiacWidth, signHeight, descendantTransit, 'ASC-T', '#FF9900', true);
    drawVerticalAngle(ctx, startX, startY, zodiacWidth, signHeight, ascendantTransit, 'DESC-T', '#00CC66', true);
  }

  // Fonction pour dessiner un angle astrologique sur le zodiaque vertical
  function drawVerticalAngle(ctx, startX, startY, zodiacWidth, signHeight, angle_deg, label, color, isTransit = false) {
    // Calculer la position de l'angle
    const signIndex = Math.floor(angle_deg / 30);
    const degreeInSign = angle_deg % 30;

    // Calculer la position sur le canvas
    const y = startY + signIndex * signHeight;
    const x = startX + (zodiacWidth / 30) * degreeInSign;

    // Dessiner un triangle pour marquer l'angle
    const triangleSize = isTransit ? 6 : 8;

    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x - triangleSize/2, y - triangleSize);
    ctx.lineTo(x + triangleSize/2, y - triangleSize);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // Ajouter le texte du label
    ctx.font = isTransit ? 'bold 9px Arial' : 'bold 10px Arial';
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(label, x, y - triangleSize - 8);
  }

  // Draw the Nakshatra zodiac (13°20' divisions)
  function drawNakshatraZodiac(birthPositions, transitPositions) {
    if (!nakshatraZodiacCanvas) return;

    const ctx = nakshatraZodiacCanvas.getContext('2d');
    const width = nakshatraZodiacCanvas.width = nakshatraZodiacCanvas.offsetWidth;
    const height = nakshatraZodiacCanvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Stocker les positions des planètes pour les interactions
    nakshatraZodiacCanvas.birthPositions = birthPositions;
    nakshatraZodiacCanvas.transitPositions = transitPositions;

    // Calculer les aspects entre les planètes de transit et les planètes natales
    const aspects = calculateTransitNatalAspects(transitPositions, birthPositions);

    // Afficher les aspects dans la section dédiée avec les filtres par défaut
    displayZodiacAspectsWithFilters('nakshatra-zodiac-aspects', transitPositions, birthPositions);

    // Calculate dimensions
    const zodiacWidth = width - 40;
    const zodiacHeight = height - 20;
    const startX = 20;
    const startY = 10;

    // Calculate ascendant
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );
    const latitude = parseFloat(latitudeInput.value) || 48.0667;
    const longitude = parseFloat(longitudeInput.value) || -0.7667;

    // Calculate ascendant
    const lst = calculateLocalSiderealTime(birthDate, longitude);
    let ascendant = calculateAscendant(lst, latitude);

    // Apply sidereal correction
    const ayanamsa = getAyanamsa(birthDate);
    ascendant = normalizeAngle(ascendant - ayanamsa);

    // Nakshatra divisions (27 nakshatras of 13°20' each)
    const nakshatraCount = 27;
    const nakshatraHeight = zodiacHeight / nakshatraCount;

    // Nakshatra names (simplified for display)
    const nakshatraNames = [
      "Ashwini", "Bharani", "Krittika", "Rohini", "Mrigashira", "Ardra",
      "Punarvasu", "Pushya", "Ashlesha", "Magha", "Purva Phalguni", "Uttara Phalguni",
      "Hasta", "Chitra", "Swati", "Vishakha", "Anuradha", "Jyeshtha",
      "Mula", "Purva Ashadha", "Uttara Ashadha", "Shravana", "Dhanishta", "Shatabhisha",
      "Purva Bhadrapada", "Uttara Bhadrapada", "Revati"
    ];

    // Draw zodiac background
    ctx.fillStyle = '#f9f9f9';
    ctx.fillRect(startX, startY, zodiacWidth, zodiacHeight);
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 1;
    ctx.strokeRect(startX, startY, zodiacWidth, zodiacHeight);

    // Draw nakshatra divisions
    for (let i = 0; i < nakshatraCount; i++) {
      const y = startY + i * nakshatraHeight;

      // Draw nakshatra background (alternating colors)
      ctx.fillStyle = i % 2 === 0 ? '#f5f5f5' : '#ffffff';
      ctx.fillRect(startX, y, zodiacWidth, nakshatraHeight);

      // Draw horizontal line
      ctx.beginPath();
      ctx.moveTo(startX, y);
      ctx.lineTo(startX + zodiacWidth, y);
      ctx.strokeStyle = '#ddd';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Draw nakshatra name
      ctx.font = '10px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'middle';
      ctx.fillText(nakshatraNames[i], startX + 5, y + nakshatraHeight / 2);

      // Draw degree markers
      const nakshatraDegrees = 13 + 1/3; // 13°20' in decimal
      for (let j = 0; j < nakshatraDegrees; j += 2) {
        const markerX = startX + zodiacWidth / nakshatraDegrees * j;

        ctx.beginPath();
        ctx.moveTo(markerX, y);
        ctx.lineTo(markerX, y + 2);
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 1;
        ctx.stroke();
      }
    }

    // Draw planets for birth chart (adjusted for Nakshatra divisions)
    drawVerticalNakshatraPlanets(ctx, startX, startY, zodiacWidth, nakshatraHeight, birthPositions, false);

    // Draw planets for transit chart (adjusted for Nakshatra divisions)
    drawVerticalNakshatraPlanets(ctx, startX, startY, zodiacWidth, nakshatraHeight, transitPositions, true);

    // Calculate descendant (opposite to ascendant)
    const descendant = normalizeAngle(ascendant + 180);

    // Calculate midheaven and imum coeli
    let midheaven = calculateMidheaven(lst);
    midheaven = normalizeAngle(midheaven - ayanamsa);
    const imumCoeli = normalizeAngle(midheaven + 180);

    // Calculate transit ascendant (using current date and time)
    const transitDate = new Date(); // Date actuelle du PC

    // Calculate local sidereal time for transit
    const lstTransit = calculateLocalSiderealTime(transitDate, longitude);

    // Calculate ascendant for transit
    let ascendantTransit = calculateAscendant(lstTransit, latitude);

    // Apply sidereal correction
    ascendantTransit = normalizeAngle(ascendantTransit - ayanamsa);

    // Calculate descendant (opposite to ascendant) for transit
    const descendantTransit = normalizeAngle(ascendantTransit + 180);

    // Draw the four angles (avec inversion ASC/DESC et MC/IC)
    drawVerticalNakshatraAngle(ctx, startX, startY, zodiacWidth, nakshatraHeight, descendant, 'ASC', '#FF0000');
    drawVerticalNakshatraAngle(ctx, startX, startY, zodiacWidth, nakshatraHeight, ascendant, 'DESC', '#00AA00');
    drawVerticalNakshatraAngle(ctx, startX, startY, zodiacWidth, nakshatraHeight, imumCoeli, 'MC', '#0000FF');
    drawVerticalNakshatraAngle(ctx, startX, startY, zodiacWidth, nakshatraHeight, midheaven, 'IC', '#AA00AA');

    // Draw transit ascendant and descendant
    drawVerticalNakshatraAngle(ctx, startX, startY, zodiacWidth, nakshatraHeight, descendantTransit, 'ASC-T', '#FF9900', true);
    drawVerticalNakshatraAngle(ctx, startX, startY, zodiacWidth, nakshatraHeight, ascendantTransit, 'DESC-T', '#00CC66', true);
  }

  // Fonction pour dessiner un angle astrologique sur le zodiaque Nakshatra
  function drawVerticalNakshatraAngle(ctx, startX, startY, zodiacWidth, nakshatraHeight, angle_deg, label, color, isTransit = false) {
    // Calculer la position absolue de l'angle (0-360°)
    const absoluteLongitude = angle_deg;

    // Calculer la position Nakshatra (27 nakshatras de 13°20' chacun)
    const nakshatraDegree = 13 + 1/3; // 13°20' en décimal
    const nakshatraIndex = Math.floor(absoluteLongitude / nakshatraDegree);
    const degreeInNakshatra = absoluteLongitude % nakshatraDegree;

    // Calculer la position sur le canvas
    const y = startY + nakshatraIndex * nakshatraHeight;
    const x = startX + (zodiacWidth / nakshatraDegree) * degreeInNakshatra;

    // Dessiner un triangle pour marquer l'angle
    const triangleSize = isTransit ? 6 : 8;

    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x - triangleSize/2, y - triangleSize);
    ctx.lineTo(x + triangleSize/2, y - triangleSize);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // Ajouter le texte du label
    ctx.font = isTransit ? 'bold 9px Arial' : 'bold 10px Arial';
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(label, x, y - triangleSize - 8);
  }

  // Draw planets on the vertical standard zodiac
  function drawVerticalPlanets(ctx, startX, startY, zodiacWidth, signHeight, positions, isTransit) {
    const planets = [
      { key: 'sun', name: 'Sun' },
      { key: 'moon', name: 'Moon' },
      { key: 'mercury', name: 'Mercury' },
      { key: 'venus', name: 'Venus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturn' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    // Stocker les coordonnées des planètes pour les interactions
    const planetCoordinates = [];

    planets.forEach(planet => {
      const planetData = positions[planet.key];

      // Calculate position in the 30° zodiac
      const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);
      const degreeInSign = parseFloat(planetData.degree);

      // Calculate position on the canvas
      const y = startY + signIndex * signHeight + signHeight / 2;
      const x = startX + (zodiacWidth / 30) * degreeInSign;

      // Draw planet symbol
      const planetSymbol = PLANET_SYMBOLS[planet.name] || planet.name.substring(0, 2);

      // Draw a circle background for the planet
      ctx.beginPath();
      ctx.arc(x, y, isTransit ? 8 : 10, 0, 2 * Math.PI);
      ctx.fillStyle = isTransit ? 'rgba(0, 113, 227, 0.1)' : 'rgba(51, 51, 51, 0.05)';
      ctx.fill();

      // Draw a circle around the planet
      ctx.beginPath();
      ctx.arc(x, y, isTransit ? 8 : 10, 0, 2 * Math.PI);
      ctx.strokeStyle = isTransit ? '#0071e3' : '#333';
      ctx.lineWidth = isTransit ? 1 : 1.5;
      ctx.stroke();

      // Draw planet symbol
      ctx.font = isTransit ? 'bold 10px Arial' : 'bold 12px Arial';
      ctx.fillStyle = isTransit ? '#0071e3' : '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(planetSymbol, x, y);

      // Stocker les coordonnées et informations de la planète pour les interactions
      planetCoordinates.push({
        x: x,
        y: y,
        radius: isTransit ? 8 : 10,
        planet: planet.key,
        isTransit: isTransit,
        data: planetData
      });
    });

    // Stocker les coordonnées dans le canvas pour les interactions
    if (isTransit) {
      standardZodiacCanvas.transitPlanetCoordinates = planetCoordinates;
    } else {
      standardZodiacCanvas.birthPlanetCoordinates = planetCoordinates;
    }
  }

  // Draw planets on the vertical Nakshatra zodiac
  function drawVerticalNakshatraPlanets(ctx, startX, startY, zodiacWidth, nakshatraHeight, positions, isTransit) {
    const planets = [
      { key: 'sun', name: 'Sun' },
      { key: 'moon', name: 'Moon' },
      { key: 'mercury', name: 'Mercury' },
      { key: 'venus', name: 'Venus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturn' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    // Stocker les coordonnées des planètes pour les interactions
    const planetCoordinates = [];

    planets.forEach(planet => {
      const planetData = positions[planet.key];

      // Calculate absolute longitude (0-360°)
      const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);
      const absoluteLongitude = signIndex * 30 + parseFloat(planetData.degree);

      // Calculate Nakshatra position (27 nakshatras of 13°20' each)
      const nakshatraDegree = 13 + 1/3; // 13°20' in decimal
      const nakshatraIndex = Math.floor(absoluteLongitude / nakshatraDegree);
      const degreeInNakshatra = absoluteLongitude % nakshatraDegree;

      // Calculate position on the canvas
      const y = startY + nakshatraIndex * nakshatraHeight + nakshatraHeight / 2;
      const x = startX + (zodiacWidth / nakshatraDegree) * degreeInNakshatra;

      // Draw planet symbol
      const planetSymbol = PLANET_SYMBOLS[planet.name] || planet.name.substring(0, 2);

      // Draw a circle background for the planet
      ctx.beginPath();
      ctx.arc(x, y, isTransit ? 8 : 10, 0, 2 * Math.PI);
      ctx.fillStyle = isTransit ? 'rgba(0, 113, 227, 0.1)' : 'rgba(51, 51, 51, 0.05)';
      ctx.fill();

      // Draw a circle around the planet
      ctx.beginPath();
      ctx.arc(x, y, isTransit ? 8 : 10, 0, 2 * Math.PI);
      ctx.strokeStyle = isTransit ? '#0071e3' : '#333';
      ctx.lineWidth = isTransit ? 1 : 1.5;
      ctx.stroke();

      // Draw planet symbol
      ctx.font = isTransit ? 'bold 10px Arial' : 'bold 12px Arial';
      ctx.fillStyle = isTransit ? '#0071e3' : '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(planetSymbol, x, y);

      // Stocker les coordonnées et informations de la planète pour les interactions
      planetCoordinates.push({
        x: x,
        y: y,
        radius: isTransit ? 8 : 10,
        planet: planet.key,
        isTransit: isTransit,
        data: planetData
      });
    });

    // Stocker les coordonnées dans le canvas pour les interactions
    if (isTransit) {
      nakshatraZodiacCanvas.transitPlanetCoordinates = planetCoordinates;
    } else {
      nakshatraZodiacCanvas.birthPlanetCoordinates = planetCoordinates;
    }
  }

  // Calculate future aspects between transit and natal planets
  function calculateFutureAspects() {
    // Get birth date and time
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );

    // Get prediction start date
    const predictionStartDateInput = document.getElementById('prediction-start-date');
    let startDate;

    if (predictionStartDateInput.value) {
      // Use the custom start date if provided
      startDate = new Date(predictionStartDateInput.value);
      startDate.setHours(0, 0, 0, 0); // Start at beginning of the day
    } else {
      // Fallback to current transit date if no custom date is provided
      startDate = new Date(transitDateInput.value);
      startDate.setHours(
        parseInt(transitHourInput.value) || 0,
        parseInt(transitMinuteInput.value) || 0,
        0
      );

      // Set the transit date as the default value for the prediction start date
      predictionStartDateInput.valueAsDate = startDate;
    }

    // Get prediction period in months
    const predictionMonths = parseInt(predictionMonthsInput.value) || 3;

    // Get selected aspect types from the stored future aspects
    const selectedAspectTypes = [];

    // Get standard aspects
    if (futureAspects && Array.isArray(futureAspects.standard)) {
      futureAspects.standard.forEach(aspect => {
        if (aspect.selected) {
          selectedAspectTypes.push(parseFloat(aspect.angle));
        }
      });
    }

    // Get custom aspects
    if (futureAspects && Array.isArray(futureAspects.custom)) {
      futureAspects.custom.forEach(aspect => {
        if (aspect.selected) {
          selectedAspectTypes.push(parseFloat(aspect.angle));
        }
      });
    }

    // If no aspects selected, show message and return
    if (selectedAspectTypes.length === 0) {
      futureAspectsBody.innerHTML = '<tr><td colspan="4">Veuillez sélectionner au moins un type d\'aspect.</td></tr>';
      return;
    }

    // Get selected transit planets
    const selectedTransitPlanets = [];
    document.querySelectorAll('input[name="transit-planet"]:checked').forEach(checkbox => {
      selectedTransitPlanets.push(checkbox.value);
    });

    // If no transit planets selected, show message and return
    if (selectedTransitPlanets.length === 0) {
      futureAspectsBody.innerHTML = '<tr><td colspan="4">Veuillez sélectionner au moins une planète de transit.</td></tr>';
      return;
    }

    // Get selected natal planets
    const selectedNatalPlanets = [];
    document.querySelectorAll('input[name="natal-planet"]:checked').forEach(checkbox => {
      selectedNatalPlanets.push(checkbox.value);
    });

    // If no natal planets selected, show message and return
    if (selectedNatalPlanets.length === 0) {
      futureAspectsBody.innerHTML = '<tr><td colspan="4">Veuillez sélectionner au moins une planète natale.</td></tr>';
      return;
    }

    // Calculate natal positions
    const birthPositions = calculatePlanetaryPositions(birthDate);

    // Clear previous results
    futureAspectsBody.innerHTML = '';

    // Calculate future aspects with selected planets and aspects
    const calculatedAspects = findFutureAspects(birthPositions, startDate, predictionMonths, selectedAspectTypes, selectedTransitPlanets, selectedNatalPlanets);

    // Sort aspects by date
    calculatedAspects.sort((a, b) => a.date.getTime() - b.date.getTime());

    // Display future aspects
    displayFutureAspects(calculatedAspects);

    // Save prediction start date, selected planets and aspects to storage
    if (predictionStartDateInput.value) {
      chrome.storage.local.set({
        predictionStartDate: predictionStartDateInput.value,
        selectedTransitPlanets: selectedTransitPlanets,
        selectedNatalPlanets: selectedNatalPlanets,
        futureAspects: futureAspects // Sauvegarde la variable globale futureAspects
      });
    }
  }

  // Find future aspects between transit and natal planets
  function findFutureAspects(birthPositions, startDate, predictionMonths, aspectAngles, selectedTransitPlanets = null, selectedNatalPlanets = null) {
    const futureAspects = [];
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + predictionMonths);

    // Define all possible transit planets
    const allTransitPlanets = [
      { key: 'sun', name: 'Soleil', speedPerDay: 1 }, // Approximate speed in degrees per day
      { key: 'moon', name: 'Lune', speedPerDay: 13 },
      { key: 'mercury', name: 'Mercure', speedPerDay: 1.5 },
      { key: 'venus', name: 'Vénus', speedPerDay: 1.2 },
      { key: 'mars', name: 'Mars', speedPerDay: 0.5 },
      { key: 'jupiter', name: 'Jupiter', speedPerDay: 0.08 },
      { key: 'saturn', name: 'Saturne', speedPerDay: 0.03 }
    ];

    // Filter transit planets if a selection is provided
    const transitPlanets = selectedTransitPlanets
      ? allTransitPlanets.filter(planet => selectedTransitPlanets.includes(planet.key))
      : allTransitPlanets;

    // Define all possible natal planets
    const allNatalPlanets = [
      { key: 'sun', name: 'Soleil' },
      { key: 'moon', name: 'Lune' },
      { key: 'mercury', name: 'Mercure' },
      { key: 'venus', name: 'Vénus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturne' }
    ];

    // Filter natal planets if a selection is provided
    const natalPlanets = selectedNatalPlanets
      ? allNatalPlanets.filter(planet => selectedNatalPlanets.includes(planet.key))
      : allNatalPlanets;

    // For each transit planet
    for (const transitPlanet of transitPlanets) {
      // For each natal planet
      for (const natalPlanet of natalPlanets) {
        // Get natal position
        const natalPosition = birthPositions[natalPlanet.key].siderealLongitude;

        // Calculate transit start position
        const transitStartPosition = calculatePlanetaryPositions(startDate)[transitPlanet.key].siderealLongitude;

        // For each selected aspect angle
        for (const aspectAngle of aspectAngles) {
          // Calculate aspect positions (adding and subtracting the aspect angle)
          const aspectPosition1 = (natalPosition + aspectAngle) % 360;
          const aspectPosition2 = (natalPosition - aspectAngle + 360) % 360;

          // Calculate days to reach aspect position 1
          let daysToAspect1 = calculateDaysToPosition(transitStartPosition, aspectPosition1, transitPlanet.speedPerDay);
          if (daysToAspect1 >= 0 && daysToAspect1 <= predictionMonths * 30) {
            const aspectDate1 = new Date(startDate);
            aspectDate1.setDate(aspectDate1.getDate() + daysToAspect1);

            // Refine the date with more precise calculation
            const refinedDate1 = refineAspectDate(aspectDate1, transitPlanet.key, aspectPosition1);

            futureAspects.push({
              transitPlanet: transitPlanet.name,
              natalPlanet: natalPlanet.name,
              aspect: `${aspectAngle}°`,
              aspectAngle: aspectAngle, // Store the actual angle value for display
              date: refinedDate1
            });
          }

          // Calculate days to reach aspect position 2
          let daysToAspect2 = calculateDaysToPosition(transitStartPosition, aspectPosition2, transitPlanet.speedPerDay);
          if (daysToAspect2 >= 0 && daysToAspect2 <= predictionMonths * 30) {
            const aspectDate2 = new Date(startDate);
            aspectDate2.setDate(aspectDate2.getDate() + daysToAspect2);

            // Refine the date with more precise calculation
            const refinedDate2 = refineAspectDate(aspectDate2, transitPlanet.key, aspectPosition2);

            futureAspects.push({
              transitPlanet: transitPlanet.name,
              natalPlanet: natalPlanet.name,
              aspect: `${aspectAngle}°`,
              aspectAngle: aspectAngle, // Store the actual angle value for display
              date: refinedDate2
            });
          }
        }
      }
    }

    return futureAspects;
  }

  // Calculate days needed for a planet to reach a specific position
  function calculateDaysToPosition(currentPosition, targetPosition, speedPerDay) {
    // Handle cases where the planet needs to cross 0°
    let distance = targetPosition - currentPosition;
    if (distance < 0) {
      distance += 360;
    }

    // Calculate days based on speed
    return Math.round(distance / speedPerDay);
  }

  // Refine the aspect date with more precise calculation
  function refineAspectDate(approximateDate, planetKey, targetPosition) {
    // Check a few days before and after the approximate date
    const checkRange = 5; // Days to check before and after
    let closestDate = approximateDate;
    let smallestDifference = 360;

    for (let i = -checkRange; i <= checkRange; i++) {
      const checkDate = new Date(approximateDate);
      checkDate.setDate(checkDate.getDate() + i);

      const position = calculatePlanetaryPositions(checkDate)[planetKey].siderealLongitude;

      // Calculate the angular difference
      let difference = Math.abs(position - targetPosition);
      if (difference > 180) {
        difference = 360 - difference;
      }

      // Update closest date if this is more precise
      if (difference < smallestDifference) {
        smallestDifference = difference;
        closestDate = new Date(checkDate);
      }
    }

    return closestDate;
  }

  // Fonction pour mettre à jour le résumé des aspects sélectionnés
  function updateSelectedAspectsSummary() {
    if (!selectedAspectsSummary) return;

    const selectedAspects = [];

    // Vérifier que futureAspects est défini et a les propriétés attendues
    if (futureAspects && Array.isArray(futureAspects.standard)) {
      // Ajouter les aspects standards sélectionnés
      futureAspects.standard.forEach(aspect => {
        if (aspect.selected) {
          selectedAspects.push(aspect.name);
        }
      });
    }

    // Vérifier que futureAspects.custom est défini
    if (futureAspects && Array.isArray(futureAspects.custom)) {
      // Ajouter les aspects personnalisés sélectionnés
      futureAspects.custom.forEach(aspect => {
        if (aspect.selected) {
          selectedAspects.push(aspect.name);
        }
      });
    }

    // Afficher le résumé
    if (selectedAspects.length === 0) {
      selectedAspectsSummary.textContent = "Aucun aspect sélectionné";
    } else {
      selectedAspectsSummary.textContent = selectedAspects.join(", ");
    }
  }

  // Fonction pour ouvrir la fenêtre modale des aspects futurs
  function openFutureAspectsModal() {
    if (!futureAspectsModal) return;

    // Mettre à jour les cases à cocher des aspects standards
    document.querySelectorAll('input[name="future-aspect-type"]').forEach(checkbox => {
      const aspectAngle = parseFloat(checkbox.value);
      const aspect = futureAspects.standard.find(a => parseFloat(a.angle) === aspectAngle);
      if (aspect) {
        checkbox.checked = aspect.selected;
      }
    });

    // Mettre à jour la liste des aspects personnalisés
    updateCustomFutureAspectsList();

    // Afficher la fenêtre modale
    futureAspectsModal.style.display = 'block';
  }

  // Fonction pour sauvegarder les aspects sélectionnés
  function saveFutureAspects() {
    // Sauvegarder les aspects standards
    document.querySelectorAll('input[name="future-aspect-type"]').forEach(checkbox => {
      const aspectAngle = parseFloat(checkbox.value);
      const aspectIndex = futureAspects.standard.findIndex(a => parseFloat(a.angle) === aspectAngle);
      if (aspectIndex !== -1) {
        futureAspects.standard[aspectIndex].selected = checkbox.checked;
      }
    });

    // Mettre à jour le résumé des aspects sélectionnés
    updateSelectedAspectsSummary();

    // Sauvegarder dans le stockage local
    chrome.storage.local.set({ futureAspects: futureAspects });
  }

  // Fonction pour ajouter un aspect personnalisé
  function addCustomFutureAspect() {
    const angleInput = document.getElementById('new-custom-aspect-angle');
    const nameInput = document.getElementById('new-custom-aspect-name');

    const angle = parseFloat(angleInput.value) || 0;
    const name = nameInput.value.trim() || `Aspect de ${angle}°`;

    // Vérifier si l'angle est valide
    if (angle < 0 || angle > 180) {
      alert("L'angle doit être compris entre 0 et 180 degrés.");
      return;
    }

    // Vérifier si l'aspect existe déjà
    const existingStandardAspect = futureAspects.standard.find(a => Math.abs(parseFloat(a.angle) - angle) < 0.01);
    const existingCustomAspect = futureAspects.custom.find(a => Math.abs(parseFloat(a.angle) - angle) < 0.01);

    if (existingStandardAspect || existingCustomAspect) {
      alert("Un aspect avec cet angle existe déjà.");
      return;
    }

    // Ajouter l'aspect personnalisé
    futureAspects.custom.push({
      angle: angle,
      name: name,
      selected: true
    });

    // Mettre à jour la liste des aspects personnalisés
    updateCustomFutureAspectsList();

    // Réinitialiser les champs
    angleInput.value = "";
    nameInput.value = "";
  }

  // Fonction pour mettre à jour la liste des aspects personnalisés
  function updateCustomFutureAspectsList() {
    if (!customFutureAspectsList) return;

    // Vider la liste
    customFutureAspectsList.innerHTML = '';

    // Ajouter chaque aspect personnalisé
    futureAspects.custom.forEach((aspect, index) => {
      const aspectItem = document.createElement('div');
      aspectItem.className = 'custom-aspect-item';

      aspectItem.innerHTML = `
        <div class="custom-aspect-info">
          <input type="checkbox" data-index="${index}" ${aspect.selected ? 'checked' : ''}>
          <span>${aspect.name} (${aspect.angle}°)</span>
        </div>
        <div class="custom-aspect-actions">
          <button type="button" class="delete-aspect-btn" data-index="${index}">×</button>
        </div>
      `;

      // Ajouter un écouteur d'événement pour la case à cocher
      const checkbox = aspectItem.querySelector('input[type="checkbox"]');
      checkbox.addEventListener('change', function() {
        const index = parseInt(this.dataset.index);
        futureAspects.custom[index].selected = this.checked;
      });

      // Ajouter un écouteur d'événement pour le bouton de suppression
      const deleteBtn = aspectItem.querySelector('.delete-aspect-btn');
      deleteBtn.addEventListener('click', function() {
        const index = parseInt(this.dataset.index);
        futureAspects.custom.splice(index, 1);
        updateCustomFutureAspectsList();
      });

      customFutureAspectsList.appendChild(aspectItem);
    });

    // Afficher un message si aucun aspect personnalisé
    if (futureAspects.custom.length === 0) {
      const emptyMessage = document.createElement('div');
      emptyMessage.className = 'empty-aspects-message';
      emptyMessage.textContent = 'Aucun aspect personnalisé ajouté.';
      customFutureAspectsList.appendChild(emptyMessage);
    }
  }

  // Display future aspects in the table
  function displayFutureAspects(aspectResults) {
    if (aspectResults.length === 0) {
      // No aspects found
      const noAspectsRow = document.createElement('tr');
      noAspectsRow.innerHTML = `<td colspan="5">Aucun aspect trouvé dans la période sélectionnée.</td>`;
      futureAspectsBody.appendChild(noAspectsRow);
      return;
    }

    // Add each aspect to the table
    aspectResults.forEach(aspect => {
      const row = document.createElement('tr');

      // Format the date
      const dateStr = formatDateTime(aspect.date);

      // Get aspect name based on angle
      let aspectName;
      const aspectAngle = aspect.aspectAngle;

      // Determine the aspect name based on the angle
      switch (aspectAngle) {
        case 0:
          aspectName = "Conjonction (0°)";
          break;
        case 16:
          aspectName = "Aspect de 16°";
          break;
        case 30:
          aspectName = "Semi-sextile (30°)";
          break;
        case 45:
          aspectName = "Semi-carré (45°)";
          break;
        case 60:
          aspectName = "Sextile (60°)";
          break;
        case 90:
          aspectName = "Carré (90°)";
          break;
        case 120:
          aspectName = "Trigone (120°)";
          break;
        case 135:
          aspectName = "Sesqui-carré (135°)";
          break;
        case 150:
          aspectName = "Quinconce (150°)";
          break;
        case 180:
          aspectName = "Opposition (180°)";
          break;
        default:
          // Check if it's a custom aspect
          const customAspectAngle = parseFloat(customAspectAngleInput.value) || 16;
          if (Math.abs(aspectAngle - customAspectAngle) < 0.01) {
            aspectName = `Aspect personnalisé (${aspectAngle}°)`;
          } else {
            aspectName = `Aspect de ${aspectAngle}°`;
          }
      }

      // Créer la cellule d'interprétation avec un bouton
      const interpretationCell = document.createElement('td');
      const interpretBtn = document.createElement('button');
      interpretBtn.className = 'future-aspect-interpret-btn';
      interpretBtn.textContent = 'Voir';
      interpretBtn.addEventListener('click', () => {
        // Convertir les noms de planètes en clés pour l'interprétation
        const transitPlanetKey = convertPlanetNameToKey(aspect.transitPlanet);
        const natalPlanetKey = convertPlanetNameToKey(aspect.natalPlanet);
        const aspectTypeKey = convertAspectAngleToType(aspectAngle);

        showTransitNatalInterpretation(
          transitPlanetKey,
          aspectTypeKey,
          natalPlanetKey
        );
      });
      interpretationCell.appendChild(interpretBtn);

      // Créer la ligne avec toutes les cellules
      row.innerHTML = `
        <td>${aspect.transitPlanet}</td>
        <td>${aspect.natalPlanet}</td>
        <td>${aspectName}</td>
        <td class="aspect-date">${dateStr}</td>
      `;

      // Ajouter la cellule d'interprétation
      row.appendChild(interpretationCell);

      futureAspectsBody.appendChild(row);
    });
  }

  // Fonction utilitaire pour convertir les noms de planètes en clés
  function convertPlanetNameToKey(planetName) {
    const planetMap = {
      'Soleil': 'sun',
      'Lune': 'moon',
      'Mercure': 'mercury',
      'Vénus': 'venus',
      'Mars': 'mars',
      'Jupiter': 'jupiter',
      'Saturne': 'saturn',
      'Uranus': 'uranus',
      'Neptune': 'neptune',
      'Pluton': 'pluto'
    };
    return planetMap[planetName] || planetName.toLowerCase();
  }

  // Fonction utilitaire pour convertir les angles d'aspects en types
  function convertAspectAngleToType(angle) {
    const aspectMap = {
      0: 'conjunction',
      30: 'semisextile',
      45: 'semisquare',
      60: 'sextile',
      90: 'square',
      120: 'trine',
      135: 'sesquisquare',
      150: 'quincunx',
      180: 'opposition'
    };
    return aspectMap[angle] || 'conjunction';
  }

  // Fonction pour calculer les aspects en mode proportionnel 30°
  function calculateProportionalAspects(transitPositions, birthPositions, orbValue, selectedAspectTypes) {
    const aspects = [];

    // Définir les aspects proportionnels (360° → 30°)
    const proportionalAspectTypes = [
      { name: 'conjunction', angle: 0, originalAngle: 0 },
      { name: 'sextile', angle: 5, originalAngle: 60 },
      { name: 'square', angle: 7.5, originalAngle: 90 },
      { name: 'trine', angle: 10, originalAngle: 120 },
      { name: 'opposition', angle: 15, originalAngle: 180 }
    ];

    // Filtrer les types d'aspects sélectionnés
    const typesToCheck = proportionalAspectTypes.filter(type =>
      selectedAspectTypes.includes(type.name)
    );

    // Planètes à vérifier
    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune'];

    planets.forEach(transitPlanet => {
      planets.forEach(natalPlanet => {
        // Calculer les positions proportionnelles (0-30°)
        const transitPos = getProportionalPosition(transitPositions[transitPlanet]);
        const natalPos = getProportionalPosition(birthPositions[natalPlanet]);

        // Calculer la séparation en mode proportionnel
        let separation = Math.abs(transitPos - natalPos);
        if (separation > 15) separation = 30 - separation; // Gérer le wrap-around à 30°

        // Vérifier chaque type d'aspect proportionnel
        typesToCheck.forEach(aspectType => {
          const aspectAngle = aspectType.angle;
          const orbDifference = Math.abs(separation - aspectAngle);

          // Vérifier si l'aspect est dans l'orb
          if (orbDifference <= orbValue) {
            aspects.push({
              transitPlanet: transitPlanet,
              natalPlanet: natalPlanet,
              aspectType: aspectType.name,
              aspectAngle: aspectAngle,
              originalAngle: aspectType.originalAngle,
              orb: orbDifference.toFixed(1),
              transitPosition: transitPos.toFixed(1),
              natalPosition: natalPos.toFixed(1),
              separation: separation.toFixed(1)
            });
          }
        });
      });
    });

    // Trier les aspects par orb (plus précis en premier)
    aspects.sort((a, b) => parseFloat(a.orb) - parseFloat(b.orb));

    return aspects;
  }

  // Fonction pour obtenir la position proportionnelle (0-30°)
  function getProportionalPosition(planetData) {
    if (!planetData) return 0;

    // Calculer la position absolue (0-360°)
    const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);
    const absolutePosition = signIndex * 30 + parseFloat(planetData.degree);

    // Convertir en position proportionnelle (0-30°)
    // 360° → 30°, donc diviser par 12
    return (absolutePosition / 12) % 30;
  }

  // Fonction pour calculer et afficher les aspects proportionnels
  function calculateAndDisplayProportionalAspects(birthPositions, transitPositions) {
    // Valeurs par défaut
    const orbValue = 1.0;
    const selectedAspectTypes = ['conjunction', 'opposition'];

    // Calculer les aspects
    const aspects = calculateProportionalAspects(transitPositions, birthPositions, orbValue, selectedAspectTypes);

    // Afficher les aspects
    displayZodiacAspects(aspects, 'proportional-zodiac-aspects');
  }

  // Fonction pour gérer les filtres des aspects proportionnels
  function initProportionalAspectsFilter() {
    const orbFilter = document.getElementById('proportional-orb-filter');
    const orbValue = document.getElementById('proportional-orb-value');
    const applyFilterBtn = document.getElementById('proportional-apply-filter');
    const aspectTypeCheckboxes = document.querySelectorAll('input[name="proportional-aspect-type"]');

    if (!orbFilter || !orbValue || !applyFilterBtn) return;

    // Mettre à jour l'affichage de la valeur de l'orb
    orbFilter.addEventListener('input', function() {
      orbValue.textContent = this.value + '°';
    });

    // Appliquer les filtres
    applyFilterBtn.addEventListener('click', function() {
      const orbVal = parseFloat(orbFilter.value);

      // Récupérer les types d'aspects sélectionnés
      const selectedTypes = [];
      aspectTypeCheckboxes.forEach(checkbox => {
        if (checkbox.checked) {
          selectedTypes.push(checkbox.value);
        }
      });

      // Récupérer les positions actuelles
      const birthDate = new Date(birthDateInput.value);
      const transitDate = new Date();
      const birthPositions = calculatePlanetaryPositions(birthDate);
      const transitPositions = calculatePlanetaryPositions(transitDate);

      // Calculer et afficher les aspects avec les nouveaux filtres
      const aspects = calculateProportionalAspects(transitPositions, birthPositions, orbVal, selectedTypes);
      displayZodiacAspects(aspects, 'proportional-zodiac-aspects');
    });
  }

  // Import events from CSV
  function importEventsFromCSV() {
    const csvData = csvInput.value.trim();

    if (!csvData) {
      alert('Veuillez coller des données CSV d\'abord.');
      return;
    }

    try {
      // Split CSV into lines
      const lines = csvData.split('\n');

      // Clear previous events
      importedEvents = [];
      eventsTableBody.innerHTML = '';
      eventsWithPositions.innerHTML = '';

      // Reset export button
      exportCsvBtn.disabled = true;
      hasCalculatedPositions = false;

      // Process each line
      lines.forEach((line, index) => {
        if (!line.trim()) return; // Skip empty lines

        // Split line by comma
        const parts = line.split(',').map(part => part.trim());

        // Check if we have at least date, time and description
        if (parts.length < 3) {
          console.warn(`Ligne ${index + 1} n'a pas assez de données: ${line}`);
          return;
        }

        const eventDate = parts[0];
        const eventTime = parts[1];
        const eventDescription = parts[2];

        // Parse date (expected format: DD/MM/YYYY)
        const dateParts = eventDate.split('/');
        if (dateParts.length !== 3) {
          console.warn(`Format de date invalide à la ligne ${index + 1}: ${eventDate}`);
          return;
        }

        // Parse time (expected format: HH:MM)
        const timeParts = eventTime.split(':');
        if (timeParts.length !== 2) {
          console.warn(`Format d'heure invalide à la ligne ${index + 1}: ${eventTime}`);
          return;
        }

        // Create date object (month is 0-based in JavaScript)
        const eventDateTime = new Date(
          parseInt(dateParts[2]), // Year
          parseInt(dateParts[1]) - 1, // Month (0-based)
          parseInt(dateParts[0]), // Day
          parseInt(timeParts[0]), // Hour
          parseInt(timeParts[1])  // Minute
        );

        // Check if date is valid
        if (isNaN(eventDateTime.getTime())) {
          console.warn(`Date/heure invalide à la ligne ${index + 1}: ${eventDate} ${eventTime}`);
          return;
        }

        // Add event to array
        const event = {
          id: Date.now() + index, // Generate unique ID
          date: eventDateTime,
          description: eventDescription,
          positions: null // Will be filled later
        };

        importedEvents.push(event);

        // Add event to table
        addEventToTable(event);
      });

      // Show message
      if (importedEvents.length === 0) {
        alert('Aucun événement valide trouvé dans les données CSV.');
      } else {
        alert(`${importedEvents.length} événements importés avec succès.`);
      }

    } catch (error) {
      console.error('Erreur lors de l\'importation CSV:', error);
      alert('Erreur lors de l\'importation des données CSV. Veuillez vérifier le format et réessayer.');
    }
  }

  // Add event to the table
  function addEventToTable(event) {
    const row = document.createElement('tr');
    row.dataset.eventId = event.id;

    // Format date and time
    const dateStr = `${event.date.getDate().toString().padStart(2, '0')}/${(event.date.getMonth() + 1).toString().padStart(2, '0')}/${event.date.getFullYear()}`;
    const timeStr = `${event.date.getHours().toString().padStart(2, '0')}:${event.date.getMinutes().toString().padStart(2, '0')}`;

    row.innerHTML = `
      <td>${dateStr}</td>
      <td>${timeStr}</td>
      <td>${event.description}</td>
      <td>
        <button type="button" class="event-action-btn" data-action="calculate" data-event-id="${event.id}">
          Calculer
        </button>
      </td>
    `;

    // Add event listener for the calculate button
    row.querySelector('button[data-action="calculate"]').addEventListener('click', function() {
      const eventId = this.dataset.eventId;
      calculatePositionsForEvent(eventId);
    });

    eventsTableBody.appendChild(row);
  }

  // Calculate positions for a specific event
  function calculatePositionsForEvent(eventId) {
    const event = importedEvents.find(e => e.id.toString() === eventId.toString());

    if (!event) {
      console.error(`Événement avec ID ${eventId} non trouvé.`);
      return;
    }

    // Calculate planetary positions
    const positions = calculatePlanetaryPositions(event.date);

    // Store positions with the event
    event.positions = positions;

    // Display the event with positions
    displayEventWithPositions(event);

    // Update button text
    const button = document.querySelector(`button[data-event-id="${eventId}"]`);
    if (button) {
      button.textContent = 'Recalculer';
    }

    // Enable export button if not already enabled
    if (!hasCalculatedPositions) {
      exportCsvBtn.disabled = false;
      hasCalculatedPositions = true;
    }

    // Update ruler view if it's currently displayed
    if (eventsRulerView.style.display === 'block') {
      // If the ruler for this event already exists, update it
      const existingRuler = document.querySelector(`.event-ruler[data-event-id="${eventId}"]`);
      if (existingRuler) {
        existingRuler.remove();
      }
      createEventRuler(event);
    }
  }

  // Add positions to all events
  function addPositionsToAllEvents() {
    if (importedEvents.length === 0) {
      alert('Veuillez d\'abord importer des événements.');
      return;
    }

    // Clear previous results
    eventsWithPositions.innerHTML = '';

    // Calculate positions for each event
    importedEvents.forEach(event => {
      // Calculate planetary positions
      const positions = calculatePlanetaryPositions(event.date);

      // Store positions with the event
      event.positions = positions;

      // Display the event with positions
      displayEventWithPositions(event);
    });

    // Update all buttons
    document.querySelectorAll('button[data-action="calculate"]').forEach(button => {
      button.textContent = 'Recalculer';
    });

    // Enable export button
    exportCsvBtn.disabled = false;
    hasCalculatedPositions = true;

    // Update ruler view if it's currently displayed
    if (eventsRulerView.style.display === 'block') {
      createRulerView();
    }

    alert(`Positions astrales calculées pour ${importedEvents.length} événements.`);
  }

  // Reset all events (remove all events including natal charts)
  function resetCalculatedEvents() {
    if (!importedEvents || importedEvents.length === 0) {
      alert('Aucun événement à réinitialiser.');
      return;
    }

    // Demander confirmation avant de supprimer tous les événements
    const confirmMessage = `Voulez-vous supprimer tous les ${importedEvents.length} événements ?`;
    if (!confirm(confirmMessage)) {
      return; // L'utilisateur a annulé
    }

    // Nombre d'événements supprimés
    const removedCount = importedEvents.length;

    // Vider le tableau des événements
    importedEvents = [];

    // Vider la table
    eventsTableBody.innerHTML = '';

    // Vider l'affichage des positions
    eventsWithPositions.innerHTML = '';

    // Mettre à jour la vue en règlette si elle est actuellement affichée
    if (eventsRulerView.style.display === 'block') {
      rulerContainer.innerHTML = '<div class="no-data">Aucun événement avec positions astrales à afficher.</div>';
    }

    // Mettre à jour l'indicateur hasCalculatedPositions
    hasCalculatedPositions = false;

    // Mettre à jour l'état du bouton d'exportation
    exportCsvBtn.disabled = true;

    // Afficher un message de confirmation
    alert(`${removedCount} événements ont été supprimés.`);
  }

  // Display event with its astral positions
  function displayEventWithPositions(event) {
    // Check if this event is already displayed
    const existingEvent = document.querySelector(`.event-with-positions[data-event-id="${event.id}"]`);
    if (existingEvent) {
      existingEvent.remove(); // Remove it to update
    }

    const eventElement = document.createElement('div');
    eventElement.className = 'event-with-positions';
    eventElement.dataset.eventId = event.id;

    // Format date and time
    const dateStr = `${event.date.getDate().toString().padStart(2, '0')}/${(event.date.getMonth() + 1).toString().padStart(2, '0')}/${event.date.getFullYear()}`;
    const timeStr = `${event.date.getHours().toString().padStart(2, '0')}:${event.date.getMinutes().toString().padStart(2, '0')}`;

    // Create header
    const header = document.createElement('div');
    header.className = 'event-header';

    const title = document.createElement('div');
    title.className = 'event-title';
    title.textContent = event.description;

    const datetime = document.createElement('div');
    datetime.className = 'event-datetime';
    datetime.textContent = `${dateStr} ${timeStr}`;

    header.appendChild(title);
    header.appendChild(datetime);

    // Create positions display
    const positionsElement = document.createElement('div');
    positionsElement.className = 'event-positions';

    const planets = [
      { key: 'sun', name: 'Soleil' },
      { key: 'moon', name: 'Lune' },
      { key: 'mercury', name: 'Mercure' },
      { key: 'venus', name: 'Vénus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturne' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    planets.forEach(planet => {
      const planetData = event.positions[planet.key];
      const planetElement = document.createElement('div');
      planetElement.className = 'event-planet';

      const planetSymbol = PLANET_SYMBOLS[planet.name === 'Soleil' ? 'Sun' :
                                         planet.name === 'Lune' ? 'Moon' :
                                         planet.name === 'Mercure' ? 'Mercury' :
                                         planet.name === 'Vénus' ? 'Venus' :
                                         planet.name === 'Saturne' ? 'Saturn' :
                                         planet.name] || '';

      planetElement.innerHTML = `
        <span class="event-planet-symbol">${planetSymbol}</span>
        ${planet.name}: ${planetData.sign} ${planetData.degree}°
      `;

      positionsElement.appendChild(planetElement);
    });

    // Assemble the event element
    eventElement.appendChild(header);
    eventElement.appendChild(positionsElement);

    // Add to the container
    eventsWithPositions.appendChild(eventElement);

    // Scroll to the event
    eventElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }

  // Toggle between classic view and ruler view
  function toggleEventView() {
    if (eventsRulerView.style.display === 'none') {
      // Switch to ruler view
      eventsWithPositions.style.display = 'none';
      eventsRulerView.style.display = 'block';
      toggleViewBtn.textContent = 'Afficher en liste';
      toggleViewBtn.classList.add('active');

      // Create ruler view if it doesn't exist yet
      if (rulerContainer.children.length === 0 && hasCalculatedPositions) {
        createRulerView();
      }
    } else {
      // Switch to classic view
      eventsWithPositions.style.display = 'flex';
      eventsRulerView.style.display = 'none';
      toggleViewBtn.textContent = 'Afficher en règlette';
      toggleViewBtn.classList.remove('active');
    }
  }

  // Create ruler view for all events with positions
  function createRulerView() {
    // Clear previous rulers
    rulerContainer.innerHTML = '';

    // Filter events that have positions calculated
    const eventsWithPositionsData = importedEvents.filter(event => event.positions !== null);

    if (eventsWithPositionsData.length === 0) {
      rulerContainer.innerHTML = '<div class="no-data">Aucun événement avec positions astrales à afficher.</div>';
      return;
    }

    // Create a ruler for each event
    eventsWithPositionsData.forEach(event => {
      createEventRuler(event);
    });
  }

  // Toggle between 360° and 30° ruler modes
  function toggleRulerMode() {
    // Toggle mode
    if (rulerMode === '360') {
      rulerMode = '30';
      document.getElementById('toggle-ruler-mode-btn').textContent = 'Mode 360°';
      document.getElementById('toggle-ruler-mode-btn').classList.add('active');
      document.getElementById('ruler-mode-text').textContent = '30°';
    } else {
      rulerMode = '360';
      document.getElementById('toggle-ruler-mode-btn').textContent = 'Mode 30°';
      document.getElementById('toggle-ruler-mode-btn').classList.remove('active');
      document.getElementById('ruler-mode-text').textContent = '360°';
    }

    // Recreate the ruler view with the new mode
    if (eventsRulerView.style.display === 'block') {
      createRulerView();
    }
  }

  // Create a ruler for a single event
  function createEventRuler(event) {
    // Create ruler container
    const rulerElement = document.createElement('div');
    rulerElement.className = 'event-ruler';
    if (rulerMode === '30') {
      rulerElement.classList.add('ruler-30deg');
    }
    rulerElement.dataset.eventId = event.id;

    // Format date and time
    const dateStr = `${event.date.getDate().toString().padStart(2, '0')}/${(event.date.getMonth() + 1).toString().padStart(2, '0')}/${event.date.getFullYear()}`;
    const timeStr = `${event.date.getHours().toString().padStart(2, '0')}:${event.date.getMinutes().toString().padStart(2, '0')}`;

    // Create header
    const header = document.createElement('div');
    header.className = 'event-ruler-header';
    header.innerHTML = `
      <div class="event-ruler-title">${event.description}</div>
      <div class="event-ruler-date">${dateStr} ${timeStr}</div>
    `;

    // Create zodiac signs container (only for 360° mode)
    const zodiacContainer = document.createElement('div');
    zodiacContainer.className = 'zodiac-signs-container';

    if (rulerMode === '360') {
      // Add zodiac sign labels
      const signs = ['Bélier', 'Taureau', 'Gémeaux', 'Cancer',
                    'Lion', 'Vierge', 'Balance', 'Scorpion',
                    'Sagittaire', 'Capricorne', 'Verseau', 'Poissons'];

      signs.forEach((sign, index) => {
        const signLabel = document.createElement('div');
        signLabel.className = 'zodiac-sign-label';
        signLabel.textContent = sign;
        signLabel.style.left = (index * 8.333) + '%';
        zodiacContainer.appendChild(signLabel);
      });
    }

    // Create ruler scale
    const rulerScale = document.createElement('div');
    rulerScale.className = 'ruler-scale';

    // Create ruler marks
    const rulerMarks = document.createElement('div');
    rulerMarks.className = 'ruler-marks';

    if (rulerMode === '360') {
      // Add marks for every degree (360 marks)
      for (let i = 0; i < 360; i += 1) {
        const mark = document.createElement('div');
        mark.className = 'ruler-mark';

        // Add label for every 30 degrees (beginning of each sign)
        if (i % 30 === 0) {
          // Add degree label
          const degreeLabel = document.createElement('div');
          degreeLabel.className = 'ruler-mark-label';
          degreeLabel.textContent = i + '°';
          mark.appendChild(degreeLabel);
        }

        rulerMarks.appendChild(mark);
      }
    } else {
      // Mode 30° - Add marks for every degree (30 marks)
      for (let i = 0; i < 30; i += 1) {
        const mark = document.createElement('div');
        mark.className = 'ruler-mark';

        // Add label for every 5 degrees
        if (i % 5 === 0) {
          // Add degree label
          const degreeLabel = document.createElement('div');
          degreeLabel.className = 'ruler-mark-label';
          degreeLabel.textContent = i + '°';
          mark.appendChild(degreeLabel);
        }

        rulerMarks.appendChild(mark);
      }
    }

    rulerScale.appendChild(rulerMarks);

    // Add planet markers
    const planets = [
      { key: 'sun', name: 'Soleil', emoji: '☉' },
      { key: 'moon', name: 'Lune', emoji: '☽' },
      { key: 'mercury', name: 'Mercure', emoji: '☿' },
      { key: 'venus', name: 'Vénus', emoji: '♀' },
      { key: 'mars', name: 'Mars', emoji: '♂' },
      { key: 'jupiter', name: 'Jupiter', emoji: '♃' },
      { key: 'saturn', name: 'Saturne', emoji: '♄' },
      { key: 'uranus', name: 'Uranus', emoji: '♅' },
      { key: 'neptune', name: 'Neptune', emoji: '♆' }
    ];

    planets.forEach(planet => {
      if (event.positions[planet.key]) {
        const position = event.positions[planet.key].siderealLongitude;
        const marker = document.createElement('div');
        marker.className = 'planet-marker';

        if (rulerMode === '360') {
          // Position sur la règlette 360°
          marker.style.left = `${(position / 360) * 100}%`;
          marker.innerHTML = `${planet.emoji}<span class="planet-name">${planet.name}</span>`;
        } else {
          // Position sur la règlette 30° (basée sur le degré dans le signe)
          const degreeInSign = position % 30;
          marker.style.left = `${(degreeInSign / 30) * 100}%`;

          // Déterminer le signe du zodiaque
          const signIndex = Math.floor(position / 30) % 12;
          const signs = ['Bélier', 'Taureau', 'Gémeaux', 'Cancer',
                        'Lion', 'Vierge', 'Balance', 'Scorpion',
                        'Sagittaire', 'Capricorne', 'Verseau', 'Poissons'];
          const sign = signs[signIndex];

          // Afficher le nom de la planète avec le signe et le degré exact
          const degreeFormatted = degreeInSign.toFixed(1);
          marker.innerHTML = `${planet.emoji}<span class="planet-name">${planet.name} (${sign} ${degreeFormatted}°)</span>`;
        }
        rulerScale.appendChild(marker);
      }
    });

    // Assemble the ruler
    rulerElement.appendChild(header);
    rulerElement.appendChild(zodiacContainer);
    rulerElement.appendChild(rulerScale);

    // Add to the container
    rulerContainer.appendChild(rulerElement);
  }

  // Export events with positions to CSV
  function exportEventsToCSV() {
    if (importedEvents.length === 0 || !hasCalculatedPositions) {
      alert('Aucun événement avec positions astrales à exporter.');
      return;
    }

    // Filter events that have positions calculated
    const eventsWithPositionsData = importedEvents.filter(event => event.positions !== null);

    if (eventsWithPositionsData.length === 0) {
      alert('Aucun événement avec positions astrales à exporter.');
      return;
    }

    // Create CSV content
    let csvContent = 'date,heure,description,';

    // Add planet headers
    const planets = [
      { key: 'sun', name: 'Soleil' },
      { key: 'moon', name: 'Lune' },
      { key: 'mercury', name: 'Mercure' },
      { key: 'venus', name: 'Vénus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturne' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    // Add planet headers
    planets.forEach(planet => {
      csvContent += `${planet.name}_signe,${planet.name}_degré,`;
    });

    // Remove trailing comma and add newline
    csvContent = csvContent.slice(0, -1) + '\n';

    // Add data rows
    eventsWithPositionsData.forEach(event => {
      // Format date and time
      const dateStr = `${event.date.getDate().toString().padStart(2, '0')}/${(event.date.getMonth() + 1).toString().padStart(2, '0')}/${event.date.getFullYear()}`;
      const timeStr = `${event.date.getHours().toString().padStart(2, '0')}:${event.date.getMinutes().toString().padStart(2, '0')}`;

      // Start row with date, time, description
      csvContent += `${dateStr},${timeStr},"${event.description.replace(/"/g, '""')}",`;

      // Add planet positions
      planets.forEach(planet => {
        const planetData = event.positions[planet.key];
        csvContent += `"${planetData.sign}",${planetData.degree},`;
      });

      // Remove trailing comma and add newline
      csvContent = csvContent.slice(0, -1) + '\n';
    });

    // Create a Blob with the CSV content
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // Create a download link
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    // Set link properties
    link.setAttribute('href', url);
    link.setAttribute('download', 'evenements_positions_astrales.csv');
    link.style.visibility = 'hidden';

    // Add link to document, click it, and remove it
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // Calculate houses based on selected system
  function calculateHouses() {
    // Get current date and time from inputs
    const date = new Date(birthDateInput.value);
    date.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );

    // Get location coordinates
    const latitude = parseFloat(latitudeInput.value) || 48.0667;
    const longitude = parseFloat(longitudeInput.value) || -0.7667;

    // Get calculation options
    const houseSystem = houseSystemSelect.value;
    const isSidereal = siderealModeRadio.checked;
    const isCounterClockwise = counterClockwiseModeRadio.checked;
    const invertHouses = invertHousesCheckbox.checked;

    // Calculate houses based on selected system
    let houses;
    switch (houseSystem) {
      case 'whole-sign':
        houses = calculateWholeSignHouses(date, latitude, longitude, isSidereal);
        break;
      case 'equal':
        houses = calculateEqualHouses(date, latitude, longitude, isSidereal);
        break;
      case 'porphyry':
      default:
        houses = calculatePorphyryHouses(date, latitude, longitude, isSidereal, isCounterClockwise);
        break;
    }

    // Apply house inversion if selected
    if (invertHouses) {
      houses = invertHousesArray(houses);
    }

    // Display houses
    displayHouses(houses, isSidereal, houseSystem);

    // Draw houses chart
    drawHousesChart(houses, isCounterClockwise, houseSystem);

    // Display the date used for calculation
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    housesDateDisplay.textContent = `${day}/${month}/${year} ${hours}:${minutes}`;
  }

  // Function to invert houses (1→7, 2→8, etc.)
  function invertHousesArray(houses) {
    const invertedHouses = [];
    for (let i = 0; i < 12; i++) {
      // Map house i to house (i+6)%12
      invertedHouses[i] = houses[(i + 6) % 12];
    }
    return invertedHouses;
  }

  // Calculate houses using Porphyry system
  function calculatePorphyryHouses(date, latitude, longitude, isSidereal = false, isCounterClockwise = false) {
    // Calculate local sidereal time (LST)
    const lst = calculateLocalSiderealTime(date, longitude);

    // Calculate ascendant (1st house cusp)
    let ascendant = calculateAscendant(lst, latitude);

    // Calculate midheaven (10th house cusp)
    let midheaven = calculateMidheaven(lst);

    // Apply sidereal correction if needed
    if (isSidereal) {
      const ayanamsa = getAyanamsa(date);
      ascendant = normalizeAngle(ascendant - ayanamsa);
      midheaven = normalizeAngle(midheaven - ayanamsa);
    }

    // Calculate descendant (7th house cusp) - opposite to ascendant
    const descendant = normalizeAngle(ascendant + 180);

    // Calculate imum coeli (4th house cusp) - opposite to midheaven
    const imumCoeli = normalizeAngle(midheaven + 180);

    // Calculate intermediate house cusps using Porphyry system
    // Porphyry divides the arcs between the angles equally

    // Calculate the arc from Ascendant to Midheaven (houses 12, 11)
    let arc1 = midheaven - ascendant;
    if (arc1 < 0) arc1 += 360;

    // Calculate the arc from Midheaven to Descendant (houses 9, 8)
    let arc2 = descendant - midheaven;
    if (arc2 < 0) arc2 += 360;

    // Calculate the arc from Descendant to Imum Coeli (houses 6, 5)
    let arc3 = imumCoeli - descendant;
    if (arc3 < 0) arc3 += 360;

    // Calculate the arc from Imum Coeli to Ascendant (houses 3, 2)
    let arc4 = ascendant - imumCoeli;
    if (arc4 < 0) arc4 += 360;

    // Calculate intermediate house cusps - ensuring equal division
    // Houses 11 and 12 (between Ascendant and Midheaven)
    const house12 = normalizeAngle(ascendant + arc1 / 3);
    const house11 = normalizeAngle(ascendant + 2 * arc1 / 3);

    // Houses 8 and 9 (between Midheaven and Descendant)
    const house9 = normalizeAngle(midheaven + arc2 / 3);
    const house8 = normalizeAngle(midheaven + 2 * arc2 / 3);

    // Houses 5 and 6 (between Descendant and Imum Coeli)
    const house6 = normalizeAngle(descendant + arc3 / 3);
    const house5 = normalizeAngle(descendant + 2 * arc3 / 3);

    // Houses 2 and 3 (between Imum Coeli and Ascendant)
    const house3 = normalizeAngle(imumCoeli + arc4 / 3);
    const house2 = normalizeAngle(imumCoeli + 2 * arc4 / 3);

    // Get all house cusps
    let houses = [
      ascendant,   // House 1
      house2,      // House 2
      house3,      // House 3
      imumCoeli,   // House 4
      house5,      // House 5
      house6,      // House 6
      descendant,  // House 7
      house8,      // House 8
      house9,      // House 9
      midheaven,   // House 10
      house11,     // House 11
      house12      // House 12
    ];

    // Note: Sidereal correction is already applied to ascendant and midheaven

    // Reverse the order if counter-clockwise is selected
    if (isCounterClockwise) {
      // We need to keep the angles the same but reverse the house order
      // This is a simplified approach - in a real implementation,
      // the calculation would be more complex
      const newHouses = [];
      for (let i = 0; i < 12; i++) {
        newHouses[i] = houses[(12 - i) % 12];
      }
      houses = newHouses;
    }

    return houses;
  }

  // Calculate Whole Sign Houses
  function calculateWholeSignHouses(date, latitude, longitude, isSidereal = false) {
    // Calculate local sidereal time (LST)
    const lst = calculateLocalSiderealTime(date, longitude);

    // Calculate ascendant (1st house cusp)
    const ascendant = calculateAscendant(lst, latitude);

    // Apply sidereal correction if needed
    let adjustedAscendant = ascendant;
    if (isSidereal) {
      const ayanamsa = getAyanamsa(date);
      adjustedAscendant = normalizeAngle(ascendant - ayanamsa);
    }

    // In Whole Sign Houses, the 1st house begins at 0° of the sign containing the ascendant
    const ascSign = Math.floor(adjustedAscendant / 30);
    const firstHouseCusp = ascSign * 30; // 0° of the sign

    // All other houses start at 0° of subsequent signs
    const houses = [];
    for (let i = 0; i < 12; i++) {
      houses.push(normalizeAngle(firstHouseCusp + i * 30));
    }

    return houses;
  }

  // Calculate Equal Houses
  function calculateEqualHouses(date, latitude, longitude, isSidereal = false) {
    // Calculate local sidereal time (LST)
    const lst = calculateLocalSiderealTime(date, longitude);

    // Calculate ascendant (1st house cusp)
    let ascendant = calculateAscendant(lst, latitude);

    // Apply sidereal correction if needed
    if (isSidereal) {
      const ayanamsa = getAyanamsa(date);
      ascendant = normalizeAngle(ascendant - ayanamsa);
    }

    // In Equal Houses, each house is exactly 30° wide, starting from the ascendant
    const houses = [];
    for (let i = 0; i < 12; i++) {
      houses.push(normalizeAngle(ascendant + i * 30));
    }

    return houses;
  }

  // Calculate Local Sidereal Time (LST)
  function calculateLocalSiderealTime(date, longitude) {
    // Calculate Julian Day
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    let jd = 367 * year - Math.floor(7 * (year + Math.floor((month + 9) / 12)) / 4) +
             Math.floor(275 * month / 9) + day + 1721013.5;

    // Add time of day
    jd += (date.getUTCHours() + date.getUTCMinutes() / 60.0 + date.getUTCSeconds() / 3600.0) / 24.0;

    // Calculate Greenwich Sidereal Time (GST)
    const T = (jd - 2451545.0) / 36525;
    let theta = 280.46061837 + 360.98564736629 * (jd - 2451545.0) + 0.000387933 * T * T - T * T * T / 38710000.0;
    theta = normalizeAngle(theta);

    // Convert to Local Sidereal Time
    const lst = theta + longitude;
    return normalizeAngle(lst);
  }

  // Calculate Ascendant
  function calculateAscendant(lst, latitude) {
    // Convert to radians
    const latRad = latitude * DEG_TO_RAD;

    // Calculate Ascendant
    const tanAsc = -Math.cos(lst * DEG_TO_RAD) / (Math.sin(lst * DEG_TO_RAD) * Math.cos(23.4393 * DEG_TO_RAD) + Math.tan(latRad) * Math.sin(23.4393 * DEG_TO_RAD));
    let ascendant = Math.atan(tanAsc) * RAD_TO_DEG;

    // Adjust quadrant
    if (Math.cos(lst * DEG_TO_RAD) > 0) {
      ascendant += 180;
    }

    return normalizeAngle(ascendant);
  }

  // Calculate Midheaven (MC)
  function calculateMidheaven(lst) {
    // Convert LST to degrees and normalize
    let mc = lst - 90;
    return normalizeAngle(mc);
  }

  // Display houses in the list
  function displayHouses(houses, isSidereal = false, houseSystem = 'porphyry') {
    const isInverted = invertHousesCheckbox.checked;
    // Clear previous houses
    housesList.innerHTML = '';

    // Get system name for display
    let systemName = '';
    switch (houseSystem) {
      case 'whole-sign':
        systemName = 'Signes Entiers';
        break;
      case 'equal':
        systemName = 'Maisons Égales';
        break;
      case 'porphyry':
      default:
        systemName = 'Porphyre';
        break;
    }

    // Display each house
    houses.forEach((cusp, index) => {
      const houseNumber = index + 1;

      // Calculate zodiac sign and degree
      const sign = Math.floor(cusp / 30);
      const degree = cusp % 30;

      // Create house item
      const houseItem = document.createElement('div');
      houseItem.className = 'house-item';

      // Add system and inversion indicators for the first house
      let indicators = [];
      if (index === 0) {
        indicators.push(`<div class="house-system">(Système: ${systemName})</div>`);
      }
      if (isSidereal) {
        indicators.push('<div class="house-mode">(Sidéral)</div>');
      }
      if (isInverted && index === 0) {
        indicators.push('<div class="house-inverted">(Maisons inversées)</div>');
      }

      const indicatorsHtml = indicators.join('');

      houseItem.innerHTML = `
        <div class="house-number">Maison ${houseNumber}</div>
        <div class="house-sign">
          <span>${ZODIAC_SIGNS[sign]}</span>
          <span class="house-cusp">${degree.toFixed(2)}°</span>
        </div>
        ${indicatorsHtml}
      `;

      housesList.appendChild(houseItem);
    });
  }

  // Draw houses chart
  function drawHousesChart(houses, isCounterClockwise = false, houseSystem = 'porphyry') {
    const isInverted = invertHousesCheckbox.checked;
    const ctx = housesCanvas.getContext('2d');
    const width = housesCanvas.width = housesCanvas.offsetWidth;
    const height = housesCanvas.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) - 20;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw outer circle (zodiac wheel)
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw inner circle (houses wheel)
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.7, 0, 2 * Math.PI);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw zodiac signs
    for (let i = 0; i < 12; i++) {
      // Adjust angle based on direction
      const baseAngle = isCounterClockwise ? (i * -30 - 90) : (i * 30 - 90);
      const angle = baseAngle * Math.PI / 180;

      // Draw sign division lines
      ctx.beginPath();
      ctx.moveTo(
        centerX + radius * 0.7 * Math.cos(angle),
        centerY + radius * 0.7 * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle)
      );
      ctx.strokeStyle = '#999';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Draw sign symbol
      const symbolOffset = isCounterClockwise ? -15 : 15;
      const symbolAngle = (baseAngle + symbolOffset) * Math.PI / 180;
      const symbolX = centerX + radius * 0.85 * Math.cos(symbolAngle);
      const symbolY = centerY + radius * 0.85 * Math.sin(symbolAngle);

      ctx.font = '14px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(ZODIAC_SIGNS[i].substring(0, 3), symbolX, symbolY);
    }

    // Get system name for display
    let systemName = '';
    switch (houseSystem) {
      case 'whole-sign':
        systemName = 'Signes Entiers';
        break;
      case 'equal':
        systemName = 'Maisons Égales';
        break;
      case 'porphyry':
      default:
        systemName = 'Porphyre';
        break;
    }

    // Draw system, direction and inversion indicators
    ctx.font = '12px Arial';
    ctx.fillStyle = '#666';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    let indicatorText = `Système: ${systemName} - ${isCounterClockwise ? '↺ Anti-horaire' : '↻ Horaire'}`;
    if (isInverted) {
      indicatorText += ' - Maisons inversées';
      ctx.fillStyle = '#e74c3c'; // Red color for inversion warning
    }

    ctx.fillText(indicatorText, centerX, centerY - radius - 15);

    // Draw house cusps
    houses.forEach((cusp, index) => {
      const angle = (cusp - 90) * Math.PI / 180;

      // Draw house cusp line
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(
        centerX + radius * 0.7 * Math.cos(angle),
        centerY + radius * 0.7 * Math.sin(angle)
      );
      ctx.strokeStyle = '#0071e3';
      ctx.lineWidth = 1.5;
      ctx.stroke();

      // Draw house number
      const numberOffset = isCounterClockwise ? -15 : 15;
      const numberAngle = (cusp - 90 + numberOffset) * Math.PI / 180;
      const numberX = centerX + radius * 0.4 * Math.cos(numberAngle);
      const numberY = centerY + radius * 0.4 * Math.sin(numberAngle);

      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = '#0071e3';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText((index + 1).toString(), numberX, numberY);
    });

    // Draw Ascendant (ASC) and Midheaven (MC) labels
    const ascAngle = (houses[0] - 90) * Math.PI / 180;
    const mcAngle = (houses[9] - 90) * Math.PI / 180;

    // ASC label
    ctx.font = 'bold 14px Arial';
    ctx.fillStyle = '#e74c3c';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('ASC',
      centerX + radius * 0.75 * Math.cos(ascAngle),
      centerY + radius * 0.75 * Math.sin(ascAngle)
    );

    // MC label
    ctx.fillText('MC',
      centerX + radius * 0.75 * Math.cos(mcAngle),
      centerY + radius * 0.75 * Math.sin(mcAngle)
    );
  }

  // Initialize the chart on load
  calculateChart();

  // Calculate houses on load
  calculateHouses();

  // Calculate astrological weather forecast
  function calculateWeatherForecast(customStartDate = null) {
    // Get the birth date
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );

    // Calculate birth positions
    const birthPositions = calculatePlanetaryPositions(birthDate);

    // Get the start date for transit
    const startDate = customStartDate || new Date();
    startDate.setHours(0, 0, 0, 0);

    // Get the forecast period in days
    const forecastDays = parseInt(weatherPeriodInput.value) || 7;

    // Calculate end date
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + forecastDays - 1); // -1 because we include the start date

    // Get the master planet
    const masterPlanet = masterPlanetSelect.value;

    // Get active aspects from standard and custom aspects
    const positiveAspects = [];
    const negativeAspects = [];

    // Add active standard aspects
    standardAspects.forEach(aspect => {
      if (aspect.active !== false) {
        if (aspect.type === 'positive') {
          positiveAspects.push(parseFloat(aspect.id));
        } else {
          negativeAspects.push(parseFloat(aspect.id));
        }
      }
    });

    // Add active custom aspects
    customAspects.forEach(aspect => {
      if (aspect.active !== false) {
        if (aspect.type === 'positive') {
          positiveAspects.push(parseFloat(aspect.id));
        } else {
          negativeAspects.push(parseFloat(aspect.id));
        }
      }
    });

    // Get selected icon logic
    const iconLogic = document.querySelector('input[name="icon-logic"]:checked').value;

    // Log aspects for debugging
    console.log('Aspects positifs:', positiveAspects);
    console.log('Aspects négatifs:', negativeAspects);

    // Initialize daily aspects array
    const dailyAspects = [];

    // Transit planets to consider
    const transitPlanets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn'];

    // Natal planets to consider
    const natalPlanets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn'];

    // Loop through each day in the forecast period
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      // Create a new date object to avoid reference issues
      const dayDate = new Date(currentDate);

      // Initialize aspects for this day
      const dayAspects = {
        date: dayDate,
        aspects: [],
        total: 0,
        positive: 0,
        negative: 0,
        master: 0,
        masterNatal: 0,    // Nouveau compteur pour les aspects sur l'astre maître natal
        masterTransit: 0,  // Nouveau compteur pour les aspects de l'astre maître en transit
        weatherType: 'cloudy' // Default weather type
      };

      // Calculate transit positions for the current date
      const transitPositions = calculatePlanetaryPositions(new Date(currentDate));

      // Check for aspects between transit and natal planets
      transitPlanets.forEach(transitPlanet => {
        natalPlanets.forEach(natalPlanet => {
          // Skip if both planets are the same
          if (transitPlanet === natalPlanet) return;

          const transitPos = transitPositions[transitPlanet].siderealLongitude;
          const natalPos = birthPositions[natalPlanet].siderealLongitude;

          // Calculate the angular separation
          let separation = Math.abs(transitPos - natalPos);
          if (separation > 180) separation = 360 - separation;

          // Get the custom orb value or use default 2°
          const customOrb = parseFloat(aspectOrbInput.value) || 2;

          // Check for aspects with the custom orb
          const checkAspects = [...positiveAspects, ...negativeAspects];

          checkAspects.forEach(aspectAngle => {
            const orb = Math.abs(separation - aspectAngle);
            if (orb <= customOrb) {
              // This is an aspect
              const isPositive = positiveAspects.includes(aspectAngle);

              // Vérifier si l'astre maître est impliqué (en natal ou en transit)
              const isMasterAspectNatal = (natalPlanet === masterPlanet);
              const isMasterAspectTransit = (transitPlanet === masterPlanet);
              const isMasterAspect = isMasterAspectNatal || isMasterAspectTransit;

              // Add to day's aspects
              dayAspects.aspects.push({
                transitPlanet,
                natalPlanet,
                aspectAngle,
                isPositive,
                isMasterAspect,
                isMasterAspectNatal,
                isMasterAspectTransit
              });

              // Update day's statistics
              dayAspects.total++;
              if (isPositive) dayAspects.positive++;
              else dayAspects.negative++;

              // Mettre à jour les statistiques de l'astre maître
              if (isMasterAspect) dayAspects.master++;
              if (isMasterAspectNatal) dayAspects.masterNatal++;
              if (isMasterAspectTransit) dayAspects.masterTransit++;
            }
          });
        });
      });

      // Get threshold values
      const veryFavorableThreshold = parseInt(thresholdVeryFavorable.value) || 3;
      const favorableMinThreshold = parseInt(thresholdFavorable.value) || 1;
      const favorableMaxThreshold = parseInt(thresholdFavorableMax.value) || 2;
      const difficultMinThreshold = parseInt(thresholdDifficult.value) || 1;
      const difficultMaxThreshold = parseInt(thresholdDifficultMax.value) || 2;
      const veryDifficultThreshold = parseInt(thresholdVeryDifficult.value) || 3;

      // Determine weather type based on selected icon logic
      switch (iconLogic) {
        case 'ratio':
          // Based on ratio of positive to negative aspects
          const ratio = dayAspects.total > 0 ? dayAspects.positive / dayAspects.total : 0.5;
          if (ratio >= 0.8) {
            dayAspects.weatherType = 'sunny';
          } else if (ratio >= 0.6) {
            dayAspects.weatherType = 'partly-cloudy';
          } else if (ratio >= 0.4) {
            dayAspects.weatherType = 'cloudy';
          } else if (ratio >= 0.2) {
            dayAspects.weatherType = 'rainy';
          } else {
            dayAspects.weatherType = 'stormy';
          }
          break;

        case 'master':
          // Based on aspects to master planet
          if (dayAspects.master > 0) {
            const masterPositive = dayAspects.aspects.filter(a => a.isMasterAspect && a.isPositive).length;
            const masterNegative = dayAspects.master - masterPositive;

            if (masterPositive > masterNegative) {
              dayAspects.weatherType = masterPositive >= veryFavorableThreshold ? 'sunny' : 'partly-cloudy';
            } else if (masterNegative > masterPositive) {
              dayAspects.weatherType = masterNegative >= veryDifficultThreshold ? 'stormy' : 'rainy';
            } else {
              dayAspects.weatherType = 'cloudy';
            }
          } else {
            dayAspects.weatherType = 'cloudy'; // No master aspects
          }
          break;

        case 'default':
        default:
          // Default logic based on positive vs negative count with custom thresholds
          if (dayAspects.positive > dayAspects.negative) {
            if (dayAspects.positive >= veryFavorableThreshold) {
              dayAspects.weatherType = 'sunny';
            } else if (dayAspects.positive >= favorableMinThreshold && dayAspects.positive <= favorableMaxThreshold) {
              dayAspects.weatherType = 'partly-cloudy';
            } else {
              dayAspects.weatherType = 'cloudy';
            }
          } else if (dayAspects.negative > dayAspects.positive) {
            if (dayAspects.negative >= veryDifficultThreshold) {
              dayAspects.weatherType = 'stormy';
            } else if (dayAspects.negative >= difficultMinThreshold && dayAspects.negative <= difficultMaxThreshold) {
              dayAspects.weatherType = 'rainy';
            } else {
              dayAspects.weatherType = 'cloudy';
            }
          } else {
            dayAspects.weatherType = 'cloudy';
          }
          break;
      }

      // Add this day to the array
      dailyAspects.push(dayAspects);

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Calculate overall statistics
    const totalAspects = dailyAspects.reduce((sum, day) => sum + day.total, 0);
    const positiveAspectsTotal = dailyAspects.reduce((sum, day) => sum + day.positive, 0);
    const negativeAspectsTotal = dailyAspects.reduce((sum, day) => sum + day.negative, 0);
    const masterAspectsTotal = dailyAspects.reduce((sum, day) => sum + day.master, 0);
    const masterNatalAspectsTotal = dailyAspects.reduce((sum, day) => sum + day.masterNatal, 0);
    const masterTransitAspectsTotal = dailyAspects.reduce((sum, day) => sum + day.masterTransit, 0);

    // Update the UI
    totalAspectsCount.textContent = totalAspects;
    positiveAspectsCount.textContent = positiveAspectsTotal;
    negativeAspectsCount.textContent = negativeAspectsTotal;
    masterAspectsCount.textContent = masterAspectsTotal;
    document.getElementById('master-natal-aspects-count').textContent = masterNatalAspectsTotal;
    document.getElementById('master-transit-aspects-count').textContent = masterTransitAspectsTotal;

    // Update period display
    document.getElementById('start-date-display').textContent = formatDate(startDate);
    document.getElementById('end-date-display').textContent = formatDate(endDate);

    // Mettre à jour la variable globale pour les aspects quotidiens
    globalDailyAspects = dailyAspects;

    // Generate daily weather cards
    generateDailyWeatherCards(dailyAspects);

    // Generate summary text
    generateWeatherSummary(totalAspects, positiveAspectsTotal, negativeAspectsTotal, masterAspectsTotal, masterNatalAspectsTotal, masterTransitAspectsTotal, masterPlanet, forecastDays);

    // Activer le bouton Google Calendar
    const exportToGCalBtn = document.getElementById('export-to-gcal-btn');
    if (exportToGCalBtn) {
      exportToGCalBtn.disabled = false;
      exportToGCalBtn.style.opacity = '1';
      exportToGCalBtn.style.cursor = 'pointer';
    }

    // Stocker les données météo pour l'exportation vers Google Calendar
    currentWeatherData = {
      days: dailyAspects.map(day => ({
        date: formatDate(day.date),
        weatherType: day.weatherType,
        totalAspects: day.total,
        positiveAspects: day.positive,
        negativeAspects: day.negative,
        masterAspects: day.master,
        masterNatalAspects: day.masterNatal,
        masterTransitAspects: day.masterTransit,
        aspects: day.aspects.map(aspect => ({
          transitPlanet: getPlanetFrenchName(aspect.transitPlanet),
          natalPlanet: getPlanetFrenchName(aspect.natalPlanet),
          aspectName: aspect.aspectName
        }))
      }))
    };
  }

  // Generate daily weather cards
  function generateDailyWeatherCards(dailyAspects) {
    const dailyWeatherGrid = document.getElementById('daily-weather-grid');
    dailyWeatherGrid.innerHTML = '';

    // Days of the week in French
    const daysOfWeek = ['DIM.', 'LUN.', 'MAR.', 'MER.', 'JEU.', 'VEN.', 'SAM.'];

    // Vérifier si nous sommes en mode "basé sur l'astre maître"
    const isInMasterMode = document.querySelector('input[name="icon-logic"]:checked').value === 'master';

    // Trouver le jour avec le plus d'aspects à l'astre maître
    let maxMasterAspects = 0;
    if (isInMasterMode) {
      maxMasterAspects = Math.max(...dailyAspects.map(day => day.master));
    }

    // Create a card for each day
    dailyAspects.forEach(day => {
      const dayOfWeek = daysOfWeek[day.date.getDay()];
      const dayDate = day.date.getDate();

      // Create the card element
      const card = document.createElement('div');
      card.className = 'daily-weather-card';

      // Weather icons based on weather type
      const weatherIcons = {
        'sunny': '☀️',
        'partly-cloudy': '⛅',
        'cloudy': '☁️',
        'rainy': '🌧️',
        'stormy': '⛈️'
      };

      // Déterminer si ce jour a le maximum d'aspects à l'astre maître
      const hasMostMasterAspects = isInMasterMode && day.master > 0 && day.master === maxMasterAspects;

      // Create card content
      card.innerHTML = `
        <div class="day-header">${dayOfWeek}</div>
        <div class="day-date">${dayDate}</div>
        <div class="weather-icon ${day.weatherType}">${weatherIcons[day.weatherType]}</div>
        <div class="weather-stats-daily">
          <div class="weather-stat-daily">
            <div class="stat-label-daily">Total:</div>
            <div class="stat-value-daily">${day.total}</div>
          </div>
          <div class="weather-stat-daily">
            <div class="stat-label-daily">Positifs:</div>
            <div class="stat-value-daily">${day.positive}</div>
          </div>
          <div class="weather-stat-daily">
            <div class="stat-label-daily">Négatifs:</div>
            <div class="stat-value-daily">${day.negative}</div>
          </div>
          <div class="weather-stat-daily ${hasMostMasterAspects ? 'master-highlight' : ''}">
            <div class="stat-label-daily">Maître:</div>
            <div class="stat-value-daily">${day.master}</div>
          </div>
          <div class="weather-stat-daily">
            <div class="stat-label-daily">M.Natal:</div>
            <div class="stat-value-daily">${day.masterNatal}</div>
          </div>
          <div class="weather-stat-daily">
            <div class="stat-label-daily">M.Transit:</div>
            <div class="stat-value-daily">${day.masterTransit}</div>
          </div>
        </div>
        <div class="day-card-actions">
          <button class="view-details-btn" data-date="${formatDate(day.date)}">Voir détails</button>
          <button class="export-day-gcal-btn" data-date="${formatDate(day.date)}" title="Exporter ce jour vers Google Calendar">
            <span class="gcal-icon">📅</span>
          </button>
        </div>
      `;

      // Add event listener for the details button
      card.querySelector('.view-details-btn').addEventListener('click', function() {
        const dateStr = this.dataset.date;
        showAspectDetails(day, dateStr);
      });

      // Add event listener for the export to Google Calendar button
      card.querySelector('.export-day-gcal-btn').addEventListener('click', function() {
        const dateStr = this.dataset.date;
        exportDayToGoogleCalendar(dateStr);
      });

      // Add the card to the grid
      dailyWeatherGrid.appendChild(card);
    });
  }

  // Format date as DD/MM/YYYY
  function formatDate(date) {
    return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
  }

  // Generate weather summary text
  function generateWeatherSummary(total, positive, negative, master, masterNatal, masterTransit, masterPlanet, days) {
    if (total === 0) {
      weatherSummaryText.innerHTML = `<p>Aucun aspect significatif n'a été trouvé pour les ${days} prochains jours.</p>`;
      return;
    }

    // Calculate the balance
    const balance = positive - negative;
    const masterPlanetName = getPlanetFrenchName(masterPlanet);

    let summary = '';

    // Overall tone
    if (balance > 3) {
      summary += `<p>La période des ${days} prochains jours s'annonce très favorable avec une prédominance d'aspects positifs (${positive} aspects positifs contre ${negative} aspects négatifs).</p>`;
    } else if (balance > 0) {
      summary += `<p>La période des ${days} prochains jours s'annonce plutôt favorable avec légèrement plus d'aspects positifs (${positive}) que négatifs (${negative}).</p>`;
    } else if (balance === 0) {
      summary += `<p>La période des ${days} prochains jours présente un équilibre entre les aspects positifs (${positive}) et négatifs (${negative}).</p>`;
    } else if (balance > -3) {
      summary += `<p>La période des ${days} prochains jours s'annonce légèrement difficile avec plus d'aspects négatifs (${negative}) que positifs (${positive}).</p>`;
    } else {
      summary += `<p>La période des ${days} prochains jours s'annonce particulièrement difficile avec une forte prédominance d'aspects négatifs (${negative} aspects négatifs contre ${positive} aspects positifs).</p>`;
    }

    // Master planet influence
    if (master > 0) {
      if (master > 2) {
        summary += `<p>Votre astre maître (${masterPlanetName}) est impliqué dans ${master} aspects durant cette période, indiquant une forte influence sur votre expérience personnelle.</p>`;
      } else {
        summary += `<p>Votre astre maître (${masterPlanetName}) est impliqué dans ${master} aspect(s) durant cette période, ce qui pourrait influencer modérément votre expérience personnelle.</p>`;
      }

      // Détails sur les aspects de l'astre maître natal et en transit
      summary += `<p>Détail des aspects de l'astre maître :</p>`;
      summary += `<ul>`;
      if (masterNatal > 0) {
        summary += `<li>${masterPlanetName} natal reçoit ${masterNatal} aspect(s) des planètes en transit</li>`;
      }
      if (masterTransit > 0) {
        summary += `<li>${masterPlanetName} en transit forme ${masterTransit} aspect(s) avec les planètes natales</li>`;
      }
      summary += `</ul>`;
    } else {
      summary += `<p>Votre astre maître (${masterPlanetName}) n'est impliqué dans aucun aspect durant cette période (ni en position natale, ni en transit), suggérant que les événements pourraient être moins personnellement significatifs.</p>`;
    }

    // Advice based on the balance
    if (balance > 0) {
      summary += `<p><strong>Conseil :</strong> C'est une bonne période pour prendre des initiatives et avancer dans vos projets.</p>`;
    } else if (balance < 0) {
      summary += `<p><strong>Conseil :</strong> Soyez prudent et évitez les décisions importantes durant cette période. Prenez le temps de réfléchir avant d'agir.</p>`;
    } else {
      summary += `<p><strong>Conseil :</strong> Restez attentif aux opportunités tout en étant conscient des défis potentiels.</p>`;
    }

    weatherSummaryText.innerHTML = summary;
  }

  // Get French name for a planet
  function getPlanetFrenchName(planetKey) {
    const planetNames = {
      'sun': 'Soleil',
      'moon': 'Lune',
      'mercury': 'Mercure',
      'venus': 'Vénus',
      'mars': 'Mars',
      'jupiter': 'Jupiter',
      'saturn': 'Saturne',
      'uranus': 'Uranus',
      'neptune': 'Neptune'
    };

    return planetNames[planetKey] || planetKey;
  }

  // Show aspect details in modal
  function showAspectDetails(dayData, dateStr) {
    // Update modal title with date
    aspectDetailsDate.textContent = dateStr;

    // Update summary statistics
    modalTotalAspects.textContent = dayData.total;
    modalPositiveAspects.textContent = dayData.positive;
    modalNegativeAspects.textContent = dayData.negative;
    modalMasterAspects.textContent = dayData.master;
    document.getElementById('modal-master-natal-aspects').textContent = dayData.masterNatal;
    document.getElementById('modal-master-transit-aspects').textContent = dayData.masterTransit;

    // Vérifier si nous sommes en mode "basé sur l'astre maître"
    const isInMasterMode = document.querySelector('input[name="icon-logic"]:checked').value === 'master';

    // Réinitialiser la classe de mise en valeur
    const masterStatElement = document.querySelector('#modal-master-aspects').parentElement.parentElement;
    masterStatElement.classList.remove('master-highlight');

    // Si nous sommes en mode "basé sur l'astre maître" et que ce jour a des aspects à l'astre maître
    if (isInMasterMode && dayData.master > 0) {
      // Trouver le maximum d'aspects à l'astre maître parmi tous les jours
      const maxMasterAspects = Math.max(...globalDailyAspects.map(day => day.master));

      // Si ce jour a le maximum d'aspects à l'astre maître, le mettre en valeur
      if (dayData.master === maxMasterAspects) {
        masterStatElement.classList.add('master-highlight');
      }
    }

    // Clear previous aspect list
    aspectDetailsList.innerHTML = '';

    // Get birth and transit positions for the day
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );
    const birthPositions = calculatePlanetaryPositions(birthDate);

    // Parse the date string (format: DD/MM/YYYY)
    const [day, month, year] = dateStr.split('/').map(num => parseInt(num));
    const transitDate = new Date(year, month - 1, day);
    const transitPositions = calculatePlanetaryPositions(transitDate);

    // Add each aspect to the list
    if (dayData.aspects.length > 0) {
      dayData.aspects.forEach(aspect => {
        const aspectItem = document.createElement('div');
        aspectItem.className = 'aspect-details-item';

        // Add class based on aspect type
        if (aspect.isPositive) {
          aspectItem.classList.add('positive');
        } else {
          aspectItem.classList.add('negative');
        }

        // Add master class if it's a master aspect
        if (aspect.isMasterAspect) {
          aspectItem.classList.add('master');
        }

        // Get planet names
        const transitPlanetName = getPlanetFrenchName(aspect.transitPlanet);
        const natalPlanetName = getPlanetFrenchName(aspect.natalPlanet);

        // Get aspect name
        let aspectName = getAspectName(aspect.aspectAngle);

        // Get planet positions and calculate orb
        const transitPos = transitPositions[aspect.transitPlanet];
        const birthPos = birthPositions[aspect.natalPlanet];

        // Calculate the angular difference for orb
        const transitLong = transitPos.siderealLongitude;
        const birthLong = birthPos.siderealLongitude;
        let diff = Math.abs(transitLong - birthLong);
        if (diff > 180) diff = 360 - diff;
        const orb = Math.abs(diff - aspect.aspectAngle).toFixed(1);

        // Get sign and degree information
        const transitSign = transitPos.sign;
        const transitDegree = parseFloat(transitPos.degree).toFixed(1);
        const birthSign = birthPos.sign;
        const birthDegree = parseFloat(birthPos.degree).toFixed(1);

        // Get aspect type name (without the angle)
        let aspectTypeName = getAspectTypeName(aspect.aspectAngle);

        // Créer le texte pour l'astre maître
        let masterText = '';
        if (aspect.isMasterAspect) {
          if (aspect.isMasterAspectNatal && aspect.isMasterAspectTransit) {
            masterText = ' • Astre Maître (Natal et Transit)';
          } else if (aspect.isMasterAspectNatal) {
            masterText = ' • Astre Maître (Natal)';
          } else if (aspect.isMasterAspectTransit) {
            masterText = ' • Astre Maître (Transit)';
          }
        }

        // Créer le contenu de l'élément d'aspect sans utiliser innerHTML
        const aspectDetailsDiv = document.createElement('div');
        aspectDetailsDiv.className = 'aspect-details-planets';

        // Créer le bouton Fiche
        const ficheBtn = document.createElement('button');
        ficheBtn.className = 'view-aspect-card-btn';
        ficheBtn.textContent = 'Fiche';
        ficheBtn.setAttribute('data-transit-planet', aspect.transitPlanet);
        ficheBtn.setAttribute('data-natal-planet', aspect.natalPlanet);
        ficheBtn.setAttribute('data-aspect-angle', aspect.aspectAngle);
        ficheBtn.setAttribute('data-date', dateStr);

        // Créer le bouton d'interprétation
        const interpretBtn = document.createElement('button');
        interpretBtn.className = 'interpret-aspect-btn';
        interpretBtn.textContent = 'Interprétation';
        interpretBtn.setAttribute('data-transit-planet', aspect.transitPlanet);
        interpretBtn.setAttribute('data-natal-planet', aspect.natalPlanet);
        interpretBtn.setAttribute('data-aspect-angle', aspect.aspectAngle);

        // Ajouter un gestionnaire d'événements directement sur le bouton
        ficheBtn.addEventListener('click', function(e) {
          e.preventDefault(); // Empêcher le comportement par défaut
          e.stopPropagation(); // Empêcher la propagation de l'événement

          console.log('=== DÉBOGAGE BOUTON FICHE ===');
          console.log('Bouton Fiche cliqué directement');
          console.log('Données du bouton:', {
            transitPlanet: aspect.transitPlanet,
            natalPlanet: aspect.natalPlanet,
            aspectAngle: aspect.aspectAngle,
            date: dateStr
          });

          // Vérifier si la fenêtre modale existe
          const modal = document.getElementById('aspect-card-modal');
          console.log('La fenêtre modale existe-t-elle?', !!modal);

          // Appeler la fonction avec un délai pour éviter les conflits
          setTimeout(function() {
            openAspectCard(aspect.transitPlanet, aspect.natalPlanet, aspect.aspectAngle, dateStr);
          }, 100);
        });

        // Créer le span pour le texte de l'aspect
        const aspectText = document.createElement('span');
        aspectText.textContent = `${transitPlanetName}: ${transitSign} ${transitDegree}° | ${aspectTypeName} | ${natalPlanetName}: ${birthSign} ${birthDegree}° - ${aspect.isPositive ? 'Positif' : 'Négatif'} - Orb: ${orb}°${masterText}`;

        // Ajouter un gestionnaire d'événements pour le bouton d'interprétation
        interpretBtn.addEventListener('click', function(e) {
          e.preventDefault();

          // Récupérer les informations sur l'aspect
          const transitPlanet = this.getAttribute('data-transit-planet');
          const natalPlanet = this.getAttribute('data-natal-planet');
          const aspectAngle = parseFloat(this.getAttribute('data-aspect-angle'));

          // Déterminer le type d'aspect
          let aspectType = '';
          if (aspectAngle === 0) aspectType = 'conjunction';
          else if (aspectAngle === 60) aspectType = 'sextile';
          else if (aspectAngle === 90) aspectType = 'square';
          else if (aspectAngle === 120) aspectType = 'trine';
          else if (aspectAngle === 180) aspectType = 'opposition';
          else aspectType = `aspect de ${aspectAngle}°`;

          // Afficher l'interprétation
          showTransitNatalInterpretation(transitPlanet.toLowerCase(), aspectType, natalPlanet.toLowerCase());
        });

        // Ajouter les éléments au div parent
        aspectDetailsDiv.appendChild(ficheBtn);
        aspectDetailsDiv.appendChild(interpretBtn);
        aspectDetailsDiv.appendChild(aspectText);

        // Ajouter le div à l'élément d'aspect
        aspectItem.appendChild(aspectDetailsDiv);

        aspectDetailsList.appendChild(aspectItem);
      });
    } else {
      // No aspects for this day
      const noAspectsItem = document.createElement('div');
      noAspectsItem.className = 'aspect-details-item neutral';
      noAspectsItem.textContent = 'Aucun aspect significatif pour cette journée.';
      aspectDetailsList.appendChild(noAspectsItem);
    }

    // Show the modal
    aspectDetailsModal.style.display = 'block';

    // S'assurer que le bouton de fermeture fonctionne
    const closeBtn = document.getElementById('close-aspect-details-btn');
    if (closeBtn) {
      // Supprimer les gestionnaires d'événements existants pour éviter les doublons
      const newCloseBtn = closeBtn.cloneNode(true);
      closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);

      // Ajouter un nouveau gestionnaire d'événements
      newCloseBtn.addEventListener('click', function() {
        aspectDetailsModal.style.display = 'none';
      });
    }
  }

  // Get aspect name based on angle
  function getAspectName(angle) {
    switch (parseInt(angle)) {
      case 0: return 'Conjonction (0°)';
      case 30: return 'Semi-sextile (30°)';
      case 45: return 'Semi-carré (45°)';
      case 60: return 'Sextile (60°)';
      case 90: return 'Carré (90°)';
      case 120: return 'Trigone (120°)';
      case 135: return 'Sesqui-carré (135°)';
      case 150: return 'Quinconce (150°)';
      case 180: return 'Opposition (180°)';
      default: return `Aspect de ${angle}°`;
    }
  }

  // Get aspect type name without the angle
  function getAspectTypeName(angle) {
    switch (parseInt(angle)) {
      case 0: return 'conjonction';
      case 30: return 'semi-sextile';
      case 45: return 'semi-carré';
      case 60: return 'sextile';
      case 90: return 'carré';
      case 120: return 'trigone';
      case 135: return 'sesqui-carré';
      case 150: return 'quinconce';
      case 180: return 'opposition';
      default: return `aspect de ${angle}°`;
    }
  }

  // Gestionnaire pour fermer la fenêtre modale des détails d'aspects
  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', function() {
      if (aspectDetailsModal) {
        aspectDetailsModal.style.display = 'none';
      }
    });
  } else {
    console.error("Bouton de fermeture de la fenêtre modale des détails d'aspects non trouvé");
  }


  // Close modal when clicking outside the content
  window.addEventListener('click', function(event) {
    // Récupérer les modales directement
    const aspectDetailsModal = document.getElementById('aspect-details-modal');
    const aspectCardModal = document.getElementById('aspect-card-modal');

    // Fermer la modale des détails d'aspect si on clique dessus
    if (event.target === aspectDetailsModal) {
      console.log('Fermeture de la modale des détails d\'aspect par clic extérieur');
      aspectDetailsModal.style.display = 'none';
    }

    // Ne pas fermer automatiquement la fiche d'aspect par clic extérieur
    // pour éviter les problèmes d'affichage
    // La fiche d'aspect ne peut être fermée que par le bouton de fermeture
  });

  // Variables pour la fiche d'aspect
  let aspectCardModal, aspectCardClose, aspectCardTitle, aspectImportance;
  let aspectComment, saveAspectNoteBtn;

  // Fonction pour initialiser les variables de la fiche d'aspect
  function initAspectCardVariables() {
    aspectCardModal = document.getElementById('aspect-card-modal');
    aspectCardClose = document.getElementById('aspect-card-close');
    aspectCardTitle = document.getElementById('aspect-card-title');
    aspectImportance = document.getElementById('aspect-importance');
    aspectComment = document.getElementById('aspect-comment');

    // Bouton de la fiche d'aspect
    saveAspectNoteBtn = document.getElementById('save-aspect-note-btn');

    console.log('Variables de la fiche d\'aspect initialisées :', {
      aspectCardModal, aspectCardClose, aspectCardTitle, aspectImportance,
      aspectComment, saveAspectNoteBtn
    });
  }

  // Initialiser les variables de la fiche d'aspect
  initAspectCardVariables();

  // Ajouter les gestionnaires d'événements pour les clics sur les planètes dans les zodiacs
  initZodiacInteractions();

  // Initialiser les contrôles de filtrage des aspects des zodiacs
  initZodiacAspectsFilters();

  // Gestionnaire pour fermer la fiche d'aspect
  if (aspectCardClose) {
    aspectCardClose.addEventListener('click', function() {
      aspectCardModal.style.display = 'none';
    });
  }

  // Fonction pour générer une clé unique pour un aspect
  function generateAspectKey(transitPlanet, natalPlanet, aspectAngle) {
    return `${transitPlanet}-${natalPlanet}-${aspectAngle}`;
  }

  // Fonction pour calculer les aspects entre les planètes de transit et les planètes natales
  function calculateTransitNatalAspects(transitPositions, birthPositions, orbValue = null, selectedAspectTypes = null) {
    const aspects = [];
    const orb = orbValue || parseFloat(aspectOrbInput?.value) || 2; // Utiliser l'orb spécifié ou celui des paramètres ou 2° par défaut

    // Définir les types d'aspects à considérer
    const aspectTypes = [
      { angle: 0, name: "Conjonction", type: "conjunction" },
      { angle: 60, name: "Sextile", type: "sextile" },
      { angle: 90, name: "Carré", type: "square" },
      { angle: 120, name: "Trigone", type: "trine" },
      { angle: 180, name: "Opposition", type: "opposition" }
    ];

    // Définir les planètes à considérer
    const planets = [
      { key: 'sun', name: 'Soleil' },
      { key: 'moon', name: 'Lune' },
      { key: 'mercury', name: 'Mercure' },
      { key: 'venus', name: 'Vénus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturne' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    // Pour chaque planète de transit
    planets.forEach(transitPlanet => {
      const transitData = transitPositions[transitPlanet.key];
      if (!transitData) return;

      // Calculer la position absolue de la planète de transit
      const transitSignIndex = ZODIAC_SIGNS.indexOf(transitData.sign);
      const transitLongitude = transitSignIndex * 30 + parseFloat(transitData.degree);

      // Pour chaque planète natale
      planets.forEach(natalPlanet => {
        const natalData = birthPositions[natalPlanet.key];
        if (!natalData) return;

        // Calculer la position absolue de la planète natale
        const natalSignIndex = ZODIAC_SIGNS.indexOf(natalData.sign);
        const natalLongitude = natalSignIndex * 30 + parseFloat(natalData.degree);

        // Calculer l'angle entre les deux planètes
        let angle = Math.abs(transitLongitude - natalLongitude);
        if (angle > 180) angle = 360 - angle;

        // Vérifier si l'angle correspond à un aspect connu
        aspectTypes.forEach(aspectType => {
          // Si des types d'aspects spécifiques sont sélectionnés, vérifier s'ils incluent ce type
          if (selectedAspectTypes && !selectedAspectTypes.includes(aspectType.type)) {
            return; // Ignorer cet aspect s'il n'est pas sélectionné
          }

          const aspectOrb = Math.abs(angle - aspectType.angle);

          // Si l'orb est dans la limite définie
          if (aspectOrb <= orb) {
            // Déterminer si l'aspect est positif ou négatif
            const isPositive = [0, 60, 120].includes(aspectType.angle);

            // Ajouter l'aspect à la liste
            aspects.push({
              transitPlanet: transitPlanet.key,
              transitPlanetName: transitPlanet.name,
              transitSign: transitData.sign,
              transitDegree: transitData.degree,
              natalPlanet: natalPlanet.key,
              natalPlanetName: natalPlanet.name,
              natalSign: natalData.sign,
              natalDegree: natalData.degree,
              aspect: aspectType.name,
              aspectType: aspectType.type,
              aspectAngle: aspectType.angle,
              orb: aspectOrb.toFixed(1),
              isPositive: isPositive
            });
          }
        });
      });
    });

    return aspects;
  }

  // Fonction pour calculer les aspects entre les planètes de transit et les planètes natales (mode Nakshatra)
  function calculateTransitNatalAspectsNakshatra(transitPositions, birthPositions, orbValue = null, selectedAspectTypes = null) {
    const aspects = [];
    const orb = orbValue || parseFloat(aspectOrbInput?.value) || 2; // Utiliser l'orb spécifié ou celui des paramètres ou 2° par défaut

    // Définir les types d'aspects à considérer
    const aspectTypes = [
      { angle: 0, name: "Conjonction", type: "conjunction" },
      { angle: 60, name: "Sextile", type: "sextile" },
      { angle: 90, name: "Carré", type: "square" },
      { angle: 120, name: "Trigone", type: "trine" },
      { angle: 180, name: "Opposition", type: "opposition" }
    ];

    // Filtrer les types d'aspects si spécifiés
    const typesToCheck = selectedAspectTypes && selectedAspectTypes.length > 0
      ? aspectTypes.filter(type => selectedAspectTypes.includes(type.type))
      : aspectTypes;

    // Planètes à considérer
    const planets = ['sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune', 'pluto'];

    // Pour chaque planète de transit
    planets.forEach(transitPlanet => {
      if (!transitPositions[transitPlanet]) return;

      // Pour chaque planète natale
      planets.forEach(natalPlanet => {
        if (!birthPositions[natalPlanet]) return;

        // Utiliser les positions sidérales pour le mode Nakshatra
        const transitPos = transitPositions[transitPlanet].siderealLongitude;
        const natalPos = birthPositions[natalPlanet].siderealLongitude;

        // Calculer la séparation angulaire
        let separation = Math.abs(transitPos - natalPos);
        if (separation > 180) separation = 360 - separation;

        // Vérifier chaque type d'aspect
        typesToCheck.forEach(aspectType => {
          const aspectAngle = aspectType.angle;
          const orbDifference = Math.abs(separation - aspectAngle);

          // Vérifier si l'aspect est dans l'orb
          if (orbDifference <= orb) {
            aspects.push({
              transitPlanet: transitPlanet,
              natalPlanet: natalPlanet,
              aspectType: aspectType.name,
              aspectAngle: aspectAngle,
              orb: orbDifference.toFixed(1),
              transitPosition: transitPos.toFixed(1),
              natalPosition: natalPos.toFixed(1),
              separation: separation.toFixed(1)
            });
          }
        });
      });
    });

    // Trier les aspects par orb (plus précis en premier)
    aspects.sort((a, b) => parseFloat(a.orb) - parseFloat(b.orb));

    return aspects;
  }

  // Fonction pour afficher les aspects avec filtres
  function displayZodiacAspectsWithFilters(containerId, transitPositions, birthPositions, prefix = null) {
    // Déterminer le préfixe basé sur l'ID du conteneur ou le paramètre fourni
    const actualPrefix = prefix || (containerId.includes('standard') ? 'standard' : 'nakshatra');

    // Récupérer les valeurs des filtres
    const orbSlider = document.getElementById(`${actualPrefix}-orb-filter`);
    const orbValue = parseFloat(orbSlider?.value) || 2;

    // Récupérer les types d'aspects sélectionnés
    const aspectTypeCheckboxes = document.querySelectorAll(`input[name="${actualPrefix}-aspect-type"]:checked`);
    const selectedAspectTypes = Array.from(aspectTypeCheckboxes).map(cb => cb.value);

    // Calculer les aspects avec les filtres selon le mode
    let aspects;
    if (actualPrefix === 'nakshatra') {
      // Pour le mode Nakshatra, utiliser les positions sidérales
      aspects = calculateTransitNatalAspectsNakshatra(transitPositions, birthPositions, orbValue, selectedAspectTypes);
    } else {
      // Pour le mode Standard, utiliser les positions tropicales
      aspects = calculateTransitNatalAspects(transitPositions, birthPositions, orbValue, selectedAspectTypes);
    }

    // Afficher les aspects
    displayZodiacAspects(aspects, containerId);
  }

  // Fonction pour afficher les aspects dans une section dédiée
  function displayZodiacAspects(aspects, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // Vider le conteneur
    container.innerHTML = '';

    // Si aucun aspect n'est trouvé
    if (aspects.length === 0) {
      container.innerHTML = '<div class="empty-aspects-message">Aucun aspect trouvé avec l\'orb actuel.</div>';
      return;
    }

    // Trier les aspects par orb (du plus précis au moins précis)
    const sortedAspects = [...aspects].sort((a, b) => parseFloat(a.orb) - parseFloat(b.orb));

    // Limiter à 10 aspects maximum pour ne pas surcharger l'interface
    const limitedAspects = sortedAspects.slice(0, 10);

    // Créer un élément pour chaque aspect
    limitedAspects.forEach(aspect => {
      const aspectItem = document.createElement('div');
      aspectItem.className = 'zodiac-aspect-item';

      // Créer la div d'information sur l'aspect
      const aspectInfo = document.createElement('div');
      aspectInfo.className = 'zodiac-aspect-info';

      // Créer la div pour les planètes
      const aspectPlanets = document.createElement('div');
      aspectPlanets.className = 'zodiac-aspect-planets';

      // Gérer les différences entre les formats d'aspects Standard, Nakshatra et Proportionnel
      let transitPlanetName, natalPlanetName, aspectName;
      if (aspect.transitPlanetName && aspect.natalPlanetName) {
        // Format Standard
        transitPlanetName = aspect.transitPlanetName;
        natalPlanetName = aspect.natalPlanetName;
        aspectName = aspect.aspect;
      } else {
        // Format Nakshatra ou Proportionnel - convertir les clés en noms
        transitPlanetName = getPlanetFrenchName(aspect.transitPlanet);
        natalPlanetName = getPlanetFrenchName(aspect.natalPlanet);

        // Pour les aspects proportionnels, afficher l'angle original et proportionnel
        if (aspect.originalAngle !== undefined) {
          aspectName = `${aspect.aspectType} (${aspect.originalAngle}°→${aspect.aspectAngle}°)`;
        } else {
          aspectName = aspect.aspectType;
        }
      }

      aspectPlanets.textContent = `${transitPlanetName} ${aspectName} ${natalPlanetName}`;

      // Créer la div pour les détails
      const aspectDetails = document.createElement('div');
      aspectDetails.className = 'zodiac-aspect-details';

      if (aspect.transitSign && aspect.natalSign) {
        // Format Standard
        aspectDetails.textContent = `${aspect.transitSign} ${aspect.transitDegree}° - ${aspect.natalSign} ${aspect.natalDegree}° (Orb: ${aspect.orb}°)`;
      } else {
        // Format Nakshatra ou Proportionnel
        if (aspect.originalAngle !== undefined) {
          // Format Proportionnel - afficher les positions dans le système 30°
          aspectDetails.textContent = `${aspect.transitPosition}° - ${aspect.natalPosition}° en mode 30° (Orb: ${aspect.orb}°)`;
        } else {
          // Format Nakshatra
          aspectDetails.textContent = `${aspect.transitPosition}° - ${aspect.natalPosition}° (Orb: ${aspect.orb}°)`;
        }
      }

      // Ajouter les éléments à la div d'information
      aspectInfo.appendChild(aspectPlanets);
      aspectInfo.appendChild(aspectDetails);

      // Créer le bouton d'interprétation
      const interpretBtn = document.createElement('button');
      interpretBtn.className = 'zodiac-aspect-interpret-btn';
      interpretBtn.textContent = 'Interprétation';
      interpretBtn.addEventListener('click', () => {
        // Afficher l'interprétation de l'aspect
        let transitPlanetKey, natalPlanetKey, aspectTypeKey;

        if (aspect.transitPlanet && aspect.natalPlanet && aspect.aspectType) {
          // Format Standard ou Nakshatra avec clés directes
          transitPlanetKey = aspect.transitPlanet;
          natalPlanetKey = aspect.natalPlanet;
          aspectTypeKey = aspect.aspectType;
        } else {
          // Format Standard avec noms complets
          transitPlanetKey = aspect.transitPlanet;
          natalPlanetKey = aspect.natalPlanet;
          aspectTypeKey = aspect.aspectType;
        }

        showTransitNatalInterpretation(
          transitPlanetKey,
          aspectTypeKey,
          natalPlanetKey
        );
      });

      // Ajouter les éléments à l'item d'aspect
      aspectItem.appendChild(aspectInfo);
      aspectItem.appendChild(interpretBtn);

      // Ajouter l'item d'aspect au conteneur
      container.appendChild(aspectItem);
    });

    // Ajouter un message si plus d'aspects sont disponibles
    if (aspects.length > 10) {
      const moreAspectsMessage = document.createElement('div');
      moreAspectsMessage.className = 'more-aspects-message';
      moreAspectsMessage.textContent = `+ ${aspects.length - 10} autres aspects`;
      container.appendChild(moreAspectsMessage);
    }
  }

  // Fonction pour initialiser les contrôles de filtrage des aspects des zodiacs
  function initZodiacAspectsFilters() {
    // Initialiser les contrôles pour le zodiaque standard
    initZodiacFilterControls('standard');

    // Initialiser les contrôles pour le zodiaque Nakshatra
    initZodiacFilterControls('nakshatra');
  }

  // Fonction pour initialiser les contrôles de filtrage pour un zodiaque spécifique
  function initZodiacFilterControls(prefix) {
    const orbSlider = document.getElementById(`${prefix}-orb-filter`);
    const orbValue = document.getElementById(`${prefix}-orb-value`);
    const applyFilterBtn = document.getElementById(`${prefix}-apply-filter`);

    if (orbSlider && orbValue) {
      // Mettre à jour la valeur affichée quand le slider change
      orbSlider.addEventListener('input', function() {
        orbValue.textContent = parseFloat(this.value).toFixed(1) + '°';

        // Appliquer automatiquement les filtres quand l'orb change
        const canvas = document.getElementById(`${prefix === 'standard' ? 'standard-zodiac-canvas' : 'nakshatra-zodiac-canvas'}`);
        if (canvas && canvas.birthPositions && canvas.transitPositions) {
          const containerId = `${prefix}-zodiac-aspects`;
          displayZodiacAspectsWithFilters(containerId, canvas.transitPositions, canvas.birthPositions, prefix);
        }
      });
    }

    if (applyFilterBtn) {
      // Appliquer les filtres quand le bouton est cliqué
      applyFilterBtn.addEventListener('click', function() {
        // Récupérer les positions des planètes depuis les canvas
        const canvas = document.getElementById(`${prefix === 'standard' ? 'standard-zodiac-canvas' : 'nakshatra-zodiac-canvas'}`);
        if (canvas && canvas.birthPositions && canvas.transitPositions) {
          const containerId = `${prefix}-zodiac-aspects`;
          displayZodiacAspectsWithFilters(containerId, canvas.transitPositions, canvas.birthPositions, prefix);
        }
      });
    }

    // Ajouter des gestionnaires pour les checkboxes d'aspects
    const aspectCheckboxes = document.querySelectorAll(`input[name="${prefix}-aspect-type"]`);
    aspectCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        // Appliquer automatiquement les filtres quand une checkbox change
        const canvas = document.getElementById(`${prefix === 'standard' ? 'standard-zodiac-canvas' : 'nakshatra-zodiac-canvas'}`);
        if (canvas && canvas.birthPositions && canvas.transitPositions) {
          const containerId = `${prefix}-zodiac-aspects`;
          displayZodiacAspectsWithFilters(containerId, canvas.transitPositions, canvas.birthPositions, prefix);
        }
      });
    });
  }

  // Fonction pour initialiser les interactions avec les zodiacs
  function initZodiacInteractions() {
    // Variables pour stocker les planètes sélectionnées
    let selectedPlanet = null;

    // Gestionnaire d'événements pour le clic sur le zodiaque standard
    if (standardZodiacCanvas) {
      standardZodiacCanvas.addEventListener('click', function(event) {
        handleZodiacClick(event, standardZodiacCanvas, 'standard');
      });
    }

    // Gestionnaire d'événements pour le clic sur le zodiaque Nakshatra
    if (nakshatraZodiacCanvas) {
      nakshatraZodiacCanvas.addEventListener('click', function(event) {
        handleZodiacClick(event, nakshatraZodiacCanvas, 'nakshatra');
      });
    }

    // Fonction pour gérer le clic sur un zodiaque
    function handleZodiacClick(event, canvas, zodiacType) {
      // Obtenir les coordonnées du clic par rapport au canvas
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Vérifier si le clic est sur une planète de transit
      const transitPlanet = findPlanetAtCoordinates(x, y, canvas.transitPlanetCoordinates);

      // Vérifier si le clic est sur une planète natale
      const natalPlanet = findPlanetAtCoordinates(x, y, canvas.birthPlanetCoordinates);

      // Si une planète est cliquée
      if (transitPlanet || natalPlanet) {
        const clickedPlanet = transitPlanet || natalPlanet;

        // Si c'est la première planète sélectionnée
        if (!selectedPlanet) {
          selectedPlanet = {
            planet: clickedPlanet.planet,
            isTransit: clickedPlanet.isTransit,
            data: clickedPlanet.data,
            zodiacType: zodiacType
          };

          // Afficher un message indiquant la planète sélectionnée
          alert(`Planète sélectionnée: ${clickedPlanet.planet.charAt(0).toUpperCase() + clickedPlanet.planet.slice(1)} (${clickedPlanet.isTransit ? 'Transit' : 'Natal'}). Cliquez sur une autre planète pour voir l'interprétation de l'aspect.`);
        }
        // Si c'est la deuxième planète sélectionnée
        else {
          // Vérifier que les deux planètes ne sont pas les mêmes
          if (selectedPlanet.planet === clickedPlanet.planet &&
              selectedPlanet.isTransit === clickedPlanet.isTransit) {
            alert("Vous avez sélectionné la même planète. Veuillez en sélectionner une différente.");
            return;
          }

          // Déterminer quelle planète est en transit et quelle planète est natale
          let transitPlanetKey, natalPlanetKey;

          if (selectedPlanet.isTransit && !clickedPlanet.isTransit) {
            transitPlanetKey = selectedPlanet.planet;
            natalPlanetKey = clickedPlanet.planet;
          } else if (!selectedPlanet.isTransit && clickedPlanet.isTransit) {
            transitPlanetKey = clickedPlanet.planet;
            natalPlanetKey = selectedPlanet.planet;
          } else {
            // Les deux planètes sont soit en transit, soit natales
            alert("Veuillez sélectionner une planète de transit et une planète natale.");
            selectedPlanet = null;
            return;
          }

          // Calculer l'angle entre les deux planètes
          const transitPos = selectedPlanet.isTransit ? selectedPlanet.data : clickedPlanet.data;
          const natalPos = !selectedPlanet.isTransit ? selectedPlanet.data : clickedPlanet.data;

          // Calculer les positions absolues
          const transitSignIndex = ZODIAC_SIGNS.indexOf(transitPos.sign);
          const natalSignIndex = ZODIAC_SIGNS.indexOf(natalPos.sign);

          const transitLongitude = transitSignIndex * 30 + parseFloat(transitPos.degree);
          const natalLongitude = natalSignIndex * 30 + parseFloat(natalPos.degree);

          // Calculer l'angle entre les deux positions
          let aspectAngle = Math.abs(transitLongitude - natalLongitude);
          if (aspectAngle > 180) aspectAngle = 360 - aspectAngle;

          // Arrondir l'angle à l'aspect le plus proche
          const standardAspects = [0, 60, 90, 120, 180];
          let closestAspect = standardAspects.reduce((prev, curr) =>
            Math.abs(curr - aspectAngle) < Math.abs(prev - aspectAngle) ? curr : prev
          );

          // Calculer l'orb (différence entre l'angle exact et l'aspect standard le plus proche)
          const orb = Math.abs(aspectAngle - closestAspect).toFixed(1);

          // Afficher l'interprétation de l'aspect
          showTransitNatalInterpretation(
            transitPlanetKey,
            getAspectName(closestAspect).toLowerCase(),
            natalPlanetKey
          );

          // Réinitialiser la sélection
          selectedPlanet = null;
        }
      }
    }

    // Fonction pour trouver une planète aux coordonnées données
    function findPlanetAtCoordinates(x, y, planetCoordinates) {
      if (!planetCoordinates) return null;

      for (const planet of planetCoordinates) {
        const distance = Math.sqrt(Math.pow(x - planet.x, 2) + Math.pow(y - planet.y, 2));
        if (distance <= planet.radius) {
          return planet;
        }
      }

      return null;
    }

    // Fonction pour obtenir le nom d'un aspect à partir de son angle
    function getAspectName(angle) {
      switch (angle) {
        case 0: return 'Conjunction';
        case 60: return 'Sextile';
        case 90: return 'Square';
        case 120: return 'Trine';
        case 180: return 'Opposition';
        default: return `Aspect de ${angle}°`;
      }
    }
  }

  // Fonction pour ouvrir la fiche d'aspect (version ultra-simplifiée et robuste)
  function openAspectCard(transitPlanet, natalPlanet, aspectAngle, date) {
    console.log('=== DÉBOGAGE OUVERTURE FICHE ASPECT ===');
    console.log('openAspectCard appelé avec :', { transitPlanet, natalPlanet, aspectAngle, date });

    try {
      // Vérifier que tous les paramètres sont présents
      if (!transitPlanet || !natalPlanet || !aspectAngle) {
        console.error('Paramètres manquants pour openAspectCard');
        alert('Erreur : paramètres manquants pour ouvrir la fiche d\'aspect');
        return;
      }

      // Convertir aspectAngle en nombre si c'est une chaîne
      if (typeof aspectAngle === 'string') {
        aspectAngle = parseInt(aspectAngle);
      }

      // Vérifier que aspectAngle est un nombre valide
      if (isNaN(aspectAngle)) {
        console.error('Angle d\'aspect invalide :', aspectAngle);
        alert('Erreur : angle d\'aspect invalide');
        return;
      }

      // Récupérer directement les éléments DOM pour éviter les problèmes de variables
      const aspectCardModal = document.getElementById('aspect-card-modal');
      const aspectCardTitle = document.getElementById('aspect-card-title');
      const aspectImportance = document.getElementById('aspect-importance');
      const aspectComment = document.getElementById('aspect-comment');
      const saveAspectNoteBtn = document.getElementById('save-aspect-note-btn');
      const aspectCardClose = document.getElementById('aspect-card-close');

      console.log('Éléments DOM récupérés :', {
        aspectCardModal: !!aspectCardModal,
        aspectCardTitle: !!aspectCardTitle,
        aspectImportance: !!aspectImportance,
        aspectComment: !!aspectComment,
        saveAspectNoteBtn: !!saveAspectNoteBtn,
        aspectCardClose: !!aspectCardClose
      });

      // Vérifier que les éléments DOM existent
      if (!aspectCardModal || !aspectCardTitle) {
        console.error('Éléments DOM de la fiche d\'aspect non trouvés');
        alert('Erreur : éléments DOM de la fiche d\'aspect non trouvés');
        return;
      }

      const aspectKey = generateAspectKey(transitPlanet, natalPlanet, aspectAngle);
      console.log('Clé d\'aspect générée :', aspectKey);

      // Récupérer les données de l'aspect depuis le stockage local
      chrome.storage.local.get(['aspectCards'], function(result) {
        console.log('Données récupérées du stockage :', result);
        const aspectCards = result.aspectCards || {};
        const aspectData = aspectCards[aspectKey] || {
          importance: 5,
          comment: ''
        };

        try {
          // Mettre à jour le titre de la fiche
          const transitPlanetName = getPlanetFrenchName(transitPlanet);
          const natalPlanetName = getPlanetFrenchName(natalPlanet);
          const aspectName = getAspectTypeName(aspectAngle);
          aspectCardTitle.textContent = `${transitPlanetName} ${aspectName} ${natalPlanetName}`;
          console.log('Titre mis à jour :', aspectCardTitle.textContent);

          // Remplir les champs avec les données existantes
          aspectImportance.value = aspectData.importance || 5;
          aspectComment.value = '';  // Vider le champ de commentaire pour un nouveau commentaire
          console.log('Champs remplis avec les données existantes');

          // Afficher l'historique des commentaires
          console.log('Affichage de l\'historique des commentaires');
          displayAspectHistory(aspectData.notes || {});

          // Configurer le gestionnaire d'événements pour le bouton d'enregistrement
          if (saveAspectNoteBtn) {
            saveAspectNoteBtn.onclick = function() {
              saveSimpleAspectData(aspectKey, parseFloat(aspectImportance.value), aspectComment.value);
            };
            console.log('Gestionnaire d\'événements configuré pour le bouton d\'enregistrement');
          }

          // Fermer d'abord toutes les autres modales
          const otherModals = document.querySelectorAll('.aspect-details-modal');
          otherModals.forEach(modal => {
            if (modal !== aspectCardModal) {
              modal.style.display = 'none';
            }
          });

          // Afficher la fiche d'aspect
          aspectCardModal.style.display = 'block';
          console.log('Fiche d\'aspect affichée !');

          // S'assurer que la fenêtre modale est visible et au-dessus des autres éléments
          aspectCardModal.style.zIndex = '3000';

          // Forcer un reflow pour s'assurer que la fenêtre modale est visible
          void aspectCardModal.offsetWidth;

          // Ajouter un gestionnaire d'événements pour le bouton de fermeture
          if (aspectCardClose) {
            // Supprimer tous les gestionnaires d'événements existants
            aspectCardClose.onclick = function(e) {
              if (e) {
                e.preventDefault();
                e.stopPropagation();
              }
              console.log('Bouton de fermeture cliqué');
              aspectCardModal.style.display = 'none';
            };
            console.log('Gestionnaire d\'événements configuré pour le bouton de fermeture');
          }

          // S'assurer que la fenêtre modale reste visible même si on clique ailleurs
          setTimeout(function() {
            if (aspectCardModal.style.display !== 'block') {
              console.log('La fenêtre modale a été fermée automatiquement, on la réaffiche');
              aspectCardModal.style.display = 'block';
            }
          }, 200);
        } catch (innerError) {
          console.error('Erreur lors de l\'affichage de la fiche d\'aspect :', innerError);
          alert('Erreur lors de l\'affichage de la fiche d\'aspect : ' + innerError.message);
        }
      });
    } catch (error) {
      console.error('Erreur globale dans openAspectCard :', error);
      alert('Erreur globale dans openAspectCard : ' + error.message);
    }
  }

  // Fonction pour afficher l'historique des commentaires
  function displayAspectHistory(notes) {
    // Récupérer directement l'élément DOM pour éviter les problèmes de référence
    const historyList = document.getElementById('aspect-history-list');

    if (!historyList) {
      console.error('L\'élément aspect-history-list n\'existe pas dans le DOM');
      return;
    }

    historyList.innerHTML = '';

    if (!notes || Object.keys(notes).length === 0) {
      historyList.innerHTML = '<div class="aspect-history-empty">Aucun historique disponible</div>';
      return;
    }

    // Trier les dates par ordre décroissant
    const sortedDates = Object.keys(notes).sort((a, b) => {
      const dateA = new Date(a.split('/').reverse().join('-'));
      const dateB = new Date(b.split('/').reverse().join('-'));
      return dateB - dateA;
    });

    sortedDates.forEach(date => {
      const note = notes[date];
      const historyItem = document.createElement('div');
      historyItem.className = 'aspect-history-item';

      // Créer l'élément d'historique avec le format souhaité
      historyItem.innerHTML = `
        <div class="aspect-history-date">${date} · ${note.rating}/5 Aspect fort</div>
        <div class="aspect-history-comment">${note.comment || 'Aucun commentaire'}</div>
        <div class="aspect-history-actions">
          <button class="history-edit-btn" data-date="${date}">Éditer</button>
          <button class="history-delete-btn" data-date="${date}">Supprimer</button>
        </div>
      `;

      historyList.appendChild(historyItem);
    });

    // Ajouter des gestionnaires d'événements pour les boutons d'édition et de suppression
    const editButtons = historyList.querySelectorAll('.history-edit-btn');
    const deleteButtons = historyList.querySelectorAll('.history-delete-btn');

    editButtons.forEach(button => {
      button.addEventListener('click', function() {
        const date = this.dataset.date;
        const note = notes[date];

        // Remplir le champ de commentaire avec le commentaire existant
        const commentField = document.getElementById('aspect-comment');
        if (commentField) {
          commentField.value = note.comment || '';
        }
      });
    });

    deleteButtons.forEach(button => {
      button.addEventListener('click', function() {
        const date = this.dataset.date;

        // Vérifier que le titre de l'aspect existe et a du contenu
        const cardTitle = document.getElementById('aspect-card-title');
        if (!cardTitle || !cardTitle.textContent) {
          console.error('Le titre de l\'aspect n\'existe pas ou n\'a pas de contenu');
          alert('Erreur : impossible de supprimer la note');
          return;
        }

        // Récupérer les parties du titre pour générer la clé d'aspect
        const titleParts = cardTitle.textContent.split(' ');

        // Récupérer les planètes et l'aspect
        const transitPlanetName = titleParts[0];
        const aspectName = titleParts[1];
        const natalPlanetName = titleParts[2];

        // Convertir les noms en clés
        const transitPlanet = getPlanetKeyFromFrenchName(transitPlanetName);
        const natalPlanet = getPlanetKeyFromFrenchName(natalPlanetName);
        const aspectAngle = getAspectAngleFromName(aspectName);

        const aspectKey = generateAspectKey(transitPlanet, natalPlanet, aspectAngle);

        if (confirm(`Êtes-vous sûr de vouloir supprimer le commentaire du ${date} ?`)) {
          deleteAspectNote(aspectKey, date);
        }
      });
    });
  }

  // Fonction pour calculer les dates futures pour un aspect (supprimée dans la version simplifiée)

  // Fonction pour obtenir l'angle d'aspect à partir du nom
  function getAspectAngleFromName(aspectName) {
    switch (aspectName.toLowerCase()) {
      case 'conjonction': return 0;
      case 'semi-sextile': return 30;
      case 'semi-carré': return 45;
      case 'sextile': return 60;
      case 'carré': return 90;
      case 'trigone': return 120;
      case 'sesqui-carré': return 135;
      case 'quinconce': return 150;
      case 'opposition': return 180;
      default: return 0;
    }
  }

  // Fonction pour obtenir la clé de planète à partir du nom français
  function getPlanetKeyFromFrenchName(frenchName) {
    switch (frenchName.toLowerCase()) {
      case 'soleil': return 'sun';
      case 'lune': return 'moon';
      case 'mercure': return 'mercury';
      case 'vénus': return 'venus';
      case 'mars': return 'mars';
      case 'jupiter': return 'jupiter';
      case 'saturne': return 'saturn';
      case 'uranus': return 'uranus';
      case 'neptune': return 'neptune';
      case 'pluton': return 'pluto';
      case 'nœud nord': return 'northNode';
      case 'nœud sud': return 'southNode';
      case 'chiron': return 'chiron';
      case 'cérès': return 'ceres';
      case 'pallas': return 'pallas';
      case 'junon': return 'juno';
      case 'vesta': return 'vesta';
      default: return frenchName.toLowerCase();
    }
  }

  // Fonction pour enregistrer les données simplifiées de l'aspect avec historique
  function saveSimpleAspectData(aspectKey, importance, comment) {
    chrome.storage.local.get(['aspectCards'], function(result) {
      const aspectCards = result.aspectCards || {};

      if (!aspectCards[aspectKey]) {
        aspectCards[aspectKey] = {
          notes: {}
        };
      }

      // S'assurer que la propriété notes existe
      if (!aspectCards[aspectKey].notes) {
        aspectCards[aspectKey].notes = {};
      }

      // Enregistrer l'importance
      aspectCards[aspectKey].importance = importance;

      // Obtenir la date actuelle au format DD/MM/YYYY
      const today = new Date();
      const dateStr = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;

      // Enregistrer le commentaire dans l'historique avec la date actuelle
      aspectCards[aspectKey].notes[dateStr] = {
        rating: 5, // Valeur par défaut
        comment: comment
      };

      chrome.storage.local.set({ aspectCards: aspectCards }, function() {
        alert('Données enregistrées avec succès !');

        // Mettre à jour l'affichage de l'historique
        // Utiliser directement la fonction sans vérifier la variable globale
        displayAspectHistory(aspectCards[aspectKey].notes);
      });
    });
  }

  // Fonction pour sauvegarder l'interprétation d'un aspect (supprimée dans la version simplifiée)

  // Fonction pour sauvegarder l'importance d'un aspect
  function saveAspectImportance(aspectKey, importance) {
    chrome.storage.local.get(['aspectCards'], function(result) {
      const aspectCards = result.aspectCards || {};

      if (!aspectCards[aspectKey]) {
        aspectCards[aspectKey] = { notes: {} };
      }

      aspectCards[aspectKey].importance = importance;

      chrome.storage.local.set({ aspectCards: aspectCards }, function() {
        alert('Coefficient d\'importance enregistré avec succès !');
      });
    });
  }

  // Fonction pour sauvegarder une note d'aspect
  function saveAspectNote(aspectKey, date, rating, comment) {
    chrome.storage.local.get(['aspectCards'], function(result) {
      const aspectCards = result.aspectCards || {};

      if (!aspectCards[aspectKey]) {
        aspectCards[aspectKey] = { notes: {} };
      }

      if (!aspectCards[aspectKey].notes) {
        aspectCards[aspectKey].notes = {};
      }

      aspectCards[aspectKey].notes[date] = {
        rating: rating,
        comment: comment
      };

      chrome.storage.local.set({ aspectCards: aspectCards }, function() {
        alert('Note et commentaire enregistrés avec succès !');
        displayAspectHistory(aspectCards[aspectKey].notes);
      });
    });
  }

  // Fonction pour supprimer une note d'aspect
  function deleteAspectNote(aspectKey, date) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette note ?')) {
      chrome.storage.local.get(['aspectCards'], function(result) {
        const aspectCards = result.aspectCards || {};

        if (aspectCards[aspectKey] && aspectCards[aspectKey].notes && aspectCards[aspectKey].notes[date]) {
          delete aspectCards[aspectKey].notes[date];

          chrome.storage.local.set({ aspectCards: aspectCards }, function() {
            alert('Note supprimée avec succès !');
            // Utiliser directement la fonction sans référence à la variable globale
            displayAspectHistory(aspectCards[aspectKey].notes || {});
          });
        }
      });
    }
  }

  // Supprimer le gestionnaire d'événements global pour les boutons de fiche d'aspect
  // car nous utilisons maintenant des gestionnaires d'événements directs sur chaque bouton

  // Draw the circular proportional zodiac
  function drawCircularProportionalZodiac(birthPositions, transitPositions) {
    const circularZodiacCanvas = document.getElementById('circular-zodiac-canvas');
    if (!circularZodiacCanvas) return;

    const ctx = circularZodiacCanvas.getContext('2d');
    const width = circularZodiacCanvas.width = circularZodiacCanvas.offsetWidth;
    const height = circularZodiacCanvas.height = 500;
    const centerX = width / 2;
    const centerY = height / 2 + 15; // Décaler le centre vers le bas pour laisser de l'espace au titre
    // Agrandir le rayon pour un zodiaque plus grand
    const radius = Math.max(10, Math.min(centerX, centerY - 45) - 30);

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw title with margin
    ctx.font = 'bold 16px ' + getComputedStyle(document.body).fontFamily;
    ctx.fillStyle = '#333';
    ctx.textAlign = 'center';
    ctx.fillText('Zodiaque Circulaire Proportionnel (360° = 30°)', centerX, 25);

    // Définir les rayons pour les différents cercles
    const outerRadius = radius;           // Cercle extérieur pour les transits
    const innerRadius = radius * 0.65;    // Cercle intérieur pour les nataux
    const middleRadius = radius * 0.82;   // Cercle moyen pour les divisions

    // Draw outer circle (transits)
    ctx.beginPath();
    ctx.arc(centerX, centerY, outerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#4A90E2';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Draw middle circle (divisions)
    ctx.beginPath();
    ctx.arc(centerX, centerY, middleRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw inner circle (nataux)
    ctx.beginPath();
    ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#8E44AD';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Remplir l'espace entre les cercles avec des couleurs subtiles
    // Zone des transits (extérieure)
    ctx.beginPath();
    ctx.arc(centerX, centerY, outerRadius, 0, 2 * Math.PI);
    ctx.arc(centerX, centerY, middleRadius, 0, 2 * Math.PI, true);
    ctx.fillStyle = 'rgba(74, 144, 226, 0.1)';
    ctx.fill();

    // Zone des nataux (intérieure)
    ctx.beginPath();
    ctx.arc(centerX, centerY, middleRadius, 0, 2 * Math.PI);
    ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI, true);
    ctx.fillStyle = 'rgba(142, 68, 173, 0.1)';
    ctx.fill();

    // Draw center point
    ctx.beginPath();
    ctx.arc(centerX, centerY, 3, 0, 2 * Math.PI);
    ctx.fillStyle = '#333';
    ctx.fill();

    // Calculate and draw the four angles (natal)
    const birthDate = new Date(birthDateInput.value);
    birthDate.setHours(
      parseInt(birthHourInput.value) || 0,
      parseInt(birthMinuteInput.value) || 0,
      0
    );
    const latitude = parseFloat(latitudeInput.value) || 48.0667;
    const longitude = parseFloat(longitudeInput.value) || -0.7667;

    // Calculate local sidereal time for birth
    const lstBirth = calculateLocalSiderealTime(birthDate, longitude);

    // Calculate the four angles for birth chart
    let ascendantBirth = calculateAscendant(lstBirth, latitude);
    let midheavenBirth = calculateMidheaven(lstBirth);

    // Apply sidereal correction
    const ayanamsa = getAyanamsa(birthDate);
    ascendantBirth = normalizeAngle(ascendantBirth - ayanamsa);
    midheavenBirth = normalizeAngle(midheavenBirth - ayanamsa);

    // Calculate descendant (opposite to ascendant)
    const descendantBirth = normalizeAngle(ascendantBirth + 180);

    // Calculate imum coeli (opposite to midheaven)
    const imumCoeliBirth = normalizeAngle(midheavenBirth + 180);

    // Calculate transit angles (using current date and time)
    const transitDate = new Date(); // Date actuelle du PC

    // Calculate local sidereal time for transit
    const lstTransit = calculateLocalSiderealTime(transitDate, longitude);

    // Calculate ascendant for transit
    let ascendantTransit = calculateAscendant(lstTransit, latitude);

    // Apply sidereal correction
    ascendantTransit = normalizeAngle(ascendantTransit - ayanamsa);

    // Calculate descendant (opposite to ascendant) for transit
    const descendantTransit = normalizeAngle(ascendantTransit + 180);

    // Draw the four angles (avec inversion ASC/DESC et MC/IC) - Natal
    drawProportionalAngle(ctx, centerX, centerY, radius, descendantBirth, 'ASC', '#FF0000');
    drawProportionalAngle(ctx, centerX, centerY, radius, ascendantBirth, 'DESC', '#00AA00');
    drawProportionalAngle(ctx, centerX, centerY, radius, imumCoeliBirth, 'MC', '#0000FF');
    drawProportionalAngle(ctx, centerX, centerY, radius, midheavenBirth, 'IC', '#AA00AA');

    // Draw transit ascendant and descendant
    drawProportionalAngle(ctx, centerX, centerY, radius * 0.9, descendantTransit, 'ASC-T', '#FF9900', true);
    drawProportionalAngle(ctx, centerX, centerY, radius * 0.9, ascendantTransit, 'DESC-T', '#00CC66', true);

    // Draw degree markers (every 2.5 degrees, representing 30 degrees of the zodiac)
    for (let i = 0; i < 12; i++) {
      // Each section represents 2.5 degrees (30/12)
      const angle = (i * 30 - 90) * Math.PI / 180;

      // Draw major division lines traversant les deux cercles
      ctx.beginPath();
      ctx.moveTo(
        centerX + innerRadius * Math.cos(angle),
        centerY + innerRadius * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + outerRadius * Math.cos(angle),
        centerY + outerRadius * Math.sin(angle)
      );
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 1.5;
      ctx.stroke();

      // Draw degree number (0, 5, 10, 15, 20, 25) au centre entre les deux cercles
      const degreeAngle = (i * 30 - 90 + 15) * Math.PI / 180;
      const degreeX = centerX + middleRadius * Math.cos(degreeAngle);
      const degreeY = centerY + middleRadius * Math.sin(degreeAngle);

      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText((i * 2.5).toFixed(1) + '°', degreeX, degreeY);
    }

    // Draw minor degree markers
    for (let i = 0; i < 60; i++) {
      // Each minor marker represents 0.5 degrees (30/60)
      const angle = (i * 6 - 90) * Math.PI / 180;
      const markerLength = i % 5 === 0 ? 0.05 : 0.03; // Longer markers for multiples of 2.5°

      ctx.beginPath();
      ctx.moveTo(
        centerX + outerRadius * (1 - markerLength) * Math.cos(angle),
        centerY + outerRadius * (1 - markerLength) * Math.sin(angle)
      );
      ctx.lineTo(
        centerX + outerRadius * Math.cos(angle),
        centerY + outerRadius * Math.sin(angle)
      );
      ctx.strokeStyle = '#999';
      ctx.lineWidth = 0.5;
      ctx.stroke();
    }





    // Draw opposition aspects between natal and transit planets (derrière les planètes)
    drawProportionalOppositions(ctx, centerX, centerY, innerRadius * 0.85, outerRadius * 0.92, birthPositions, transitPositions);

    // Draw planets for birth chart (cercle intérieur)
    drawProportionalPlanets(ctx, centerX, centerY, innerRadius * 0.85, birthPositions, false);

    // Draw planets for transit chart (cercle extérieur)
    drawProportionalPlanets(ctx, centerX, centerY, outerRadius * 0.92, transitPositions, true);

    // Setup planet tooltips after drawing is complete
    setupPlanetTooltips(circularZodiacCanvas);
  }

  // Fonction pour dessiner les aspects d'opposition dans le zodiaque proportionnel
  function drawProportionalOppositions(ctx, centerX, centerY, natalRadius, transitRadius, birthPositions, transitPositions) {
    const planets = [
      { key: 'sun', name: 'Sun' },
      { key: 'moon', name: 'Moon' },
      { key: 'mercury', name: 'Mercury' },
      { key: 'venus', name: 'Venus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturn' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    // Calculer les positions normalisées pour les planètes natales
    const natalPlanets = [];
    planets.forEach(planet => {
      const planetData = birthPositions[planet.key];
      if (!planetData) return;

      const positionInSign = planetData.degree;
      const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);
      const totalPosition = (signIndex * 30) + positionInSign;
      const normalizedPosition = totalPosition % 30;

      natalPlanets.push({
        ...planet,
        data: planetData,
        normalizedPosition: normalizedPosition,
        angle: ((normalizedPosition * 12) - 90) * Math.PI / 180,
        x: centerX + natalRadius * Math.cos(((normalizedPosition * 12) - 90) * Math.PI / 180),
        y: centerY + natalRadius * Math.sin(((normalizedPosition * 12) - 90) * Math.PI / 180)
      });
    });

    // Calculer les positions normalisées pour les planètes de transit
    const transitPlanets = [];
    planets.forEach(planet => {
      const planetData = transitPositions[planet.key];
      if (!planetData) return;

      const positionInSign = planetData.degree;
      const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);
      const totalPosition = (signIndex * 30) + positionInSign;
      const normalizedPosition = totalPosition % 30;

      transitPlanets.push({
        ...planet,
        data: planetData,
        normalizedPosition: normalizedPosition,
        angle: ((normalizedPosition * 12) - 90) * Math.PI / 180,
        x: centerX + transitRadius * Math.cos(((normalizedPosition * 12) - 90) * Math.PI / 180),
        y: centerY + transitRadius * Math.sin(((normalizedPosition * 12) - 90) * Math.PI / 180)
      });
    });

    // Chercher les aspects d'opposition (180° ± 1°)
    natalPlanets.forEach(natalPlanet => {
      transitPlanets.forEach(transitPlanet => {
        // Calculer la différence angulaire dans le système proportionnel (0-30°)
        const angleDiff = Math.abs(natalPlanet.normalizedPosition - transitPlanet.normalizedPosition);
        const minAngleDiff = Math.min(angleDiff, 30 - angleDiff);

        // Opposition dans le système proportionnel = 15° ± 1°
        const oppositionAngle = 15;
        const orb = 1;

        if (Math.abs(minAngleDiff - oppositionAngle) <= orb) {
          // Tracer la ligne d'opposition en noir (fine)
          ctx.beginPath();
          ctx.moveTo(natalPlanet.x, natalPlanet.y);
          ctx.lineTo(transitPlanet.x, transitPlanet.y);
          ctx.strokeStyle = '#000000'; // Noir
          ctx.lineWidth = 1; // Ligne plus fine
          ctx.stroke();
        }
      });
    });
  }

  // Fonction pour dessiner un angle astrologique sur le zodiaque proportionnel
  function drawProportionalAngle(ctx, centerX, centerY, radius, angle_deg, label, color, isTransit = false) {
    // Normaliser l'angle à une valeur entre 0 et 30 (pour le zodiaque proportionnel)
    const normalizedAngle = angle_deg % 30;

    // Calculer l'angle en radians (0° à 3 heures, dans le sens anti-horaire)
    const angle = ((normalizedAngle * 12) - 90) * Math.PI / 180;

    // Dessiner la ligne de l'angle
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(
      centerX + radius * Math.cos(angle),
      centerY + radius * Math.sin(angle)
    );
    ctx.strokeStyle = color;
    ctx.lineWidth = isTransit ? 1.5 : 2;
    ctx.stroke();

    // Dessiner un triangle à l'extrémité de la ligne pour marquer l'angle
    const triangleSize = isTransit ? 8 : 10;
    const triangleX = centerX + (radius + 5) * Math.cos(angle);
    const triangleY = centerY + (radius + 5) * Math.sin(angle);

    ctx.beginPath();
    ctx.moveTo(triangleX, triangleY);
    ctx.lineTo(
      triangleX + triangleSize * Math.cos(angle + Math.PI * 2/3),
      triangleY + triangleSize * Math.sin(angle + Math.PI * 2/3)
    );
    ctx.lineTo(
      triangleX + triangleSize * Math.cos(angle - Math.PI * 2/3),
      triangleY + triangleSize * Math.sin(angle - Math.PI * 2/3)
    );
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // Ajouter le texte du label près du triangle
    ctx.font = isTransit ? 'bold 11px Arial' : 'bold 12px Arial';
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    const textX = centerX + (radius + 20) * Math.cos(angle);
    const textY = centerY + (radius + 20) * Math.sin(angle);
    ctx.fillText(label, textX, textY);
  }

  // Fonction pour dessiner un angle astrologique sur la carte du ciel standard
  function drawAngleOnChart(ctx, centerX, centerY, radius, angle_deg, label, color, isTransit = false) {
    // Calculer l'angle en radians (0° à 3 heures, dans le sens anti-horaire)
    const angle = (angle_deg - 90) * Math.PI / 180;

    // Ajuster le rayon pour les angles de transit (légèrement plus court)
    const angleRadius = isTransit ? radius * 0.9 : radius;

    // Dessiner la ligne de l'angle
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.lineTo(
      centerX + angleRadius * Math.cos(angle),
      centerY + angleRadius * Math.sin(angle)
    );
    ctx.strokeStyle = color;
    ctx.lineWidth = isTransit ? 1.5 : 2;
    ctx.stroke();

    // Dessiner un triangle à l'extrémité de la ligne pour marquer l'angle
    const triangleSize = isTransit ? 8 : 10;
    const triangleX = centerX + (angleRadius + 5) * Math.cos(angle);
    const triangleY = centerY + (angleRadius + 5) * Math.sin(angle);

    ctx.beginPath();
    ctx.moveTo(triangleX, triangleY);
    ctx.lineTo(
      triangleX + triangleSize * Math.cos(angle + Math.PI * 2/3),
      triangleY + triangleSize * Math.sin(angle + Math.PI * 2/3)
    );
    ctx.lineTo(
      triangleX + triangleSize * Math.cos(angle - Math.PI * 2/3),
      triangleY + triangleSize * Math.sin(angle - Math.PI * 2/3)
    );
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // Ajouter le texte du label près du triangle
    ctx.font = isTransit ? 'bold 11px Arial' : 'bold 12px Arial';
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    const textX = centerX + (angleRadius + 20) * Math.cos(angle);
    const textY = centerY + (angleRadius + 20) * Math.sin(angle);
    ctx.fillText(label, textX, textY);

    // Ajouter le signe et le degré de l'angle
    const signIndex = Math.floor(angle_deg / 30);
    const degreeInSign = angle_deg % 30;
    const sign = ZODIAC_SIGNS[signIndex];

    const infoX = centerX + (angleRadius + 35) * Math.cos(angle);
    const infoY = centerY + (angleRadius + 35) * Math.sin(angle);
    ctx.font = isTransit ? '9px Arial' : '10px Arial';
    ctx.fillStyle = color;
    ctx.fillText(`${sign} ${degreeInSign.toFixed(1)}°`, infoX, infoY);
  }

  // Draw planets on the proportional zodiac
  function drawProportionalPlanets(ctx, centerX, centerY, radius, positions, isTransit) {
    // Réinitialiser les données de tooltip au début du dessin
    if (!isTransit) { // Seulement pour les planètes natales (premier appel)
      window.planetTooltipData = [];
    }
    const planets = [
      { key: 'sun', name: 'Sun' },
      { key: 'moon', name: 'Moon' },
      { key: 'mercury', name: 'Mercury' },
      { key: 'venus', name: 'Venus' },
      { key: 'mars', name: 'Mars' },
      { key: 'jupiter', name: 'Jupiter' },
      { key: 'saturn', name: 'Saturn' },
      { key: 'uranus', name: 'Uranus' },
      { key: 'neptune', name: 'Neptune' }
    ];

    // Calculer les positions normalisées et détecter les conjonctions
    const planetPositions = [];
    planets.forEach(planet => {
      const planetData = positions[planet.key];
      if (!planetData) return;

      // Normalize the position to a value between 0 and 30
      const positionInSign = planetData.degree;
      const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);
      const totalPosition = (signIndex * 30) + positionInSign;
      const normalizedPosition = totalPosition % 30;

      planetPositions.push({
        ...planet,
        data: planetData,
        normalizedPosition: normalizedPosition,
        angle: ((normalizedPosition * 12) - 90) * Math.PI / 180
      });
    });

    // Grouper les planètes par conjonctions (écart < 3°)
    const conjunctionGroups = [];
    const processedPlanets = new Set();

    planetPositions.forEach((planet, index) => {
      if (processedPlanets.has(index)) return;

      const group = [planet];
      processedPlanets.add(index);

      // Chercher les autres planètes proches
      planetPositions.forEach((otherPlanet, otherIndex) => {
        if (otherIndex === index || processedPlanets.has(otherIndex)) return;

        const angleDiff = Math.abs(planet.normalizedPosition - otherPlanet.normalizedPosition);
        const minAngleDiff = Math.min(angleDiff, 30 - angleDiff); // Gérer le passage 0°/30°

        if (minAngleDiff < 1) { // Conjonction si écart < 1°
          group.push(otherPlanet);
          processedPlanets.add(otherIndex);
        }
      });

      conjunctionGroups.push(group);
    });

    // Dessiner chaque groupe de conjonction
    conjunctionGroups.forEach(group => {
      if (group.length === 1) {
        // Planète seule - position normale
        const planet = group[0];
        const planetRadius = radius;
        const planetX = centerX + planetRadius * Math.cos(planet.angle);
        const planetY = centerY + planetRadius * Math.sin(planet.angle);

        drawSinglePlanet(ctx, centerX, centerY, planetX, planetY, planet, planetRadius, isTransit);
      } else {
        // Groupe de planètes en conjonction - niveaux différents
        const baseAngle = group[0].angle;

        group.forEach((planet, levelIndex) => {
          // Niveaux radiaux différents pour éviter la superposition
          const levelOffset = levelIndex * 0.15; // 15% de différence entre chaque niveau
          const planetRadius = radius * (1 - levelOffset);

          const planetX = centerX + planetRadius * Math.cos(baseAngle);
          const planetY = centerY + planetRadius * Math.sin(baseAngle);

          drawSinglePlanet(ctx, centerX, centerY, planetX, planetY, planet, planetRadius, isTransit, levelIndex);
        });
      }
    });
  }

  // Fonction pour dessiner une planète individuelle
  function drawSinglePlanet(ctx, centerX, centerY, planetX, planetY, planet, planetRadius, isTransit, level = 0) {
    // Ajuster la taille en fonction du niveau (plus petit pour les niveaux intérieurs)
    const baseSize = isTransit ? 12 : 14;
    const planetSize = baseSize - (level * 2); // Réduire la taille pour les niveaux intérieurs

    // Couleurs selon le type (natal = rouge, transit = bleu)
    const strokeColor = isTransit ? '#4A90E2' : '#FF0000'; // Rouge pour natal, bleu pour transit
    const symbolColor = isTransit ? '#4A90E2' : '#FF0000'; // Rouge pour natal, bleu pour transit

    // Draw a circle background for the planet (fond blanc)
    ctx.beginPath();
    ctx.arc(planetX, planetY, planetSize, 0, 2 * Math.PI);
    ctx.fillStyle = '#FFFFFF'; // Fond blanc pour tous
    ctx.fill();

    // Draw a small circle around the planet (contour coloré)
    ctx.beginPath();
    ctx.arc(planetX, planetY, planetSize, 0, 2 * Math.PI);
    ctx.strokeStyle = strokeColor;
    ctx.lineWidth = 2 + level; // Ligne plus épaisse pour les niveaux intérieurs
    ctx.stroke();

    // Draw planet symbol
    const fontSize = (isTransit ? 14 : 16) - (level * 2);
    ctx.font = `bold ${fontSize}px Arial`;
    ctx.fillStyle = symbolColor;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    const planetSymbol = PLANET_SYMBOLS[planet.name] || planet.name.substring(0, 2);
    ctx.fillText(planetSymbol, planetX, planetY);

    // Lignes vers le centre supprimées pour plus de clarté visuelle

    // Stocker les informations de la planète pour le système de tooltip
    if (!window.planetTooltipData) {
      window.planetTooltipData = [];
    }

    window.planetTooltipData.push({
      x: planetX,
      y: planetY,
      radius: planetSize,
      name: planet.name,
      degree: planet.data.degree.toFixed(1),
      sign: planet.data.sign,
      isTransit: isTransit,
      level: level
    });
  }

  // Système de tooltip pour les planètes
  function setupPlanetTooltips(canvas) {
    // Créer l'élément tooltip s'il n'existe pas
    let tooltip = document.getElementById('planet-tooltip');
    if (!tooltip) {
      tooltip = document.createElement('div');
      tooltip.id = 'planet-tooltip';
      tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        pointer-events: none;
        z-index: 1000;
        display: none;
        white-space: nowrap;
      `;
      document.body.appendChild(tooltip);
    }

    // Gestionnaire de mouvement de souris
    function handleMouseMove(event) {
      if (!window.planetTooltipData) return;

      const rect = canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      let hoveredPlanet = null;

      // Chercher la planète survolée
      for (const planet of window.planetTooltipData) {
        const distance = Math.sqrt(
          Math.pow(mouseX - planet.x, 2) + Math.pow(mouseY - planet.y, 2)
        );

        if (distance <= planet.radius + 5) { // Marge de 5px pour faciliter le survol
          hoveredPlanet = planet;
          break;
        }
      }

      if (hoveredPlanet) {
        // Afficher le tooltip
        const planetType = hoveredPlanet.isTransit ? 'Transit' : 'Natal';
        const levelText = hoveredPlanet.level > 0 ? ` (Niveau ${hoveredPlanet.level + 1})` : '';

        tooltip.innerHTML = `
          <div style="color: ${hoveredPlanet.isTransit ? '#4A90E2' : '#FF0000'}">
            ${hoveredPlanet.name} ${planetType}${levelText}
          </div>
          <div>${hoveredPlanet.degree}° ${hoveredPlanet.sign}</div>
        `;

        tooltip.style.display = 'block';
        tooltip.style.left = (event.clientX + 10) + 'px';
        tooltip.style.top = (event.clientY - 10) + 'px';

        canvas.style.cursor = 'pointer';
      } else {
        // Masquer le tooltip
        tooltip.style.display = 'none';
        canvas.style.cursor = 'default';
      }
    }

    // Gestionnaire de sortie de souris
    function handleMouseLeave() {
      tooltip.style.display = 'none';
      canvas.style.cursor = 'default';
    }

    // Supprimer les anciens événements s'ils existent
    canvas.removeEventListener('mousemove', canvas._planetMouseMove);
    canvas.removeEventListener('mouseleave', canvas._planetMouseLeave);

    // Ajouter les nouveaux événements
    canvas._planetMouseMove = handleMouseMove;
    canvas._planetMouseLeave = handleMouseLeave;
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseleave', handleMouseLeave);
  }

  // Fonction pour exporter les données du bulletin météo astrologique vers Google Calendar
  function exportToGoogleCalendar() {
    // Vérifier si des données météo sont disponibles
    if (!currentWeatherData || !currentWeatherData.days || currentWeatherData.days.length === 0) {
      alert('Aucune donnée météo astrologique disponible. Veuillez d\'abord calculer le bulletin météo.');
      return;
    }

    // Demander confirmation à l'utilisateur
    if (!confirm('Voulez-vous exporter tous les jours du bulletin météo astrologique vers Google Calendar ?')) {
      return;
    }

    try {
      // Créer les événements pour chaque jour
      const events = currentWeatherData.days.map(day => {
        return createGoogleCalendarUrl(day);
      });

      // Ouvrir le premier événement dans une nouvelle fenêtre
      if (events.length > 0) {
        window.open(events[0], '_blank');

        // Informer l'utilisateur des événements restants
        if (events.length > 1) {
          setTimeout(() => {
            alert(`Le premier événement a été ouvert dans Google Calendar. Il y a ${events.length - 1} autres événements à ajouter. Cliquez sur OK pour continuer avec le prochain événement.`);

            // Fonction récursive pour ouvrir les événements un par un
            function openNextEvent(index) {
              if (index < events.length) {
                window.open(events[index], '_blank');

                if (index < events.length - 1) {
                  setTimeout(() => {
                    if (confirm(`Événement ${index + 1}/${events.length} ajouté. Continuer avec le prochain événement ?`)) {
                      openNextEvent(index + 1);
                    }
                  }, 1000);
                } else {
                  alert('Tous les événements ont été exportés vers Google Calendar.');
                }
              }
            }

            // Commencer à partir du deuxième événement
            openNextEvent(1);
          }, 2000);
        } else {
          alert('L\'événement a été exporté vers Google Calendar.');
        }
      }
    } catch (error) {
      console.error('Erreur lors de l\'exportation vers Google Calendar:', error);
      alert('Une erreur s\'est produite lors de l\'exportation vers Google Calendar: ' + error.message);
    }
  }

  // Fonction pour exporter un jour spécifique vers Google Calendar
  function exportDayToGoogleCalendar(dateStr) {
    // Vérifier si des données météo sont disponibles
    if (!currentWeatherData || !currentWeatherData.days || currentWeatherData.days.length === 0) {
      alert('Aucune donnée météo astrologique disponible. Veuillez d\'abord calculer le bulletin météo.');
      return;
    }

    // Trouver le jour correspondant à la date spécifiée
    const day = currentWeatherData.days.find(d => d.date === dateStr);

    if (!day) {
      alert('Jour non trouvé dans les données météo.');
      return;
    }

    try {
      // Créer l'URL pour Google Calendar
      const url = createGoogleCalendarUrl(day);

      // Ouvrir l'événement dans une nouvelle fenêtre
      window.open(url, '_blank');

    } catch (error) {
      console.error('Erreur lors de l\'exportation vers Google Calendar:', error);
      alert('Une erreur s\'est produite lors de l\'exportation vers Google Calendar: ' + error.message);
    }
  }

  // Fonction utilitaire pour créer l'URL Google Calendar pour un jour
  function createGoogleCalendarUrl(day) {
    // Formater la date pour Google Calendar (format YYYYMMDD)
    const dateParts = day.date.split('/');
    const day_num = dateParts[0];
    const month = dateParts[1];
    const year = dateParts[2];

    // Vérifier que l'année est valide (4 chiffres)
    if (year.length !== 4) {
      console.error('Format d\'année invalide:', year);
      alert('Erreur de format de date. Veuillez vérifier que la date est au format JJ/MM/AAAA.');
      return null;
    }

    // Format YYYYMMDD pour Google Calendar
    const gcalDateFormat = `${year}${month}${day_num}`;

    // Déterminer l'icône météo en fonction du type de jour
    let weatherIcon = '';
    switch (day.weatherType) {
      case 'sunny': weatherIcon = '☀️ '; break;
      case 'partly-cloudy': weatherIcon = '⛅ '; break;
      case 'cloudy': weatherIcon = '☁️ '; break;
      case 'rainy': weatherIcon = '🌧️ '; break;
      case 'stormy': weatherIcon = '⛈️ '; break;
      default: weatherIcon = '';
    }

    // Créer le titre de l'événement
    const title = `${weatherIcon}Météo Astro: ${day.totalAspects} aspects (${day.masterTransitAspects} M.Transit)`;

    // Créer la description de l'événement
    let description = `Bulletin météo astrologique pour le ${day.date}\n\n`;
    description += `Total: ${day.totalAspects} aspects\n`;
    description += `Positifs: ${day.positiveAspects}\n`;
    description += `Négatifs: ${day.negativeAspects}\n`;
    description += `Aspects à l'astre maître: ${day.masterAspects}\n`;
    description += `Aspects à l'astre maître natal: ${day.masterNatalAspects}\n`;
    description += `Aspects à l'astre maître transit: ${day.masterTransitAspects}\n\n`;

    // Ajouter les aspects individuels si disponibles
    if (day.aspects && day.aspects.length > 0) {
      description += 'Aspects:\n';
      day.aspects.forEach(aspect => {
        description += `- ${aspect.transitPlanet} ${aspect.aspectName} ${aspect.natalPlanet}\n`;
      });
    }

    // Créer l'URL pour Google Calendar (format YYYYMMDD requis par Google Calendar)
    return `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(title)}&dates=${gcalDateFormat}T000000Z/${gcalDateFormat}T235959Z&details=${encodeURIComponent(description)}&sf=true&output=xml`;
  }

  // Variable pour stocker l'ID de l'intervalle
  let swissAstroWatchInterval = null;

  // Fonction pour démarrer la mise à jour en temps réel de la montre astro suisse
  function startSwissAstroWatchUpdate() {
    // Arrêter l'intervalle existant s'il y en a un
    if (swissAstroWatchInterval) {
      clearInterval(swissAstroWatchInterval);
    }

    // Mettre à jour la montre immédiatement
    updateSwissAstroWatch();

    // Mettre à jour la montre toutes les 1000 millisecondes (1 seconde)
    // pour éviter les boucles infinies avec calculateChart
    swissAstroWatchInterval = setInterval(function() {
      // Obtenir la date actuelle pour les calculs de transit avec précision à la milliseconde
      const realTimeDate = new Date();

      // Mettre à jour les champs de transit avec la date et l'heure actuelles
      if (transitDateInput && transitHourInput && transitMinuteInput) {
        transitDateInput.value = realTimeDate.toISOString().split('T')[0];
        transitHourInput.value = realTimeDate.getHours();
        transitMinuteInput.value = realTimeDate.getMinutes();

        // Mettre à jour le champ des secondes s'il existe
        const transitSecondInput = document.getElementById('transit-second');
        if (transitSecondInput) {
          transitSecondInput.value = realTimeDate.getSeconds();
        }
      }

      // Mettre à jour l'heure dans la légende de la montre
      updateWatchTime(realTimeDate);

      // Calculer les positions de transit pour l'heure actuelle en temps réel
      const realTimePositions = calculatePlanetaryPositions(realTimeDate);

      // Dessiner la montre astro suisse avec les positions mises à jour en temps réel
      drawSwissAstroWatch(realTimePositions);
    }, 1000);

    // Afficher un message dans la console pour confirmer que la mise à jour est active
    console.log("Montre astro suisse: mise à jour en temps réel activée à", new Date().toISOString());
  }

  // Démarrer la mise à jour en temps réel de la montre astro suisse au chargement
  setTimeout(startSwissAstroWatchUpdate, 1000);

  // Initialiser les contrôles de filtrage des aspects proportionnels
  initProportionalAspectsFilter();

  // Variable pour limiter les messages de console
  let lastLogTime = 0;

  // Fonction pour mettre à jour la montre astro suisse avec les positions actuelles
  function updateSwissAstroWatch() {
    try {
      // Obtenir la date actuelle pour les calculs de transit avec précision à la milliseconde
      const realTimeDate = new Date();

      // Mettre à jour les champs de transit avec la date et l'heure actuelles
      if (transitDateInput && transitHourInput && transitMinuteInput) {
        transitDateInput.value = realTimeDate.toISOString().split('T')[0];
        transitHourInput.value = realTimeDate.getHours();
        transitMinuteInput.value = realTimeDate.getMinutes();

        // Mettre à jour le champ des secondes s'il existe
        const transitSecondInput = document.getElementById('transit-second');
        if (transitSecondInput) {
          transitSecondInput.value = realTimeDate.getSeconds();
        }
      }

      // Calculer les positions de transit pour l'heure actuelle en temps réel
      const realTimePositions = calculatePlanetaryPositions(realTimeDate);

      // Dessiner la montre astro suisse avec les positions mises à jour en temps réel
      drawSwissAstroWatch(realTimePositions);

      // Mettre à jour l'heure dans la légende de la montre
      updateWatchTime(realTimeDate);

      // Afficher un message dans la console toutes les 5 secondes seulement
      const now = Date.now();
      if (now - lastLogTime > 5000) {
        console.log("Mise à jour de la montre astro suisse:", realTimeDate.toISOString());
        lastLogTime = now;
      }
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la montre astro suisse:", error);
    }
  }

  // Fonction pour mettre à jour l'affichage de l'heure dans la légende de la montre
  function updateWatchTime(date) {
    try {
      // Formater l'heure avec les millisecondes pour afficher la précision
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      const milliseconds = date.getMilliseconds().toString().padStart(3, '0');
      const timeStr = `${hours}:${minutes}:${seconds}.${milliseconds}`;

      // Mettre à jour l'élément d'affichage de l'heure s'il existe
      const timeDisplay = document.getElementById('watch-time-display');
      if (timeDisplay) {
        timeDisplay.textContent = timeStr;
        // Ajouter une classe pour mettre en évidence le changement
        timeDisplay.classList.add('time-updated');
        // Retirer la classe après une courte période pour créer un effet de clignotement
        setTimeout(() => {
          timeDisplay.classList.remove('time-updated');
        }, 200);
      }
    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'affichage de l'heure:", error);
    }
  }

  // Fonction pour mettre à jour l'affichage des commentaires du jour
  function updateDayCommentsDisplay() {
    const commentDateElement = document.getElementById('comment-date');
    const commentTextElement = document.getElementById('comment-text');
    const commentIndicatorElement = document.getElementById('comment-indicator');

    if (!commentDateElement || !commentTextElement || !commentIndicatorElement) {
      return;
    }

    // Obtenir la date de transit actuelle
    const transitDateInput = document.getElementById('transit-date');
    if (!transitDateInput || !transitDateInput.value) {
      return;
    }

    const transitDate = new Date(transitDateInput.value);
    const dateKey = formatDateKey(transitDate);

    // Formater la date pour l'affichage
    const displayDate = formatDateForDisplay(transitDate);
    commentDateElement.textContent = displayDate;

    // Récupérer les commentaires sauvegardés depuis localStorage (même système que le calendrier)
    const savedColors = JSON.parse(localStorage.getItem('miniCalendarColors') || '{}');
    const comment = savedColors[dateKey];

    if (comment && comment.comment && comment.comment.trim()) {
      // Il y a un commentaire pour ce jour
      commentTextElement.textContent = comment.comment;
      commentTextElement.classList.remove('no-comment');
      commentIndicatorElement.textContent = '💬';

      // Appliquer les couleurs personnalisées si elles existent
      const commentsDisplay = document.getElementById('day-comments-display');
      if (comment.background && comment.background !== 'default') {
        commentsDisplay.style.background = comment.background;
      } else {
        commentsDisplay.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';
      }

      if (comment.text && comment.text !== 'default') {
        commentTextElement.style.color = comment.text;
        commentDateElement.style.color = comment.text;
      } else {
        commentTextElement.style.color = 'white';
        commentDateElement.style.color = 'white';
      }
    } else {
      // Aucun commentaire pour ce jour
      commentTextElement.textContent = 'Aucun commentaire pour ce jour';
      commentTextElement.classList.add('no-comment');
      commentIndicatorElement.textContent = '📝';

      // Remettre les couleurs par défaut
      const commentsDisplay = document.getElementById('day-comments-display');
      commentsDisplay.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';
      commentTextElement.style.color = 'white';
      commentDateElement.style.color = 'white';
    }
  }

  // Fonction pour formater la date en clé de stockage (YYYY-M-D) - même format que le calendrier
  function formatDateKey(date) {
    const year = date.getFullYear();
    const month = date.getMonth(); // 0-11, pas de +1 car le calendrier utilise ce format
    const day = date.getDate();
    return `${year}-${month}-${day}`;
  }

  // Fonction pour formater la date pour l'affichage (DD Mois YYYY)
  function formatDateForDisplay(date) {
    const months = [
      'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
      'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
  }

  // Rendre la fonction disponible globalement
  window.updateDayCommentsDisplay = updateDayCommentsDisplay;

});
