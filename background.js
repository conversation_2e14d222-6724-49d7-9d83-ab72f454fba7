// Background script for the Sidereal Astrology Chrome Extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Sidereal Astrology Extension installed');

  // Initialize default settings if needed
  chrome.storage.local.get(['birthDate', 'transitDate', 'masterPlanet', 'weatherStartDate', 'weatherPeriod', 'aspectOrb'], (result) => {
    if (!result.birthDate) {
      // Set default birth date to current date
      const today = new Date().toISOString().split('T')[0];
      chrome.storage.local.set({ birthDate: today });
    }

    if (!result.transitDate) {
      // Set default transit date to current date
      const today = new Date().toISOString().split('T')[0];
      chrome.storage.local.set({ transitDate: today });
    }

    if (!result.masterPlanet) {
      // Set default master planet to Sun
      chrome.storage.local.set({ masterPlanet: 'sun' });
    }

    if (!result.weatherStartDate) {
      // Set default weather start date to current date
      const today = new Date().toISOString().split('T')[0];
      chrome.storage.local.set({ weatherStartDate: today });
    }

    if (!result.weatherPeriod) {
      // Set default weather period to 7 days
      chrome.storage.local.set({ weatherPeriod: 7 });
    }

    if (!result.aspectOrb) {
      // Set default aspect orb to 2 degrees
      chrome.storage.local.set({ aspectOrb: 2 });
    }
  });
});

// Open side panel when extension icon is clicked
chrome.action.onClicked.addListener((tab) => {
  // Open the side panel
  chrome.sidePanel.open({ tabId: tab.id });
});
