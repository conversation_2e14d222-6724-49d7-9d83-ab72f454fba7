/**
 * Gemini AI Integration for Sidereal Astrology Chrome Extension
 * This file handles the integration with Google's Gemini AI API
 */

// API Key for Gemini
const API_KEY = 'AIzaSyBgSik_a6DPbSqz5KOByopEkDRJ6EuOzW0';
// Le modèle sera sélectionné dynamiquement par l'utilisateur

// DOM Elements
let aiPromptInput;
let aiModelSelect;
let sendPromptBtn;
let aiResponseDiv;
let aiStatusDiv;

// Initialize the Gemini integration
function initGemini() {
  // Get DOM elements
  aiPromptInput = document.getElementById('ai-prompt');
  aiModelSelect = document.getElementById('ai-model');
  sendPromptBtn = document.getElementById('send-prompt-btn');
  aiResponseDiv = document.getElementById('ai-response');
  aiStatusDiv = document.getElementById('ai-status');

  // Add event listener for the send button
  if (sendPromptBtn) {
    sendPromptBtn.addEventListener('click', sendPromptToGemini);
  }
}

// Send the prompt to Gemini API
async function sendPromptToGemini() {
  // Get the prompt text
  const promptText = aiPromptInput.value.trim();

  // Check if prompt is empty
  if (!promptText) {
    aiStatusDiv.textContent = 'Veuillez entrer une question.';
    return;
  }

  // Get the selected model
  const selectedModel = aiModelSelect.value;

  // Construct the API URL with the selected model
  const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${selectedModel}:generateContent?key=${API_KEY}`;

  // Disable the button and show loading state
  sendPromptBtn.disabled = true;
  aiStatusDiv.innerHTML = '<span class="ai-thinking">Réflexion en cours</span>';
  aiResponseDiv.innerHTML = `<p>Traitement de votre question avec le modèle ${selectedModel}...</p>`;

  try {
    // Prepare the request data
    const requestData = {
      contents: [{
        parts: [{
          text: createAstrologicalPrompt(promptText)
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
      }
    };

    // Send the request to Gemini API
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    // Check if the request was successful
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.error?.message || `Statut: ${response.status}`;
      throw new Error(`Erreur API: ${errorMessage}`);
    }

    // Parse the response
    const data = await response.json();

    // Extract the response text
    let responseText = '';
    if (data.candidates && data.candidates.length > 0 &&
        data.candidates[0].content &&
        data.candidates[0].content.parts &&
        data.candidates[0].content.parts.length > 0) {
      responseText = data.candidates[0].content.parts[0].text;
    } else {
      responseText = "Je n'ai pas pu générer une réponse. Veuillez réessayer avec une question différente ou changer de modèle.";
    }

    // Format and display the response
    displayAIResponse(responseText);
    aiStatusDiv.textContent = 'Réponse reçue';
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    aiResponseDiv.innerHTML = `<p class="error">Erreur lors de la communication avec l'IA: ${error.message}</p>`;
    aiStatusDiv.textContent = 'Erreur';

    // Suggérer de changer de modèle
    aiResponseDiv.innerHTML += `<p class="suggestion">Essayez de sélectionner un autre modèle dans la liste déroulante.</p>`;
  } finally {
    // Re-enable the button
    sendPromptBtn.disabled = false;
  }
}

// Create a more detailed astrological prompt with context
function createAstrologicalPrompt(userPrompt) {
  // Get current chart data if available
  let chartContext = '';

  try {
    // Try to get birth and transit positions if they exist
    if (window.birthPositions && window.transitPositions) {
      chartContext = `\nInformations sur la carte du ciel actuelle:\n`;

      // Add birth positions
      chartContext += `\nPositions natales:\n`;
      for (const planet in window.birthPositions) {
        const position = window.birthPositions[planet];
        chartContext += `- ${getPlanetName(planet)}: ${position.sign} ${position.degree}°\n`;
      }

      // Add transit positions
      chartContext += `\nPositions de transit:\n`;
      for (const planet in window.transitPositions) {
        const position = window.transitPositions[planet];
        chartContext += `- ${getPlanetName(planet)}: ${position.sign} ${position.degree}°\n`;
      }
    }
  } catch (error) {
    console.error('Error getting chart context:', error);
    // Continue without chart context if there's an error
  }

  // Create the full prompt with system instructions and user query
  return `Tu es un expert en astrologie sidérale. Réponds à la question suivante de manière claire, précise et pédagogique.
Utilise tes connaissances en astrologie pour fournir une interprétation pertinente.
${chartContext}

Question de l'utilisateur: ${userPrompt}

Réponds en français, avec un ton bienveillant et accessible aux débutants en astrologie.`;
}

// Helper function to get planet name in French
function getPlanetName(planetKey) {
  const planetNames = {
    'sun': 'Soleil',
    'moon': 'Lune',
    'mercury': 'Mercure',
    'venus': 'Vénus',
    'mars': 'Mars',
    'jupiter': 'Jupiter',
    'saturn': 'Saturne',
    'uranus': 'Uranus',
    'neptune': 'Neptune',
    'pluto': 'Pluton'
  };

  return planetNames[planetKey] || planetKey;
}

// Display the AI response with formatting
function displayAIResponse(responseText) {
  // Format the response text (convert line breaks to HTML, etc.)
  const formattedText = responseText
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Set the formatted text to the response div
  aiResponseDiv.innerHTML = `<p>${formattedText}</p>`;
}

// Make the planetary positions available globally
window.makePositionsAvailableToGemini = function(birthPos, transitPos) {
  window.birthPositions = birthPos;
  window.transitPositions = transitPos;
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initGemini);
