/* Styles généraux */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
  width: 600px;
  height: 500px;
  overflow: auto;
}

.container {
  width: 100%;
  min-height: 500px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

header {
  background-color: #6a5acd;
  color: white;
  padding: 15px 20px;
  text-align: center;
}

h1 {
  font-size: 24px;
  margin-bottom: 15px;
}

h2 {
  font-size: 18px;
  color: #6a5acd;
  margin-bottom: 10px;
}

h3 {
  font-size: 16px;
  margin-bottom: 10px;
}

p {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

/* Onglets */
.tabs {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.tab-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 15px;
  cursor: pointer;
  border-radius: 4px 4px 0 0;
  font-size: 14px;
  transition: background-color 0.3s;
  margin: 0 5px;
}

.tab-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.tab-button.active {
  background-color: white;
  color: #6a5acd;
  font-weight: bold;
}

/* Conteneur de graphique */
.chart-container {
  padding: 20px;
}

.chart {
  display: none;
  margin-bottom: 20px;
}

.chart.active {
  display: block;
}

.chart-svg {
  width: 100%;
  height: 300px;
  min-height: 300px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 15px 0;
  background-color: #f9f9ff;
  position: relative;
  overflow: visible;
}

/* Légende */
.legend {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 15px;
  font-size: 14px;
}

.legend-item::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}

.birth-chart::before {
  border: 2px solid #e74c3c;
}

.current-transits::before {
  border: 2px solid #3498db;
}

/* Contrôles */
.controls {
  padding: 0 20px 20px;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}

input[type="date"],
input[type="time"],
input[type="text"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.button {
  background-color: #6a5acd;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.button:hover {
  background-color: #5849b8;
}

/* Symboles astrologiques */
.planet-symbol {
  font-size: 18px;
  font-weight: bold;
}

/* Styles SVG */
svg {
  display: block;
  width: 100%;
  height: 100%;
}

svg text {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.planet-symbol text {
  font-weight: bold;
  font-size: 14px;
}

.planet-symbol circle {
  fill: white;
  stroke-width: 2px;
}

.birth-chart circle {
  stroke: #e74c3c;
}

.current-transits circle {
  stroke: #3498db;
}

/* Responsive */
@media (max-width: 820px) {
  .container {
    width: 100%;
    border-radius: 0;
  }
}
