<!DOCTYPE html>
<html lang="fr" style="width: 600px; height: 500px;">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Montre Astro Sidérale</title>
  <link rel="stylesheet" href="styles.css">
  <script src="lib/swiss-ephemeris.js" defer></script>
  <script src="lib/d3.min.js" defer></script>
  <script src="popup.js" defer></script>
</head>
<body style="width: 600px; height: 500px; margin: 0; padding: 0;">
  <div class="container">
    <header>
      <h1>Montre Astro Sidérale</h1>
      <div class="tabs">
        <button id="tab-360" class="tab-button active">Mode 360°</button>
        <button id="tab-30" class="tab-button">Mode 30°</button>
        <button id="tab-nakshatra" class="tab-button">Mode Nakshatra 13.20°</button>
      </div>
    </header>

    <div class="chart-container">
      <div id="chart-360" class="chart active">
        <h2>Carte Zodiacale Complète (Mode 360°)</h2>
        <p>Cette carte montre les positions réelles des planètes dans la roue zodiacale complète de 360° avec les 12 signes.</p>
        <div id="chart-360-svg" class="chart-svg"></div>
        <div class="legend">
          <div class="legend-item birth-chart">Carte de Naissance</div>
          <div class="legend-item current-transits">Transits Actuels</div>
        </div>
      </div>

      <div id="chart-30" class="chart">
        <h2>Carte Zodiacale Proportionnelle (Mode 30°)</h2>
        <p>Cette carte montre les planètes positionnées par leur degré dans leur signe (0-30°), indépendamment du signe lui-même.</p>
        <div id="chart-30-svg" class="chart-svg"></div>
        <div class="legend">
          <div class="legend-item birth-chart">Carte de Naissance</div>
          <div class="legend-item current-transits">Transits Actuels</div>
        </div>
      </div>

      <div id="chart-nakshatra" class="chart">
        <h2>Carte Zodiacale Proportionnelle (Mode 13.20° - Nakshatras)</h2>
        <p>Cette carte montre les planètes positionnées par leur degré dans un segment de nakshatra (0-13.20°), correspondant au système des 27 Nakshatras.</p>
        <div id="chart-nakshatra-svg" class="chart-svg"></div>
        <div class="legend">
          <div class="legend-item birth-chart">Carte de Naissance</div>
          <div class="legend-item current-transits">Transits Actuels</div>
        </div>
      </div>
    </div>

    <div class="controls">
      <div class="birth-data">
        <h3>Données de naissance</h3>
        <div class="form-group">
          <label for="birth-date">Date de naissance:</label>
          <input type="date" id="birth-date" name="birth-date">
        </div>
        <div class="form-group">
          <label for="birth-time">Heure de naissance:</label>
          <input type="time" id="birth-time" name="birth-time">
        </div>
        <div class="form-group">
          <label for="birth-location">Lieu de naissance:</label>
          <input type="text" id="birth-location" name="birth-location" placeholder="Ville, Pays">
        </div>
        <button id="calculate-chart" class="button">Calculer la carte</button>
      </div>
    </div>
  </div>
</body>
</html>
