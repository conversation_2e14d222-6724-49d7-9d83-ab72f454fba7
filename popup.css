/* Apple-inspired design */
:root {
  --primary-color: #0071e3;
  --secondary-color: #86868b;
  --background-color: #f5f5f7;
  --card-background: #ffffff;
  --text-color: #1d1d1f;
  --border-radius: 12px;
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
}

.container {
  width: 600px;
  padding: 20px;
}

header {
  text-align: center;
  margin-bottom: 20px;
}

h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
}

h2 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 12px;
}

.date-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  background-color: var(--card-background);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.input-group {
  flex: 1;
  min-width: 200px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--secondary-color);
}

input[type="date"] {
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-family: var(--font-family);
  font-size: 16px;
  outline: none;
  transition: border-color 0.2s;
}

input[type="date"]:focus {
  border-color: var(--primary-color);
}

button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 16px;
  width: 100%;
}

button:hover {
  background-color: #0062cc;
}

.chart-container {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  background-color: var(--card-background);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.chart-wrapper {
  flex: 1;
  min-width: 300px;
  height: 300px;
  position: relative;
}

#chart-canvas {
  width: 100%;
  height: 100%;
}

.planet-positions {
  flex: 1;
  min-width: 200px;
}

.positions-list {
  font-size: 14px;
}

.planet-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.planet-name {
  font-weight: 500;
}

.planet-degree {
  color: var(--secondary-color);
}

.transit-container {
  background-color: var(--card-background);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.transits-list {
  font-size: 14px;
}

.transit-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

/* Planet symbols */
.planet-symbol {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  text-align: center;
  font-size: 18px;
}

/* Zodiac sign styling */
.sign {
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .container {
    width: 100%;
  }
  
  .chart-container {
    flex-direction: column;
  }
}
