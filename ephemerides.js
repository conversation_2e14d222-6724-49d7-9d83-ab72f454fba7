/**
 * Ephemerides data and calculations
 * This file contains additional data and functions for astronomical calculations
 */

// Ayanamsa (precession correction for sidereal zodiac)
// Using <PERSON><PERSON><PERSON>yana<PERSON> as a standard
function getAyanamsa(date) {
  // Calculate Julian day number for J2000 (January 1, 2000, 12:00 UTC)
  const j2000 = 2451545.0;
  
  // Calculate Julian day for the given date
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  
  let jd = 367 * year - Math.floor(7 * (year + Math.floor((month + 9) / 12)) / 4) +
           Math.floor(275 * month / 9) + day + 1721013.5;
  
  // Add time of day
  jd += (date.getUTCHours() + date.getUTCMinutes() / 60.0 + date.getUTCSeconds() / 3600.0) / 24.0;
  
  // Calculate T, the time in Julian centuries from J2000
  const T = (jd - j2000) / 36525;
  
  // <PERSON><PERSON><PERSON> formula (simplified)
  // Base value at J2000 plus the precession rate
  const ayanamsa = 23.85 + 0.0001 * T * 1500; // Approximately 50.3 arcseconds per year
  
  return ayanamsa;
}

// Calculate aspects between planets
function calculateAspects(birthPositions, transitPositions) {
  const aspects = [];
  const orbs = {
    "conjunction": 8,     // 0°
    "opposition": 8,      // 180°
    "trine": 6,           // 120°
    "square": 6,          // 90°
    "sextile": 4,         // 60°
    "quincunx": 3,        // 150°
    "semisextile": 2      // 30°
  };
  
  const aspectAngles = {
    "conjunction": 0,
    "opposition": 180,
    "trine": 120,
    "square": 90,
    "sextile": 60,
    "quincunx": 150,
    "semisextile": 30
  };
  
  const birthPlanets = ["sun", "moon", "mercury", "venus", "mars", "jupiter", "saturn", "uranus", "neptune"];
  const transitPlanets = ["sun", "moon", "mercury", "venus", "mars", "jupiter", "saturn", "uranus", "neptune"];
  
  // Check aspects between birth chart and transit planets
  for (const birthPlanet of birthPlanets) {
    for (const transitPlanet of transitPlanets) {
      const birthLong = birthPositions[birthPlanet].siderealLongitude;
      const transitLong = transitPositions[transitPlanet].siderealLongitude;
      
      // Calculate the angular difference
      let diff = Math.abs(transitLong - birthLong);
      if (diff > 180) diff = 360 - diff;
      
      // Check for aspects
      for (const [aspectName, aspectAngle] of Object.entries(aspectAngles)) {
        const orb = orbs[aspectName];
        if (Math.abs(diff - aspectAngle) <= orb) {
          aspects.push({
            birthPlanet: birthPlanet,
            transitPlanet: transitPlanet,
            aspect: aspectName,
            orb: (diff - aspectAngle).toFixed(2)
          });
        }
      }
    }
  }
  
  return aspects;
}

// Calculate houses using Placidus system (simplified)
function calculateHouses(date, latitude, longitude) {
  // This is a simplified placeholder for house calculations
  // In a real implementation, this would use the Placidus house system or another method
  
  // For now, we'll return equally spaced houses as a placeholder
  const houses = [];
  const ascendant = 0; // This would be calculated based on time and location
  
  for (let i = 0; i < 12; i++) {
    houses.push((ascendant + i * 30) % 360);
  }
  
  return houses;
}

// Calculate lunar phase
function calculateLunarPhase(sunLong, moonLong) {
  // Angular distance between Sun and Moon
  let phase = (moonLong - sunLong) % 360;
  if (phase < 0) phase += 360;
  
  // Convert to phase name
  let phaseName;
  if (phase < 45) phaseName = "New Moon";
  else if (phase < 90) phaseName = "Waxing Crescent";
  else if (phase < 135) phaseName = "First Quarter";
  else if (phase < 180) phaseName = "Waxing Gibbous";
  else if (phase < 225) phaseName = "Full Moon";
  else if (phase < 270) phaseName = "Waning Gibbous";
  else if (phase < 315) phaseName = "Last Quarter";
  else phaseName = "Waning Crescent";
  
  return {
    angle: phase,
    name: phaseName,
    percentage: (1 - Math.cos((phase * Math.PI) / 180)) / 2 * 100
  };
}

// Calculate retrograde status of planets
function isRetrograde(planet, d) {
  // This is a simplified approach - in reality, we would calculate positions
  // for dates a few days apart and check if longitude is decreasing
  
  // For demonstration, using typical retrograde periods
  // In a real implementation, this would be calculated dynamically
  switch (planet) {
    case "Mercury":
      // Mercury is retrograde about 3 times a year for about 3 weeks
      return Math.sin(d / 30) > 0.8;
    case "Venus":
      // Venus is retrograde about every 18 months for about 6 weeks
      return Math.sin(d / 180) > 0.9;
    case "Mars":
      // Mars is retrograde about every 26 months for about 2-2.5 months
      return Math.sin(d / 260) > 0.9;
    case "Jupiter":
      // Jupiter is retrograde about 4 months each year
      return Math.sin(d / 90) > 0.3;
    case "Saturn":
      // Saturn is retrograde about 4.5 months each year
      return Math.sin(d / 90) > 0.2;
    case "Uranus":
      // Uranus is retrograde about 5 months each year
      return Math.sin(d / 90) > 0.1;
    case "Neptune":
      // Neptune is retrograde about 5 months each year
      return Math.sin(d / 90) > 0;
    default:
      return false;
  }
}
