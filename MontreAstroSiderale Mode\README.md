# Montre Astro Sidérale

Une extension Chrome pour visualiser les positions planétaires en astrologie sidérale avec différents modes d'affichage.

## Fonctionnalités

- Affichage des positions planétaires en astrologie sidérale
- Trois modes de visualisation :
  - Mode 360° : Carte zodiacale complète avec les 12 signes
  - Mode 30° : Carte proportionnelle montrant les planètes par leur degré dans leur signe
  - Mode 13.20° : Carte des Nakshatras (système lunaire indien)
- Comparaison entre la carte de naissance et les transits actuels
- Sauvegarde des données de naissance pour une utilisation ultérieure

## Installation

### Installation en mode développement

1. Téléchargez ou clonez ce dépôt sur votre ordinateur
2. Ouvrez Chrome et accédez à `chrome://extensions/`
3. Activez le "Mode développeur" en haut à droite
4. C<PERSON>z sur "Charger l'extension non empaquetée"
5. Sélection<PERSON>z le dossier contenant les fichiers de l'extension

### Installation depuis le Chrome Web Store (à venir)

L'extension sera bientôt disponible sur le Chrome Web Store.

## Utilisation

1. Cliquez sur l'icône de l'extension dans la barre d'outils de Chrome
2. Entrez vos données de naissance (date, heure et lieu)
3. Cliquez sur "Calculer la carte"
4. Naviguez entre les différents modes d'affichage en utilisant les onglets

## Astrologie Sidérale vs Tropicale

Cette extension utilise l'astrologie sidérale, qui prend en compte la précession des équinoxes et se base sur les positions réelles des constellations. Contrairement à l'astrologie tropicale occidentale qui est basée sur les saisons, l'astrologie sidérale est alignée avec les positions astronomiques actuelles des constellations.

L'ayanamsa (décalage entre le zodiaque tropical et sidéral) utilisé dans cette extension est d'environ 24 degrés, ce qui correspond approximativement à l'ayanamsa de Lahiri, couramment utilisé en astrologie védique.

## Développement futur

- Amélioration des calculs astrologiques avec des éphémérides plus précises
- Ajout de plus d'informations sur les aspects planétaires
- Développement d'une version Android de l'application
- Intégration d'interprétations astrologiques

## Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

## Crédits

- Développé par [Votre Nom]
- Utilise une version simplifiée de D3.js pour les visualisations
- Inspiré par divers logiciels d'astrologie sidérale
