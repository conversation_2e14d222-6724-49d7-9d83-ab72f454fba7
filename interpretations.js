/**
 * Module pour gérer les interprétations des aspects transit-natal
 */

// Objet pour stocker les interprétations
let transitNatalInterpretations = {};

// Objet pour stocker les interprétations personnalisées
let customInterpretations = {};

// Fonction pour charger le fichier d'interprétations
async function loadTransitNatalInterpretations() {
  try {
    // Charger le fichier d'interprétations
    const response = await fetch('Interprétations transit natal/Francais - Transit natal.txt');
    const text = await response.text();
    parseTransitNatalInterpretations(text);

    // Charger les interprétations personnalisées depuis le stockage local
    loadCustomInterpretations();

    console.log('Interprétations transit-natal chargées avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors du chargement des interprétations transit-natal:', error);
    return false;
  }
}

// Fonction pour charger les interprétations personnalisées depuis le stockage local
function loadCustomInterpretations() {
  chrome.storage.local.get(['customInterpretations'], function(result) {
    if (result.customInterpretations) {
      customInterpretations = result.customInterpretations;
      console.log('Interprétations personnalisées chargées:', Object.keys(customInterpretations).length);
    }
  });
}

// Fonction pour sauvegarder les interprétations personnalisées dans le stockage local
function saveCustomInterpretation(key, text) {
  // Mettre à jour l'objet des interprétations personnalisées
  customInterpretations[key] = text;

  // Sauvegarder dans le stockage local
  chrome.storage.local.set({
    customInterpretations: customInterpretations
  }, function() {
    console.log('Interprétation personnalisée sauvegardée pour:', key);
  });
}

// Fonction pour analyser le contenu du fichier d'interprétations
function parseTransitNatalInterpretations(text) {
  // Nettoyer le texte (supprimer les lignes d'en-tête)
  const cleanedText = text.replace(/^Okay.*?\`\`\`plaintext/s, '').replace(/\`\`\`$/s, '');

  // Diviser le texte en lignes
  const lines = cleanedText.split('\n');

  let currentKey = null;
  let currentText = '';

  // Parcourir chaque ligne
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Si la ligne est vide, passer à la suivante
    if (line === '') continue;

    // Si la ligne commence par '[', c'est une nouvelle clé
    if (line.startsWith('[') && line.includes(']')) {
      // Si nous avons déjà une clé, enregistrer l'interprétation précédente
      if (currentKey) {
        transitNatalInterpretations[currentKey] = currentText.trim();
        currentText = '';
      }

      // Extraire la nouvelle clé
      currentKey = line.substring(1, line.indexOf(']'));
    }
    // Sinon, c'est une partie du texte d'interprétation
    else if (currentKey && !line.startsWith('//')) {
      currentText += line + ' ';
    }
  }

  // Enregistrer la dernière interprétation
  if (currentKey && currentText) {
    transitNatalInterpretations[currentKey] = currentText.trim();
  }
}

// Fonction pour obtenir l'interprétation d'un aspect spécifique
function getTransitNatalInterpretation(transitPlanet, aspect, natalPlanet) {
  // Convertir les noms de planètes en codes à 2 lettres
  const transitCode = getPlanetCode(transitPlanet);
  const natalCode = getPlanetCode(natalPlanet);
  const aspectCode = getAspectCode(aspect);

  // Créer la clé au format [TR.ASP.NA]
  const key = `${transitCode}.${aspectCode}.${natalCode}`;

  // Vérifier d'abord si une interprétation personnalisée existe
  if (customInterpretations[key]) {
    return customInterpretations[key];
  }

  // Sinon, retourner l'interprétation standard si elle existe
  return transitNatalInterpretations[key] ||
    `Aucune interprétation disponible pour ${transitPlanet} ${aspect} ${natalPlanet}`;
}

// Fonction pour obtenir la clé d'interprétation
function getInterpretationKey(transitPlanet, aspect, natalPlanet) {
  const transitCode = getPlanetCode(transitPlanet);
  const natalCode = getPlanetCode(natalPlanet);
  const aspectCode = getAspectCode(aspect);

  return `${transitCode}.${aspectCode}.${natalCode}`;
}

// Fonction pour convertir le nom d'une planète en code à 2 lettres
function getPlanetCode(planetName) {
  const planetCodes = {
    'sun': 'SU',
    'moon': 'MO',
    'mercury': 'ME',
    'venus': 'VE',
    'mars': 'MA',
    'jupiter': 'JU',
    'saturn': 'SA',
    'uranus': 'UR',
    'neptune': 'NE',
    'pluto': 'PL',
    'ascendant': 'AS',
    'midheaven': 'MC'
  };

  return planetCodes[planetName.toLowerCase()] || planetName.toUpperCase();
}

// Fonction pour convertir le nom d'un aspect en code à 3 lettres
function getAspectCode(aspectName) {
  const aspectCodes = {
    'conjunction': 'CNJ',
    'opposition': 'OPP',
    'square': 'SQR',
    'trine': 'TRI',
    'sextile': 'SXT',
    'semisquare': 'SSQ',
    'sesquiquadrate': 'SQQ',
    'semisextile': 'SSX'
  };

  return aspectCodes[aspectName.toLowerCase()] || aspectName.toUpperCase();
}

// Fonction pour afficher l'interprétation dans une fenêtre modale
function showTransitNatalInterpretation(transitPlanet, aspect, natalPlanet) {
  const interpretation = getTransitNatalInterpretation(transitPlanet, aspect, natalPlanet);

  // Mettre à jour le contenu de la fenêtre modale
  const modal = document.getElementById('interpretation-modal');
  const modalTitle = document.getElementById('interpretation-modal-title');
  const modalContent = document.getElementById('interpretation-modal-content');
  const editContent = document.getElementById('interpretation-edit-content');

  // Formater les noms des planètes pour l'affichage
  const formattedTransitPlanet = transitPlanet.charAt(0).toUpperCase() + transitPlanet.slice(1);
  const formattedNatalPlanet = natalPlanet.charAt(0).toUpperCase() + natalPlanet.slice(1);
  const formattedAspect = aspect.charAt(0).toUpperCase() + aspect.slice(1);

  // Mettre à jour le titre et le contenu
  modalTitle.textContent = `${formattedTransitPlanet} ${formattedAspect} ${formattedNatalPlanet}`;
  modalContent.textContent = interpretation;
  editContent.value = interpretation;

  // Stocker les informations sur l'aspect en cours dans des attributs data
  modal.dataset.transitPlanet = transitPlanet;
  modal.dataset.aspect = aspect;
  modal.dataset.natalPlanet = natalPlanet;

  // Afficher le mode lecture et masquer le mode édition
  document.getElementById('interpretation-view-mode').style.display = 'block';
  document.getElementById('interpretation-edit-mode').style.display = 'none';

  // Afficher la fenêtre modale
  modal.style.display = 'block';

  // Ajouter les gestionnaires d'événements pour les boutons
  setupInterpretationModalEvents();
}

// Fonction pour configurer les événements de la fenêtre modale d'interprétation
function setupInterpretationModalEvents() {
  const modal = document.getElementById('interpretation-modal');
  const editBtn = document.getElementById('edit-interpretation-btn');
  const saveBtn = document.getElementById('save-interpretation-btn');
  const cancelBtn = document.getElementById('cancel-interpretation-btn');
  const viewMode = document.getElementById('interpretation-view-mode');
  const editMode = document.getElementById('interpretation-edit-mode');

  // Bouton pour passer en mode édition
  editBtn.onclick = function() {
    viewMode.style.display = 'none';
    editMode.style.display = 'block';
  };

  // Bouton pour sauvegarder l'interprétation
  saveBtn.onclick = function() {
    const transitPlanet = modal.dataset.transitPlanet;
    const aspect = modal.dataset.aspect;
    const natalPlanet = modal.dataset.natalPlanet;
    const newText = document.getElementById('interpretation-edit-content').value;

    // Obtenir la clé d'interprétation
    const key = getInterpretationKey(transitPlanet, aspect, natalPlanet);

    // Sauvegarder l'interprétation personnalisée
    saveCustomInterpretation(key, newText);

    // Mettre à jour l'affichage
    document.getElementById('interpretation-modal-content').textContent = newText;

    // Revenir au mode lecture
    viewMode.style.display = 'block';
    editMode.style.display = 'none';
  };

  // Bouton pour annuler l'édition
  cancelBtn.onclick = function() {
    // Revenir au mode lecture sans sauvegarder
    viewMode.style.display = 'block';
    editMode.style.display = 'none';
  };
}

// Exporter les fonctions
window.loadTransitNatalInterpretations = loadTransitNatalInterpretations;
window.getTransitNatalInterpretation = getTransitNatalInterpretation;
window.showTransitNatalInterpretation = showTransitNatalInterpretation;
window.saveCustomInterpretation = saveCustomInterpretation;
window.getInterpretationKey = getInterpretationKey;
