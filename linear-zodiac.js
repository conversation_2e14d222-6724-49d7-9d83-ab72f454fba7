// Système de zodiaque linéaire 30° pour multi-transit
class LinearZodiacSystem {
  constructor() {
    this.modal = null;
    this.canvas = null;
    this.ctx = null;
    this.currentBirthPositions = null;
    this.currentTransitPositions = null;
    this.multiTransits = [];
    this.isVisible = false;
    this.is360Mode = false; // Mode 30° par défaut
    this.isCircularMode = false; // Mode linéaire par défaut

    // Système de marquage de secteurs
    this.markedSectors = new Map(); // Map<sectorNumber, {planets: Set, message: string, color: string}>
    this.sectorAlerts = []; // Alertes actives

    this.init();
  }

  init() {
    // Charger les modes sauvegardés depuis localStorage
    this.loadSavedModes();

    // Initialiser les éléments DOM
    this.section = document.getElementById('linear-zodiac-section');
    this.canvas = document.getElementById('linear-zodiac-canvas');

    if (this.canvas) {
      this.ctx = this.canvas.getContext('2d');
    }

    // Ajouter les event listeners
    this.setupEventListeners();
  }

  loadSavedModes() {
    try {
      // Charger le mode 360°/30°
      const saved360Mode = localStorage.getItem('linearZodiac360Mode');
      if (saved360Mode !== null) {
        this.is360Mode = saved360Mode === 'true';
      }

      // Charger le mode circulaire/linéaire
      const savedCircularMode = localStorage.getItem('linearZodiacCircularMode');
      if (savedCircularMode !== null) {
        this.isCircularMode = savedCircularMode === 'true';
      }
    } catch (error) {
      console.warn('Impossible de charger les modes sauvegardés depuis localStorage:', error);
    }
  }

  setupEventListeners() {
    // Bouton d'ouverture
    const openBtn = document.getElementById('show-linear-zodiac-btn');
    if (openBtn) {
      openBtn.addEventListener('click', () => this.show());
    }

    // Bouton de fermeture
    const closeBtn = document.getElementById('linear-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hide());
    }

    // Bouton d'actualisation
    const refreshBtn = document.getElementById('linear-refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.refresh());
    }

    // Bouton de téléchargement
    const downloadBtn = document.getElementById('linear-download-btn');
    if (downloadBtn) {
      downloadBtn.addEventListener('click', () => this.download());
    }

    // Bouton de bascule 360°
    const mode360Btn = document.getElementById('linear-360-mode-btn');
    if (mode360Btn) {
      mode360Btn.addEventListener('click', () => this.toggle360Mode());
    }

    // Bouton de bascule mode circulaire
    const circularModeBtn = document.getElementById('linear-circular-mode-btn');
    if (circularModeBtn) {
      circularModeBtn.addEventListener('click', () => this.toggleCircularMode());
    }

    // Multi-transit controls
    this.setupMultiTransitListeners();

    // Bouton de marquage de secteurs
    this.setupSectorMarkingButton();

    // Synchronisation avec la timebox
    this.setupTimeboxSynchronization();
  }

  setupMultiTransitListeners() {
    // Toggle formulaire multi-transit
    const toggleBtn = document.getElementById('toggle-multi-transit-form');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.toggleMultiTransitForm());
    }

    // Ajouter transit
    const addBtn = document.getElementById('add-multi-transit');
    if (addBtn) {
      addBtn.addEventListener('click', () => this.addMultiTransit());
    }

    // Annuler ajout
    const cancelBtn = document.getElementById('cancel-multi-transit');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.cancelMultiTransitForm());
    }

    // Initialiser la date par défaut
    const dateInput = document.getElementById('multi-transit-date');
    if (dateInput && !dateInput.value) {
      const today = new Date();
      dateInput.value = today.toISOString().split('T')[0];
    }
  }

  toggleMultiTransitForm() {
    const form = document.getElementById('multi-transit-form');
    const toggleBtn = document.getElementById('toggle-multi-transit-form');

    if (form.style.display === 'none') {
      form.style.display = 'block';
      toggleBtn.textContent = '✕ Annuler';
      toggleBtn.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
    } else {
      this.cancelMultiTransitForm();
    }
  }

  cancelMultiTransitForm() {
    const form = document.getElementById('multi-transit-form');
    const toggleBtn = document.getElementById('toggle-multi-transit-form');

    form.style.display = 'none';
    toggleBtn.textContent = '+ Ajouter Transit';
    toggleBtn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

    // Reset form
    document.getElementById('multi-transit-date').value = new Date().toISOString().split('T')[0];
    document.getElementById('multi-transit-hour').value = '12';
    document.getElementById('multi-transit-minute').value = '0';
  }

  async addMultiTransit() {
    const dateInput = document.getElementById('multi-transit-date');
    const hourInput = document.getElementById('multi-transit-hour');
    const minuteInput = document.getElementById('multi-transit-minute');

    const date = dateInput.value;
    const hour = parseInt(hourInput.value);
    const minute = parseInt(minuteInput.value);

    if (!date) {
      alert('Veuillez sélectionner une date');
      return;
    }

    // Créer la date complète
    const transitDate = new Date(date);
    transitDate.setHours(hour, minute, 0, 0);

    try {
      // Calculer les positions planétaires pour cette date
      const positions = await this.calculateTransitPositions(transitDate);

      // Ajouter le transit à la liste
      const transit = {
        date: transitDate.toISOString(),
        positions: positions,
        id: Date.now() // ID unique
      };

      if (!this.multiTransits) {
        this.multiTransits = [];
      }

      this.multiTransits.push(transit);

      // Mettre à jour l'affichage
      this.updateMultiTransitList();
      this.cancelMultiTransitForm();

      // Redessiner le zodiaque
      this.draw();

    } catch (error) {
      console.error('Erreur lors du calcul des positions:', error);
      alert('Erreur lors du calcul des positions planétaires');
    }
  }

  async calculateTransitPositions(date) {
    // Utiliser la même fonction que le système principal
    if (typeof calculatePlanetaryPositions === 'function') {
      return await calculatePlanetaryPositions(date);
    } else {
      // Fallback si la fonction n'est pas disponible
      console.warn('calculatePlanetaryPositions not available, using mock data');
      return this.getMockPositions();
    }
  }

  getMockPositions() {
    // Positions fictives pour test
    return {
      sun: { sign: 'Aries', degree: 15.5 },
      moon: { sign: 'Taurus', degree: 8.2 },
      mercury: { sign: 'Pisces', degree: 22.1 },
      venus: { sign: 'Gemini', degree: 3.7 },
      mars: { sign: 'Leo', degree: 18.9 },
      jupiter: { sign: 'Virgo', degree: 12.4 },
      saturn: { sign: 'Capricorn', degree: 25.8 },
      uranus: { sign: 'Aquarius', degree: 6.3 },
      neptune: { sign: 'Scorpio', degree: 14.7 }
    };
  }

  updateMultiTransitList() {
    const listContainer = document.getElementById('multi-transit-list');
    if (!listContainer) return;

    if (!this.multiTransits || this.multiTransits.length === 0) {
      listContainer.innerHTML = '<div class="multi-transit-empty">Aucun transit ajouté</div>';
      return;
    }

    const colors = ['#FF6B35', '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];

    listContainer.innerHTML = this.multiTransits.map((transit, index) => {
      const date = new Date(transit.date);
      const color = colors[index % colors.length];
      const isDefault = transit.isDefault || false;

      if (isDefault) {
        // Affichage avec champs éditables pour les transits par défaut
        return `
          <div class="multi-transit-item default-transit" data-transit-id="${transit.id}">
            <div class="multi-transit-info">
              <div class="multi-transit-color" style="background-color: ${color}"></div>
              <div class="default-transit-label">Transit ${index + 1} (défaut):</div>
            </div>
            <div class="default-transit-fields">
              <div class="default-transit-input-group">
                <label>Date:</label>
                <input type="date" class="default-transit-date" data-transit-id="${transit.id}"
                       value="${date.toISOString().split('T')[0]}">
              </div>
              <div class="default-transit-input-group">
                <label>Heure:</label>
                <input type="number" class="default-transit-hour" data-transit-id="${transit.id}"
                       min="0" max="23" value="${date.getHours()}">
              </div>
              <div class="default-transit-input-group">
                <label>Min:</label>
                <input type="number" class="default-transit-minute" data-transit-id="${transit.id}"
                       min="0" max="59" value="${date.getMinutes()}">
              </div>
              <button class="default-transit-update" data-transit-id="${transit.id}">
                Mettre à jour
              </button>
            </div>
          </div>
        `;
      } else {
        // Affichage normal pour les transits ajoutés manuellement
        return `
          <div class="multi-transit-item" data-transit-id="${transit.id}">
            <div class="multi-transit-info">
              <div class="multi-transit-color" style="background-color: ${color}"></div>
              <div class="multi-transit-date">${date.toLocaleDateString('fr-FR')}</div>
              <div class="multi-transit-time">${date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}</div>
            </div>
            <button class="multi-transit-remove" data-transit-id="${transit.id}">
              Supprimer
            </button>
          </div>
        `;
      }
    }).join('');

    // Ajouter les event listeners pour les boutons supprimer
    const removeButtons = listContainer.querySelectorAll('.multi-transit-remove');
    removeButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const transitId = parseInt(e.target.getAttribute('data-transit-id'));
        this.removeMultiTransit(transitId);
      });
    });

    // Ajouter les event listeners pour les boutons de mise à jour des transits par défaut
    const updateButtons = listContainer.querySelectorAll('.default-transit-update');
    updateButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const transitId = parseInt(e.target.getAttribute('data-transit-id'));
        this.updateDefaultTransit(transitId);
      });
    });
  }

  removeMultiTransit(transitId) {
    if (!this.multiTransits) return;

    this.multiTransits = this.multiTransits.filter(transit => transit.id !== transitId);
    this.updateMultiTransitList();
    this.draw();
  }

  async updateDefaultTransit(transitId) {
    if (!this.multiTransits) return;

    // Trouver le transit à mettre à jour
    const transitIndex = this.multiTransits.findIndex(transit => transit.id === transitId);
    if (transitIndex === -1) return;

    // Récupérer les nouvelles valeurs des champs
    const dateInput = document.querySelector(`.default-transit-date[data-transit-id="${transitId}"]`);
    const hourInput = document.querySelector(`.default-transit-hour[data-transit-id="${transitId}"]`);
    const minuteInput = document.querySelector(`.default-transit-minute[data-transit-id="${transitId}"]`);

    if (!dateInput || !hourInput || !minuteInput) return;

    const date = dateInput.value;
    const hour = parseInt(hourInput.value) || 0;
    const minute = parseInt(minuteInput.value) || 0;

    // Créer la nouvelle date
    const newDate = new Date(date);
    newDate.setHours(hour, minute, 0, 0);

    try {
      // Calculer les nouvelles positions planétaires
      const newPositions = await this.calculateTransitPositions(newDate);

      // Mettre à jour le transit
      this.multiTransits[transitIndex] = {
        ...this.multiTransits[transitIndex],
        date: newDate.toISOString(),
        positions: newPositions
      };

      // Sauvegarder dans window pour persistance
      window.multiTransits = this.multiTransits;

      // Sauvegarder dans chrome.storage.local pour persistance permanente
      await this.saveDefaultTransitsToStorage();

      // Mettre à jour l'affichage
      this.updateMultiTransitList();
      this.draw();

      // Afficher un message de confirmation
      this.showUpdateMessage(`Transit ${transitIndex + 1} mis à jour avec succès`);

    } catch (error) {
      console.error('Erreur lors de la mise à jour du transit:', error);
      alert('Erreur lors du calcul des nouvelles positions planétaires');
    }
  }

  async saveDefaultTransitsToStorage() {
    try {
      // Extraire seulement les transits par défaut
      const defaultTransits = this.multiTransits.filter(transit => transit.isDefault);

      // Sauvegarder les dates des transits par défaut
      const defaultTransitDates = defaultTransits.map(transit => ({
        id: transit.id,
        date: transit.date,
        isDefault: transit.isDefault
      }));

      await chrome.storage.local.set({
        defaultTransitDates: defaultTransitDates,
        defaultTransitDatesVersion: '1.0' // Version pour futures migrations
      });

      console.log('Dates de transit par défaut sauvegardées:', defaultTransitDates);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des transits par défaut:', error);
    }
  }

  async resetDefaultTransits() {
    try {
      // Supprimer les données sauvegardées
      await chrome.storage.local.remove(['defaultTransitDates', 'defaultTransitDatesVersion']);

      // Réinitialiser les transits par défaut
      this.multiTransits = [];
      window.multiTransits = [];

      // Recréer les transits par défaut
      await this.addDefaultTransits();

      // Mettre à jour l'affichage
      this.updateMultiTransitList();
      this.draw();

      this.showUpdateMessage('Transits par défaut réinitialisés');
      console.log('Transits par défaut réinitialisés');
    } catch (error) {
      console.error('Erreur lors de la réinitialisation des transits par défaut:', error);
    }
  }

  showUpdateMessage(message) {
    // Créer un message temporaire de confirmation
    const messageDiv = document.createElement('div');
    messageDiv.className = 'transit-update-message';
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #4CAF50;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      z-index: 10000;
      font-size: 14px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;

    document.body.appendChild(messageDiv);

    // Supprimer le message après 3 secondes
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.parentNode.removeChild(messageDiv);
      }
    }, 3000);
  }

  clearAllMultiTransits() {
    this.multiTransits = [];
    this.updateMultiTransitList();
    this.draw();
  }

  setupSectorMarkingButton() {
    // Créer le bouton de marquage de secteurs s'il n'existe pas
    let markingBtn = document.getElementById('sector-marking-btn');
    if (!markingBtn) {
      markingBtn = document.createElement('button');
      markingBtn.id = 'sector-marking-btn';
      markingBtn.innerHTML = '🎯 Marquer Secteurs';
      markingBtn.className = 'linear-control-btn';
      markingBtn.title = 'Configurer la surveillance des secteurs';

      // Ajouter le bouton après le bouton de mode circulaire
      const circularBtn = document.getElementById('linear-circular-mode-btn');
      if (circularBtn && circularBtn.parentNode) {
        circularBtn.parentNode.insertBefore(markingBtn, circularBtn.nextSibling);
      }
    }

    // Event listener pour le bouton
    markingBtn.addEventListener('click', () => {
      if (!this.isCircularMode && !this.is360Mode) { // Seulement en mode 30° linéaire
        this.showSectorManagementDialog();
      } else {
        alert('Le marquage de secteurs n\'est disponible qu\'en mode 30° linéaire.');
      }
    });
  }

  showSectorManagementDialog() {
    // Créer le dialog de gestion des secteurs
    const dialog = document.createElement('div');
    dialog.className = 'sector-management-dialog';

    // Ajouter les styles CSS inline
    const style = document.createElement('style');
    style.textContent = `
      .sector-management-dialog .dialog-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      }
      .sector-management-dialog .dialog-content {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        max-width: 800px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
      }
      .sector-management-dialog h3 {
        margin: 0 0 20px 0;
        color: #333;
        text-align: center;
      }
      .sector-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        margin-bottom: 20px;
      }
      .sector-card {
        border: 2px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        background: white;
      }
      .sector-card:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0,123,255,0.2);
      }
      .sector-card.marked {
        border-color: var(--sector-color);
        background-color: var(--sector-color-light);
      }
      .sector-card h4 {
        margin: 0 0 10px 0;
        color: #333;
      }
      .sector-card .sector-range {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
      }
      .sector-card .sector-status {
        font-size: 11px;
        font-style: italic;
      }
      .sector-card .sector-planets {
        font-size: 10px;
        margin-top: 5px;
        color: #555;
      }
      .dialog-buttons {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
      }
      .dialog-buttons button {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
      }
      .close-dialog-btn {
        background-color: #6c757d;
        color: white;
      }
      .close-dialog-btn:hover {
        background-color: #5a6268;
      }
      .clear-all-btn {
        background-color: #dc3545;
        color: white;
      }
      .clear-all-btn:hover {
        background-color: #c82333;
      }
    `;
    document.head.appendChild(style);

    // Générer le contenu du dialog
    const sectorsHtml = this.generateSectorsGrid();

    dialog.innerHTML = `
      <div class="dialog-overlay">
        <div class="dialog-content">
          <h3>🎯 Gestion des Secteurs Marqués</h3>
          <p style="text-align: center; color: #666; margin-bottom: 20px;">
            Cliquez sur un secteur pour le configurer ou le modifier
          </p>
          <div class="sector-grid">
            ${sectorsHtml}
          </div>
          <div class="dialog-buttons">
            <button class="close-dialog-btn">Fermer</button>
            <button class="clear-all-btn">Effacer Tout</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);

    // Event listeners
    this.setupSectorManagementListeners(dialog);
  }

  generateSectorsGrid() {
    const sectors = [
      { number: 1, start: 0, end: 2.5 },
      { number: 2, start: 2.5, end: 5 },
      { number: 3, start: 5, end: 7.5 },
      { number: 4, start: 7.5, end: 10 },
      { number: 5, start: 10, end: 12.5 },
      { number: 6, start: 12.5, end: 15 },
      { number: 7, start: 15, end: 17.5 },
      { number: 8, start: 17.5, end: 20 },
      { number: 9, start: 20, end: 22.5 },
      { number: 10, start: 22.5, end: 25 },
      { number: 11, start: 25, end: 27.5 },
      { number: 12, start: 27.5, end: 30 }
    ];

    return sectors.map(sector => {
      const marking = this.markedSectors.get(sector.number);
      const isMarked = marking !== undefined;

      let statusText = 'Non marqué';
      let planetsText = '';
      let cardStyle = '';

      if (isMarked) {
        statusText = `Surveillé (${marking.planets.size} planètes)`;
        planetsText = Array.from(marking.planets).map(p => this.getPlanetSymbol(p)).join(' ');
        cardStyle = `style="--sector-color: ${marking.color}; --sector-color-light: ${marking.color}20;"`;
      }

      return `
        <div class="sector-card ${isMarked ? 'marked' : ''}"
             data-sector="${sector.number}"
             ${cardStyle}>
          <h4>Secteur ${sector.number}</h4>
          <div class="sector-range">${sector.start}° - ${sector.end}°</div>
          <div class="sector-status">${statusText}</div>
          ${planetsText ? `<div class="sector-planets">${planetsText}</div>` : ''}
        </div>
      `;
    }).join('');
  }

  setupSectorManagementListeners(dialog) {
    // Clic sur les cartes de secteurs
    const sectorCards = dialog.querySelectorAll('.sector-card');
    sectorCards.forEach(card => {
      card.addEventListener('click', () => {
        const sectorNumber = parseInt(card.getAttribute('data-sector'));
        document.body.removeChild(dialog);
        this.showSectorMarkingDialog(sectorNumber);
      });
    });

    // Bouton fermer
    dialog.querySelector('.close-dialog-btn').addEventListener('click', () => {
      document.body.removeChild(dialog);
    });

    // Bouton effacer tout
    dialog.querySelector('.clear-all-btn').addEventListener('click', () => {
      if (confirm('Êtes-vous sûr de vouloir effacer tous les marquages de secteurs ?')) {
        this.clearAllSectorMarkings();
        document.body.removeChild(dialog);
      }
    });

    // Fermer en cliquant sur l'overlay
    dialog.querySelector('.dialog-overlay').addEventListener('click', (e) => {
      if (e.target === dialog.querySelector('.dialog-overlay')) {
        document.body.removeChild(dialog);
      }
    });
  }

  clearAllSectorMarkings() {
    this.markedSectors.clear();
    this.saveMarkedSectorsToStorage();
    this.draw();
    this.checkSectorAlerts();
  }

  setupTimeboxSynchronization() {
    // Observer les changements dans les champs de date/heure de transit
    const transitDateInput = document.getElementById('transit-date');
    const transitHourInput = document.getElementById('transit-hour');
    const transitMinuteInput = document.getElementById('transit-minute');
    const transitSecondInput = document.getElementById('transit-second');

    // Fonction pour mettre à jour les positions de transit
    const updateTransitPositions = async () => {
      if (!this.isVisible) return; // Ne pas mettre à jour si le zodiaque n'est pas visible

      try {
        // Récupérer la nouvelle date de transit
        const date = transitDateInput?.value;
        const hour = parseInt(transitHourInput?.value || '12');
        const minute = parseInt(transitMinuteInput?.value || '0');
        const second = parseInt(transitSecondInput?.value || '0');

        if (!date) return;

        // Créer la nouvelle date de transit
        const transitDate = new Date(date);
        transitDate.setHours(hour, minute, second, 0);

        // Calculer les nouvelles positions planétaires
        const newTransitPositions = await this.calculateTransitPositions(transitDate);

        // Mettre à jour les positions globales
        window.transitPositions = newTransitPositions;
        this.currentTransitPositions = newTransitPositions;

        // Redessiner le zodiaque avec les nouvelles positions
        this.draw();

        // Vérifier les alertes de secteurs
        this.checkSectorAlerts();

        // Afficher un message de confirmation discret
        this.showTransitUpdateMessage(transitDate);

      } catch (error) {
        console.error('Erreur lors de la mise à jour des positions de transit:', error);
      }
    };

    // Ajouter les listeners sur les champs de date/heure
    if (transitDateInput) {
      transitDateInput.addEventListener('change', updateTransitPositions);
    }

    if (transitHourInput) {
      transitHourInput.addEventListener('change', updateTransitPositions);
    }

    if (transitMinuteInput) {
      transitMinuteInput.addEventListener('change', updateTransitPositions);
    }

    if (transitSecondInput) {
      transitSecondInput.addEventListener('change', updateTransitPositions);
    }

    // Observer les clics sur le mini-calendrier
    this.observeMiniCalendarClicks();

    // Observer les changements dans les boutons de navigation temporelle
    this.observeTemporalNavigation();
  }

  observeMiniCalendarClicks() {
    // Observer les mutations du DOM pour détecter les nouveaux éléments de calendrier
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Chercher les éléments de jour du calendrier
            const dayElements = node.querySelectorAll ?
              node.querySelectorAll('.mini-calendar-day') :
              (node.classList?.contains('mini-calendar-day') ? [node] : []);

            dayElements.forEach((dayElement) => {
              // Ajouter un listener pour la synchronisation avec le zodiaque
              dayElement.addEventListener('click', () => {
                // Délai pour laisser le temps aux autres handlers de s'exécuter
                setTimeout(() => {
                  this.syncWithCurrentTransitDate();
                }, 200);
              });
            });
          }
        });
      });
    });

    // Observer le conteneur du mini-calendrier
    const calendarContainer = document.getElementById('mini-calendar-grid');
    if (calendarContainer) {
      observer.observe(calendarContainer, { childList: true, subtree: true });
    }

    // Observer aussi le document entier pour les calendriers créés dynamiquement
    observer.observe(document.body, { childList: true, subtree: true });
  }

  observeTemporalNavigation() {
    // Observer les boutons de navigation temporelle (jours, heures, etc.)
    const temporalButtons = document.querySelectorAll('[data-unit][data-value]');

    temporalButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Délai pour laisser le temps aux champs de se mettre à jour
        setTimeout(() => {
          this.syncWithCurrentTransitDate();
        }, 100);
      });
    });

    // Observer aussi le bouton "Now" s'il existe
    const nowButton = document.querySelector('.now-btn, #now-btn, [data-action="now"]');
    if (nowButton) {
      nowButton.addEventListener('click', () => {
        setTimeout(() => {
          this.syncWithCurrentTransitDate();
        }, 100);
      });
    }

    // Observer le bouton "Calculate Chart"
    const calculateButton = document.getElementById('calculate-btn');
    if (calculateButton) {
      calculateButton.addEventListener('click', () => {
        setTimeout(() => {
          this.syncWithCurrentTransitDate();
        }, 300); // Délai plus long pour laisser le temps au calcul
      });
    }
  }

  async syncWithCurrentTransitDate() {
    if (!this.isVisible) return;

    try {
      // Récupérer les valeurs actuelles des champs
      const transitDateInput = document.getElementById('transit-date');
      const transitHourInput = document.getElementById('transit-hour');
      const transitMinuteInput = document.getElementById('transit-minute');
      const transitSecondInput = document.getElementById('transit-second');

      const date = transitDateInput?.value;
      const hour = parseInt(transitHourInput?.value || '12');
      const minute = parseInt(transitMinuteInput?.value || '0');
      const second = parseInt(transitSecondInput?.value || '0');

      if (!date) return;

      // Créer la date de transit
      const transitDate = new Date(date);
      transitDate.setHours(hour, minute, second, 0);

      // Calculer les nouvelles positions
      const newTransitPositions = await this.calculateTransitPositions(transitDate);

      // Mettre à jour les positions globales
      window.transitPositions = newTransitPositions;
      this.currentTransitPositions = newTransitPositions;

      // Redessiner
      this.draw();

      // Vérifier les alertes
      this.checkSectorAlerts();

    } catch (error) {
      console.error('Erreur lors de la synchronisation avec la date de transit:', error);
    }
  }

  showTransitUpdateMessage(transitDate) {
    // Supprimer les messages existants
    const existingMessages = document.querySelectorAll('.transit-update-message');
    existingMessages.forEach(msg => msg.remove());

    // Créer un nouveau message
    const message = document.createElement('div');
    message.className = 'transit-update-message';
    message.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #007bff;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 9999;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.3s ease;
    `;

    const dateStr = transitDate.toLocaleDateString('fr-FR');
    const timeStr = transitDate.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    message.innerHTML = `
      <span style="margin-right: 8px;">🔄</span>
      Astres mis à jour: ${dateStr} ${timeStr}
    `;

    document.body.appendChild(message);

    // Animation d'apparition
    setTimeout(() => {
      message.style.opacity = '1';
    }, 10);

    // Suppression automatique après 2 secondes
    setTimeout(() => {
      message.style.opacity = '0';
      setTimeout(() => {
        if (message.parentNode) {
          message.parentNode.removeChild(message);
        }
      }, 300);
    }, 2000);
  }

  showSectorMarkingDialog(sectorNumber) {
    const existingMarking = this.markedSectors.get(sectorNumber);

    // Créer le dialog
    const dialog = document.createElement('div');
    dialog.className = 'sector-marking-dialog';

    // Ajouter les styles CSS inline
    const style = document.createElement('style');
    style.textContent = `
      .sector-marking-dialog .dialog-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      }
      .sector-marking-dialog .dialog-content {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
      }
      .sector-marking-dialog h3 {
        margin: 0 0 20px 0;
        color: #333;
        text-align: center;
      }
      .sector-marking-dialog .dialog-field {
        margin-bottom: 15px;
      }
      .sector-marking-dialog label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
      }
      .sector-marking-dialog .planet-checkboxes {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        margin-top: 8px;
      }
      .sector-marking-dialog .planet-checkboxes label {
        display: flex;
        align-items: center;
        font-weight: normal;
        margin-bottom: 0;
        cursor: pointer;
      }
      .sector-marking-dialog .planet-checkboxes input[type="checkbox"] {
        margin-right: 8px;
      }
      .sector-marking-dialog textarea {
        width: 100%;
        height: 80px;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        resize: vertical;
        font-family: Arial, sans-serif;
        box-sizing: border-box;
      }
      .sector-marking-dialog select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
      }
      .sector-marking-dialog .dialog-buttons {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
      }
      .sector-marking-dialog button {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
      }
      .sector-marking-dialog #save-sector-marking {
        background-color: #4CAF50;
        color: white;
      }
      .sector-marking-dialog #save-sector-marking:hover {
        background-color: #45a049;
      }
      .sector-marking-dialog #back-to-management {
        background-color: #007bff;
        color: white;
      }
      .sector-marking-dialog #back-to-management:hover {
        background-color: #0056b3;
      }
      .sector-marking-dialog #cancel-sector-marking {
        background-color: #6c757d;
        color: white;
      }
      .sector-marking-dialog #cancel-sector-marking:hover {
        background-color: #5a6268;
      }
      .sector-marking-dialog #delete-sector-marking {
        background-color: #dc3545;
        color: white;
      }
      .sector-marking-dialog #delete-sector-marking:hover {
        background-color: #c82333;
      }
    `;
    document.head.appendChild(style);

    dialog.innerHTML = `
      <div class="dialog-overlay">
        <div class="dialog-content">
          <h3>Marquer le Secteur ${sectorNumber}</h3>
          <div class="dialog-field">
            <label>Planètes à surveiller :</label>
            <div class="planet-checkboxes">
              <label><input type="checkbox" value="sun" ${existingMarking?.planets.has('sun') ? 'checked' : ''}> ☉ Soleil</label>
              <label><input type="checkbox" value="moon" ${existingMarking?.planets.has('moon') ? 'checked' : ''}> ☽ Lune</label>
              <label><input type="checkbox" value="mercury" ${existingMarking?.planets.has('mercury') ? 'checked' : ''}> ☿ Mercure</label>
              <label><input type="checkbox" value="venus" ${existingMarking?.planets.has('venus') ? 'checked' : ''}> ♀ Vénus</label>
              <label><input type="checkbox" value="mars" ${existingMarking?.planets.has('mars') ? 'checked' : ''}> ♂ Mars</label>
              <label><input type="checkbox" value="jupiter" ${existingMarking?.planets.has('jupiter') ? 'checked' : ''}> ♃ Jupiter</label>
              <label><input type="checkbox" value="saturn" ${existingMarking?.planets.has('saturn') ? 'checked' : ''}> ♄ Saturne</label>
              <label><input type="checkbox" value="uranus" ${existingMarking?.planets.has('uranus') ? 'checked' : ''}> ♅ Uranus</label>
              <label><input type="checkbox" value="neptune" ${existingMarking?.planets.has('neptune') ? 'checked' : ''}> ♆ Neptune</label>
              <label><input type="checkbox" value="pluto" ${existingMarking?.planets.has('pluto') ? 'checked' : ''}> ♇ Pluton</label>
            </div>
          </div>
          <div class="dialog-field">
            <label>Message d'alerte :</label>
            <textarea id="sector-message" placeholder="Message personnalisé quand les planètes sélectionnées sont dans ce secteur...">${existingMarking?.message || ''}</textarea>
          </div>
          <div class="dialog-field">
            <label>Couleur de marquage :</label>
            <select id="sector-color">
              <option value="#FFD700" ${existingMarking?.color === '#FFD700' ? 'selected' : ''}>🟡 Or</option>
              <option value="#FF6B6B" ${existingMarking?.color === '#FF6B6B' ? 'selected' : ''}>🔴 Rouge</option>
              <option value="#4ECDC4" ${existingMarking?.color === '#4ECDC4' ? 'selected' : ''}>🟢 Vert</option>
              <option value="#45B7D1" ${existingMarking?.color === '#45B7D1' ? 'selected' : ''}>🔵 Bleu</option>
              <option value="#96CEB4" ${existingMarking?.color === '#96CEB4' ? 'selected' : ''}>🟢 Vert clair</option>
              <option value="#FFEAA7" ${existingMarking?.color === '#FFEAA7' ? 'selected' : ''}>🟡 Jaune</option>
              <option value="#DDA0DD" ${existingMarking?.color === '#DDA0DD' ? 'selected' : ''}>🟣 Violet</option>
              <option value="#F0A500" ${existingMarking?.color === '#F0A500' ? 'selected' : ''}>🟠 Orange</option>
            </select>
          </div>
          <div class="dialog-buttons">
            <button id="save-sector-marking">Sauvegarder</button>
            <button id="back-to-management">Retour</button>
            <button id="cancel-sector-marking">Annuler</button>
            ${existingMarking ? '<button id="delete-sector-marking">Supprimer</button>' : ''}
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);

    // Event listeners pour les boutons
    dialog.querySelector('#save-sector-marking').addEventListener('click', () => {
      this.saveSectorMarking(sectorNumber, dialog);
    });

    dialog.querySelector('#back-to-management').addEventListener('click', () => {
      document.body.removeChild(dialog);
      // Retourner au dialog de gestion
      setTimeout(() => {
        this.showSectorManagementDialog();
      }, 100);
    });

    dialog.querySelector('#cancel-sector-marking').addEventListener('click', () => {
      document.body.removeChild(dialog);
    });

    if (existingMarking) {
      dialog.querySelector('#delete-sector-marking').addEventListener('click', () => {
        this.removeSectorMarking(sectorNumber);
        document.body.removeChild(dialog);
        // Retourner au dialog de gestion
        setTimeout(() => {
          this.showSectorManagementDialog();
        }, 100);
      });
    }

    // Fermer en cliquant sur l'overlay
    dialog.querySelector('.dialog-overlay').addEventListener('click', (e) => {
      if (e.target === dialog.querySelector('.dialog-overlay')) {
        document.body.removeChild(dialog);
      }
    });
  }

  saveSectorMarking(sectorNumber, dialog) {
    const selectedPlanets = new Set();
    const checkboxes = dialog.querySelectorAll('.planet-checkboxes input[type="checkbox"]:checked');
    checkboxes.forEach(cb => selectedPlanets.add(cb.value));

    const message = dialog.querySelector('#sector-message').value.trim();
    const color = dialog.querySelector('#sector-color').value;

    if (selectedPlanets.size === 0) {
      alert('Veuillez sélectionner au moins une planète à surveiller.');
      return;
    }

    if (!message) {
      alert('Veuillez saisir un message d\'alerte.');
      return;
    }

    // Sauvegarder le marquage
    this.markedSectors.set(sectorNumber, {
      planets: selectedPlanets,
      message: message,
      color: color
    });

    // Sauvegarder dans le storage
    this.saveMarkedSectorsToStorage();

    // Redessiner pour afficher le marquage
    this.draw();

    // Vérifier les alertes
    this.checkSectorAlerts();

    // Fermer le dialog et retourner au dialog de gestion
    document.body.removeChild(dialog);

    // Rouvrir le dialog de gestion pour voir les changements
    setTimeout(() => {
      this.showSectorManagementDialog();
    }, 100);
  }

  removeSectorMarking(sectorNumber) {
    this.markedSectors.delete(sectorNumber);
    this.saveMarkedSectorsToStorage();
    this.draw();
    this.checkSectorAlerts();
  }

  async saveMarkedSectorsToStorage() {
    try {
      const sectorsData = {};
      this.markedSectors.forEach((data, sectorNumber) => {
        sectorsData[sectorNumber] = {
          planets: Array.from(data.planets),
          message: data.message,
          color: data.color
        };
      });

      await chrome.storage.local.set({ markedSectors: sectorsData });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des secteurs marqués:', error);
    }
  }

  async loadMarkedSectorsFromStorage() {
    try {
      const result = await chrome.storage.local.get(['markedSectors']);
      if (result.markedSectors) {
        this.markedSectors.clear();
        Object.entries(result.markedSectors).forEach(([sectorNumber, data]) => {
          this.markedSectors.set(parseInt(sectorNumber), {
            planets: new Set(data.planets),
            message: data.message,
            color: data.color
          });
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement des secteurs marqués:', error);
    }
  }

  checkSectorAlerts() {
    if (!this.currentTransitPositions && !this.multiTransits) return;

    this.sectorAlerts = []; // Réinitialiser les alertes

    // Vérifier les planètes du transit actuel
    if (this.currentTransitPositions) {
      this.checkPlanetsInMarkedSectors(this.currentTransitPositions, 'Transit actuel');
    }

    // Vérifier les planètes des multi-transits
    this.multiTransits.forEach((transit, index) => {
      if (transit.positions) {
        const date = new Date(transit.date);
        const transitLabel = `Transit ${index + 1} (${date.toLocaleDateString('fr-FR')})`;
        this.checkPlanetsInMarkedSectors(transit.positions, transitLabel);
      }
    });

    // Afficher les alertes s'il y en a
    if (this.sectorAlerts.length > 0) {
      this.showSectorAlerts();
    }
  }

  checkPlanetsInMarkedSectors(positions, transitLabel) {
    this.markedSectors.forEach((marking, sectorNumber) => {
      const planetsInSector = [];

      // Vérifier chaque planète surveillée
      marking.planets.forEach(planetKey => {
        if (positions[planetKey]) {
          const planetPosition = this.getProportionalPosition(positions[planetKey]);
          const sectorStart = (sectorNumber - 1) * 2.5;
          const sectorEnd = sectorNumber * 2.5;

          // Vérifier si la planète est dans ce secteur
          if (planetPosition >= sectorStart && planetPosition < sectorEnd) {
            planetsInSector.push({
              key: planetKey,
              symbol: this.getPlanetSymbol(planetKey),
              position: planetPosition.toFixed(1)
            });
          }
        }
      });

      // Si des planètes surveillées sont dans le secteur, créer une alerte
      if (planetsInSector.length > 0) {
        this.sectorAlerts.push({
          sectorNumber: sectorNumber,
          transitLabel: transitLabel,
          planets: planetsInSector,
          message: marking.message,
          color: marking.color
        });
      }
    });
  }

  getPlanetSymbol(planetKey) {
    const symbols = {
      'sun': '☉', 'moon': '☽', 'mercury': '☿', 'venus': '♀', 'mars': '♂',
      'jupiter': '♃', 'saturn': '♄', 'uranus': '♅', 'neptune': '♆',
      'pluto': '♇', 'north_node': '☊', 'south_node': '☋'
    };
    return symbols[planetKey] || planetKey;
  }

  showSectorAlerts() {
    // Supprimer les alertes existantes
    const existingAlerts = document.querySelectorAll('.sector-alert-notification');
    existingAlerts.forEach(alert => alert.remove());

    // Créer une notification pour chaque alerte
    this.sectorAlerts.forEach((alert, index) => {
      const notification = document.createElement('div');
      notification.className = 'sector-alert-notification';
      notification.style.cssText = `
        position: fixed;
        top: ${20 + (index * 120)}px;
        right: 20px;
        background-color: ${alert.color};
        color: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 300px;
        font-size: 13px;
        border: 2px solid rgba(255,255,255,0.3);
      `;

      const planetsText = alert.planets.map(p => `${p.symbol} (${p.position}°)`).join(', ');

      notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px;">
          🚨 Alerte Secteur ${alert.sectorNumber}
        </div>
        <div style="margin-bottom: 8px;">
          <strong>${alert.transitLabel}</strong>
        </div>
        <div style="margin-bottom: 8px;">
          Planètes: ${planetsText}
        </div>
        <div style="font-style: italic; margin-bottom: 10px;">
          ${alert.message}
        </div>
        <button onclick="this.parentElement.remove()" style="
          background: rgba(255,255,255,0.2);
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 11px;
        ">Fermer</button>
      `;

      document.body.appendChild(notification);

      // Auto-suppression après 10 secondes
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 10000);
    });
  }

  async show() {
    if (!this.section) return;

    // Charger les secteurs marqués depuis le storage
    await this.loadMarkedSectorsFromStorage();

    // Récupérer les données actuelles
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;
    this.multiTransits = window.multiTransits || [];

    // Charger les transits sauvegardés ou ajouter les transits par défaut
    if (this.multiTransits.length === 0) {
      await this.loadOrCreateDefaultTransits();
    }

    // Debug : vérifier les données (supprimé pour éviter les problèmes d'affichage)
    // console.log('Linear Zodiac - Birth positions:', this.currentBirthPositions);
    // console.log('Linear Zodiac - Transit positions:', this.currentTransitPositions);

    // Mettre à jour les informations de date
    this.updateDateTimeInfo();

    // Mettre à jour la liste des multi-transits
    this.updateMultiTransitList();

    // Mettre à jour l'interface du mode
    this.updateModeInterface();

    // Afficher la section
    this.section.style.display = 'block';
    this.isVisible = true;

    // Dessiner le zodiaque linéaire
    setTimeout(() => {
      this.draw();
      // Vérifier les alertes après le dessin
      this.checkSectorAlerts();
    }, 100);
  }

  async loadOrCreateDefaultTransits() {
    try {
      // Vérifier s'il y a des transits sauvegardés
      const result = await chrome.storage.local.get(['defaultTransitDates']);

      if (result.defaultTransitDates && result.defaultTransitDates.length >= 2) {
        // Charger les transits sauvegardés
        console.log('Chargement des transits sauvegardés depuis le storage');
        await this.addDefaultTransits();
      } else {
        // Créer les transits par défaut
        console.log('Création des transits par défaut');
        await this.addDefaultTransits();
      }
    } catch (error) {
      console.error('Erreur lors du chargement des transits:', error);
      // Fallback vers la création des transits par défaut
      await this.addDefaultTransits();
    }
  }

  async addDefaultTransits() {
    try {
      // Charger les dates sauvegardées depuis chrome.storage.local
      const result = await chrome.storage.local.get(['defaultTransitDates']);

      let date1, date2;

      if (result.defaultTransitDates && result.defaultTransitDates.length >= 2) {
        // Utiliser les dates sauvegardées
        console.log('Chargement des dates de transit sauvegardées:', result.defaultTransitDates);
        date1 = new Date(result.defaultTransitDates[0].date);
        date2 = new Date(result.defaultTransitDates[1].date);
      } else {
        // Utiliser les dates par défaut si aucune sauvegarde
        console.log('Utilisation des dates de transit par défaut');
        const today = new Date();

        // Date 1 : 1 an en arrière
        date1 = new Date(today);
        date1.setFullYear(today.getFullYear() - 1);
        date1.setHours(12, 0, 0, 0);

        // Date 2 : 2 ans en arrière
        date2 = new Date(today);
        date2.setFullYear(today.getFullYear() - 2);
        date2.setHours(12, 0, 0, 0);
      }

      // Calculer les positions planétaires
      const positions1 = await this.calculateTransitPositions(date1);
      const positions2 = await this.calculateTransitPositions(date2);

      this.multiTransits = [
        {
          date: date1.toISOString(),
          positions: positions1,
          id: Date.now(),
          isDefault: true  // Marquer comme transit par défaut
        },
        {
          date: date2.toISOString(),
          positions: positions2,
          id: Date.now() + 1,
          isDefault: true  // Marquer comme transit par défaut
        }
      ];

      // Sauvegarder dans window pour persistance
      window.multiTransits = this.multiTransits;

      // Si c'est la première fois, sauvegarder les dates par défaut
      if (!result.defaultTransitDates) {
        await this.saveDefaultTransitsToStorage();
      }

    } catch (error) {
      console.error('Erreur lors du chargement des transits par défaut:', error);

      // Fallback vers les dates par défaut en cas d'erreur
      const today = new Date();

      // Date 1 : 1 an en arrière
      const date1 = new Date(today);
      date1.setFullYear(today.getFullYear() - 1);
      date1.setHours(12, 0, 0, 0);

      // Date 2 : 2 ans en arrière
      const date2 = new Date(today);
      date2.setFullYear(today.getFullYear() - 2);
      date2.setHours(12, 0, 0, 0);
      const positions1 = await this.calculateTransitPositions(date1);
      const positions2 = await this.calculateTransitPositions(date2);

      this.multiTransits = [
        {
          date: date1.toISOString(),
          positions: positions1,
          id: Date.now(),
          isDefault: true
        },
        {
          date: date2.toISOString(),
          positions: positions2,
          id: Date.now() + 1,
          isDefault: true
        }
      ];

      window.multiTransits = this.multiTransits;
    }
  }

  hide() {
    if (!this.section) return;
    this.section.style.display = 'none';
    this.isVisible = false;
  }

  updateDateTimeInfo() {
    const birthInfo = document.getElementById('linear-birth-info');
    const transitInfo = document.getElementById('linear-transit-info');

    if (birthInfo) {
      const birthDate = document.getElementById('birth-date')?.value || '17/12/1991';
      const birthHour = document.getElementById('birth-hour')?.value || '7';
      const birthMinute = document.getElementById('birth-minute')?.value || '27';
      birthInfo.textContent = `Naissance: ${birthDate} ${birthHour}:${birthMinute.padStart(2, '0')}`;
    }

    if (transitInfo) {
      const transitDate = document.getElementById('transit-date')?.value || new Date().toISOString().split('T')[0];
      const transitHour = document.getElementById('transit-hour')?.value || '12';
      const transitMinute = document.getElementById('transit-minute')?.value || '0';
      transitInfo.textContent = `Transit: ${transitDate} ${transitHour}:${transitMinute.padStart(2, '0')}`;
    }
  }

  refresh() {
    // Récupérer les nouvelles données
    this.currentBirthPositions = window.birthPositions;
    this.currentTransitPositions = window.transitPositions;
    this.multiTransits = window.multiTransits || [];

    // Mettre à jour les informations
    this.updateDateTimeInfo();

    // Redessiner
    this.draw();

    // Vérifier les alertes
    this.checkSectorAlerts();
  }

  toggle360Mode() {
    this.is360Mode = !this.is360Mode;

    // Sauvegarder le mode dans localStorage pour persistance
    try {
      localStorage.setItem('linearZodiac360Mode', this.is360Mode.toString());
    } catch (error) {
      console.warn('Impossible de sauvegarder le mode 360° dans localStorage:', error);
    }

    // Mettre à jour l'interface
    this.updateModeInterface();

    // Redessiner le zodiaque avec le nouveau mode
    this.draw();
  }

  updateModeInterface() {
    const modeBtn = document.getElementById('linear-360-mode-btn');
    const circularBtn = document.getElementById('linear-circular-mode-btn');
    const title = document.getElementById('linear-zodiac-title');

    if (modeBtn && title) {
      if (this.is360Mode) {
        // Mode 360° activé
        modeBtn.textContent = '🌟 Mode 30°';
        modeBtn.classList.add('active');
        modeBtn.title = 'Basculer en mode 30°';
        title.textContent = this.isCircularMode ?
          '🌍 Zodiaque Circulaire 360° - Multi Transit' :
          '🌍 Zodiaque Linéaire 360° - Multi Transit';
      } else {
        // Mode 30° activé
        modeBtn.textContent = '🌍 Mode 360°';
        modeBtn.classList.remove('active');
        modeBtn.title = 'Basculer en mode 360°';
        title.textContent = this.isCircularMode ?
          '🌟 Zodiaque Circulaire 30° - Multi Transit' :
          '🌟 Zodiaque Linéaire 30° - Multi Transit';
      }
    }

    if (circularBtn) {
      if (this.isCircularMode) {
        // Mode circulaire activé
        circularBtn.textContent = '📏 Mode Linéaire';
        circularBtn.classList.add('active');
        circularBtn.title = 'Basculer en mode linéaire';
      } else {
        // Mode linéaire activé
        circularBtn.textContent = '🔄 Mode Circulaire';
        circularBtn.classList.remove('active');
        circularBtn.title = 'Basculer en mode circulaire';
      }
    }
  }

  toggleCircularMode() {
    this.isCircularMode = !this.isCircularMode;

    // Sauvegarder le mode dans localStorage pour persistance
    try {
      localStorage.setItem('linearZodiacCircularMode', this.isCircularMode.toString());
    } catch (error) {
      console.warn('Impossible de sauvegarder le mode circulaire dans localStorage:', error);
    }

    // Mettre à jour l'interface
    this.updateModeInterface();

    // Redessiner selon le mode
    this.draw();
  }

  download() {
    if (!this.canvas) return;

    // Créer un lien de téléchargement
    const link = document.createElement('a');
    link.download = `zodiaque-lineaire-30deg-${new Date().toISOString().split('T')[0]}.png`;
    link.href = this.canvas.toDataURL();
    link.click();
  }

  draw() {
    if (!this.ctx || !this.currentBirthPositions || !this.currentTransitPositions) return;

    // Effacer le canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Dessiner selon le mode sélectionné
    if (this.isCircularMode) {
      this.drawCircularZodiac();
    } else {
      this.drawLinearZodiac();
    }
  }

  drawLinearZodiac() {
    const width = this.canvas.width;
    const height = this.canvas.height;
    const margin = 50;
    const zodiacWidth = width - (margin * 2);
    const zodiacHeight = 120;

    // Ajuster la position Y selon le nombre de multi-transits - plus d'espace pour les niveaux multiples
    const multiTransitCount = this.multiTransits ? this.multiTransits.length : 0;
    const multiTransitSpace = multiTransitCount * 80 + (multiTransitCount > 0 ? 80 : 0); // Plus d'espace pour les niveaux
    const sectorNamesSpace = 35; // Espace pour les noms des secteurs en dessous
    const planetLevelsSpace = 100; // Plus d'espace pour éviter que les noms cachent les marqueurs
    const totalHeight = zodiacHeight + multiTransitSpace + sectorNamesSpace + planetLevelsSpace + 100;
    const startY = Math.max(multiTransitSpace + 80, (height - totalHeight) / 2 + multiTransitSpace + 60);

    // Dessiner le fond du zodiaque
    this.drawZodiacBackground(margin, startY, zodiacWidth, zodiacHeight);

    // Dessiner les divisions des secteurs selon le mode
    if (this.is360Mode) {
      this.drawZodiacSectors360(margin, startY, zodiacWidth, zodiacHeight);
    } else {
      this.drawZodiacSectors(margin, startY, zodiacWidth, zodiacHeight);
    }

    // Dessiner les planètes natales (ligne du bas) - ajusté pour les marqueurs plus gros
    this.drawPlanets(this.currentBirthPositions, margin, startY + zodiacHeight - 50, zodiacWidth, false);

    // Dessiner les planètes de transit (ligne du haut) - ajusté pour les marqueurs plus gros
    this.drawPlanets(this.currentTransitPositions, margin, startY - 10, zodiacWidth, true);

    // Dessiner les multi-transits si présents
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.drawMultiTransits(margin, startY, zodiacWidth, zodiacHeight);
    }

    // Dessiner les noms des secteurs en dessous de la réglette avec plus d'espace
    if (this.is360Mode) {
      this.drawSignNames360(margin, startY + zodiacHeight + 50, zodiacWidth);
    } else {
      this.drawSectorNames(margin, startY + zodiacHeight + 50, zodiacWidth);
    }

    // NE PLUS dessiner les aspects - supprimé selon demande utilisateur
    // this.drawAspects(margin, startY, zodiacWidth, zodiacHeight);
  }

  drawCircularZodiac() {
    const width = this.canvas.width;
    const height = this.canvas.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.42; // Augmenté de 0.35 à 0.42 pour un cercle plus grand

    // Utiliser les fonctions du zodiaque circulaire existant
    if (this.is360Mode) {
      // Mode 360° circulaire (zodiaque complet)
      this.drawCircular360Zodiac(centerX, centerY, radius);
    } else {
      // Mode 30° circulaire (secteurs proportionnels)
      this.drawCircular30Zodiac(centerX, centerY, radius);
    }
  }

  drawCircular360Zodiac(centerX, centerY, radius) {
    // Dessiner le cercle de base
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    this.ctx.strokeStyle = '#333';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Dessiner les 12 signes zodiacaux
    const signs = [
      'Bélier', 'Taureau', 'Gémeaux', 'Cancer', 'Lion', 'Vierge',
      'Balance', 'Scorpion', 'Sagittaire', 'Capricorne', 'Verseau', 'Poissons'
    ];

    for (let i = 0; i < 12; i++) {
      const angle = (i * 30 - 90) * Math.PI / 180; // -90 pour commencer à 12h
      const nextAngle = ((i + 1) * 30 - 90) * Math.PI / 180;

      // Ligne de séparation
      this.ctx.beginPath();
      this.ctx.moveTo(centerX, centerY);
      this.ctx.lineTo(
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle)
      );
      this.ctx.strokeStyle = '#ddd';
      this.ctx.lineWidth = 1;
      this.ctx.stroke();

      // Nom du signe
      const labelAngle = angle + (30 * Math.PI / 180) / 2;
      const labelRadius = radius * 0.85;
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      this.ctx.fillStyle = '#666';
      this.ctx.font = 'bold 14px Arial'; // Augmenté de 12px à 14px et ajouté bold
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(signs[i], labelX, labelY);
    }

    // Dessiner d'abord tous les cercles en pointillés (arrière-plan)

    // Cercle natal (plus visible)
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius * 0.55, 0, 2 * Math.PI);
    this.ctx.strokeStyle = '#F44336';
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([6, 3]);
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Cercle du transit actuel (plus visible)
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius * 0.9, 0, 2 * Math.PI);
    this.ctx.strokeStyle = '#1976D2';
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([8, 4]);
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Dessiner les cercles des multi-transits (sans les planètes)
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.drawCircularMultiTransitCircles(centerX, centerY, radius);
    }

    // Calculer les aspects entre transits pour la mise en valeur
    const transitAspects = this.calculateTransitToTransitAspects();

    // Ensuite dessiner toutes les planètes (premier plan)
    this.drawCircularPlanetsWithAspects(centerX, centerY, radius, this.currentBirthPositions, false, true, 0, transitAspects);
    this.drawCircularPlanetsWithAspects(centerX, centerY, radius, this.currentTransitPositions, true, true, 0, transitAspects);

    // Dessiner les planètes des multi-transits
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.drawCircularMultiTransitPlanets(centerX, centerY, radius);
    }

    // Dessiner la légende des transits avec les aspects
    this.drawTransitLegendWithAspects(this.canvas.width, this.canvas.height, transitAspects);
  }

  drawCircular30Zodiac(centerX, centerY, radius) {
    // Dessiner le cercle de base
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    this.ctx.strokeStyle = '#333';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Dessiner les 12 secteurs de 2.5° chacun
    for (let i = 0; i < 12; i++) {
      const angle = (i * 30 - 90) * Math.PI / 180; // Proportionnel : 30° = 360°

      // Ligne de séparation
      this.ctx.beginPath();
      this.ctx.moveTo(centerX, centerY);
      this.ctx.lineTo(
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle)
      );
      this.ctx.strokeStyle = '#ddd';
      this.ctx.lineWidth = 1;
      this.ctx.stroke();

      // Numéro du secteur
      const labelAngle = angle + (30 * Math.PI / 180) / 2;
      const labelRadius = radius * 0.85;
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      this.ctx.fillStyle = '#666';
      this.ctx.font = 'bold 14px Arial'; // Augmenté de 12px à 14px et ajouté bold
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(`S${i + 1}`, labelX, labelY);
    }

    // Dessiner d'abord tous les cercles en pointillés (arrière-plan)

    // Cercle natal (plus visible)
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius * 0.55, 0, 2 * Math.PI);
    this.ctx.strokeStyle = '#F44336';
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([6, 3]);
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Cercle du transit actuel (plus visible)
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius * 0.9, 0, 2 * Math.PI);
    this.ctx.strokeStyle = '#1976D2';
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([8, 4]);
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Dessiner les cercles des multi-transits (sans les planètes)
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.drawCircularMultiTransitCircles(centerX, centerY, radius);
    }

    // Calculer les aspects entre transits pour la mise en valeur
    const transitAspects = this.calculateTransitToTransitAspects();

    // Ensuite dessiner toutes les planètes (premier plan)
    this.drawCircularPlanetsWithAspects(centerX, centerY, radius, this.currentBirthPositions, false, false, 0, transitAspects);
    this.drawCircularPlanetsWithAspects(centerX, centerY, radius, this.currentTransitPositions, true, false, 0, transitAspects);

    // Dessiner les planètes des multi-transits
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.drawCircularMultiTransitPlanets(centerX, centerY, radius);
    }

    // Dessiner la légende des transits avec les aspects
    this.drawTransitLegendWithAspects(this.canvas.width, this.canvas.height, transitAspects);
  }

  drawCircularPlanets(centerX, centerY, radius, positions, isTransit, is360, transitIndex = 0) {
    if (!positions) return;

    const planetSymbols = {
      'sun': '☉', 'moon': '☽', 'mercury': '☿', 'venus': '♀', 'mars': '♂',
      'jupiter': '♃', 'saturn': '♄', 'uranus': '♅', 'neptune': '♆',
      'pluto': '♇', 'north_node': '☊', 'south_node': '☋'
    };

    // Différentes nuances de bleu pour les transits
    const transitColors = [
      '#1976D2', // Bleu foncé
      '#2196F3', // Bleu standard
      '#42A5F5', // Bleu moyen
      '#64B5F6', // Bleu clair
      '#90CAF9', // Bleu très clair
      '#BBDEFB'  // Bleu pastel
    ];

    const planetRadius = isTransit ? radius * (0.9 - transitIndex * 0.15) : radius * 0.45; // Natal plus proche du centre
    const color = isTransit ? transitColors[transitIndex % transitColors.length] : '#F44336';

    // Collecter toutes les planètes avec leurs angles pour éviter les chevauchements
    const planetsToPlace = [];

    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position && planetSymbols[planetKey]) {
        let angle;

        if (is360) {
          // Mode 360° : position absolue
          const pos360 = this.get360Position(position);
          angle = (pos360 - 90) * Math.PI / 180;
        } else {
          // Mode 30° : position proportionnelle
          const pos30 = this.getProportionalPosition(position);
          angle = (pos30 * 12 - 90) * Math.PI / 180; // Multiplier par 12 pour étaler sur 360°
        }

        planetsToPlace.push({
          key: planetKey,
          symbol: planetSymbols[planetKey],
          angle: angle,
          originalAngle: angle
        });
      }
    });

    // Trier par angle pour traitement séquentiel
    planetsToPlace.sort((a, b) => a.angle - b.angle);

    // Ajuster les angles pour éviter les chevauchements
    const minAngleDistance = isTransit ? 0.18 + (transitIndex * 0.02) : 0.15; // Distance variable selon la taille

    for (let i = 1; i < planetsToPlace.length; i++) {
      const current = planetsToPlace[i];
      const previous = planetsToPlace[i - 1];

      if (current.angle - previous.angle < minAngleDistance) {
        current.angle = previous.angle + minAngleDistance;
      }
    }

    // Dessiner les planètes avec les positions ajustées
    planetsToPlace.forEach(planet => {
      const planetX = centerX + planetRadius * Math.cos(planet.angle);
      const planetY = centerY + planetRadius * Math.sin(planet.angle);

      // Taille variable selon le type et l'index de transit
      let planetSize, fontSize;
      if (isTransit) {
        // Transits externes plus grands
        planetSize = 16 + (transitIndex * 2); // 16, 18, 20, 22, etc.
        fontSize = 18 + (transitIndex * 2); // 18, 20, 22, 24, etc.
      } else {
        // Natal plus grand et plus visible
        planetSize = 18;
        fontSize = 16;
      }

      // Dessiner la planète avec taille variable
      this.ctx.beginPath();
      this.ctx.arc(planetX, planetY, planetSize, 0, 2 * Math.PI);
      this.ctx.fillStyle = color;
      this.ctx.fill();
      this.ctx.strokeStyle = '#fff';
      this.ctx.lineWidth = 2;
      this.ctx.stroke();

      // Symbole de la planète avec taille variable
      this.ctx.fillStyle = '#fff';
      this.ctx.font = `bold ${fontSize}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(planet.symbol, planetX, planetY);
    });
  }

  drawCircularMultiTransitCircles(centerX, centerY, radius) {
    // Différentes nuances de bleu pour les cercles de transit
    const transitCircleColors = [
      '#1976D2', // Bleu foncé
      '#2196F3', // Bleu standard
      '#42A5F5', // Bleu moyen
      '#64B5F6', // Bleu clair
      '#90CAF9', // Bleu très clair
      '#BBDEFB'  // Bleu pastel
    ];

    this.multiTransits.forEach((transit, index) => {
      if (transit.positions) {
        const color = transitCircleColors[index % transitCircleColors.length];
        const transitRadius = radius * (0.9 - index * 0.15); // Cercles beaucoup plus espacés

        // Dessiner seulement le cercle de ce transit
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, transitRadius, 0, 2 * Math.PI);
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2; // Ligne plus épaisse pour mieux voir
        this.ctx.setLineDash([8, 4]); // Tirets plus visibles
        this.ctx.stroke();
        this.ctx.setLineDash([]);
      }
    });
  }

  drawCircularMultiTransitPlanets(centerX, centerY, radius) {
    // Calculer les aspects entre transits pour la mise en valeur
    const transitAspects = this.calculateTransitToTransitAspects();

    this.multiTransits.forEach((transit, index) => {
      if (transit.positions) {
        // Dessiner les planètes de ce transit avec l'index pour la couleur et les aspects
        this.drawCircularPlanetsWithAspects(centerX, centerY, radius, transit.positions, true, this.is360Mode, index + 1, transitAspects);
      }
    });
  }

  calculateTransitToTransitAspects() {
    const aspects = [];
    const orb = 0.5; // Orb de 0.5° pour conjonction et opposition

    // Comparer chaque transit avec tous les autres
    for (let i = 0; i < this.multiTransits.length; i++) {
      for (let j = i + 1; j < this.multiTransits.length; j++) {
        const transit1 = this.multiTransits[i];
        const transit2 = this.multiTransits[j];

        if (transit1.positions && transit2.positions) {
          // Comparer chaque planète du transit 1 avec chaque planète du transit 2
          Object.entries(transit1.positions).forEach(([planet1Key, planet1Pos]) => {
            Object.entries(transit2.positions).forEach(([planet2Key, planet2Pos]) => {
              if (planet1Pos && planet2Pos) {
                let pos1, pos2;

                if (this.is360Mode) {
                  // Mode 360° : positions absolues
                  pos1 = this.get360Position(planet1Pos);
                  pos2 = this.get360Position(planet2Pos);
                } else {
                  // Mode 30° : positions proportionnelles
                  pos1 = this.getProportionalPosition(planet1Pos);
                  pos2 = this.getProportionalPosition(planet2Pos);
                }

                // Calculer la séparation angulaire
                const maxDegree = this.is360Mode ? 360 : 30;
                let separation = Math.abs(pos1 - pos2);
                if (separation > maxDegree / 2) {
                  separation = maxDegree - separation;
                }

                // Vérifier conjonction (0°) et opposition (180° en 360°, 15° en 30°)
                const oppositionAngle = this.is360Mode ? 180 : 15;

                if (separation <= orb) {
                  // Conjonction
                  aspects.push({
                    transit1Index: i,
                    transit2Index: j,
                    planet1: planet1Key,
                    planet2: planet2Key,
                    aspectType: 'conjunction',
                    orb: separation.toFixed(2)
                  });
                } else if (Math.abs(separation - oppositionAngle) <= orb) {
                  // Opposition
                  aspects.push({
                    transit1Index: i,
                    transit2Index: j,
                    planet1: planet1Key,
                    planet2: planet2Key,
                    aspectType: 'opposition',
                    orb: Math.abs(separation - oppositionAngle).toFixed(2)
                  });
                }
              }
            });
          });
        }
      }
    }

    return aspects;
  }

  drawCircularPlanetsWithAspects(centerX, centerY, radius, positions, isTransit, is360, transitIndex = 0, transitAspects = []) {
    if (!positions) return;

    const planetSymbols = {
      'sun': '☉', 'moon': '☽', 'mercury': '☿', 'venus': '♀', 'mars': '♂',
      'jupiter': '♃', 'saturn': '♄', 'uranus': '♅', 'neptune': '♆',
      'pluto': '♇', 'north_node': '☊', 'south_node': '☋'
    };

    // Différentes nuances de bleu pour les transits
    const transitColors = [
      '#1976D2', // Bleu foncé
      '#2196F3', // Bleu standard
      '#42A5F5', // Bleu moyen
      '#64B5F6', // Bleu clair
      '#90CAF9', // Bleu très clair
      '#BBDEFB'  // Bleu pastel
    ];

    const planetRadius = isTransit ? radius * (0.9 - transitIndex * 0.15) : radius * 0.45; // Natal plus proche du centre
    const color = isTransit ? transitColors[transitIndex % transitColors.length] : '#F44336';

    // Collecter toutes les planètes avec leurs angles pour éviter les chevauchements
    const planetsToPlace = [];

    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position && planetSymbols[planetKey]) {
        let angle;

        if (is360) {
          // Mode 360° : position absolue
          const pos360 = this.get360Position(position);
          angle = (pos360 - 90) * Math.PI / 180;
        } else {
          // Mode 30° : position proportionnelle
          const pos30 = this.getProportionalPosition(position);
          angle = (pos30 * 12 - 90) * Math.PI / 180; // Multiplier par 12 pour étaler sur 360°
        }

        // Vérifier si cette planète est impliquée dans un aspect
        const hasAspect = transitAspects.some(aspect =>
          (aspect.transit1Index === transitIndex - 1 && aspect.planet1 === planetKey) ||
          (aspect.transit2Index === transitIndex - 1 && aspect.planet2 === planetKey)
        );

        planetsToPlace.push({
          key: planetKey,
          symbol: planetSymbols[planetKey],
          angle: angle,
          originalAngle: angle,
          hasAspect: hasAspect
        });
      }
    });

    // Éviter les chevauchements
    const minAngleDistance = 0.2; // Distance minimale en radians
    planetsToPlace.sort((a, b) => a.angle - b.angle);

    for (let i = 1; i < planetsToPlace.length; i++) {
      const current = planetsToPlace[i];
      const previous = planetsToPlace[i - 1];

      if (Math.abs(current.angle - previous.angle) < minAngleDistance) {
        current.angle = previous.angle + minAngleDistance;
      }
    }

    // Dessiner les planètes avec les positions ajustées
    planetsToPlace.forEach(planet => {
      const planetX = centerX + planetRadius * Math.cos(planet.angle);
      const planetY = centerY + planetRadius * Math.sin(planet.angle);

      // Taille variable selon le type et l'index de transit
      let planetSize, fontSize;
      if (isTransit) {
        // Transits externes plus grands
        planetSize = 16 + (transitIndex * 2); // 16, 18, 20, 22, etc.
        fontSize = 18 + (transitIndex * 2); // 18, 20, 22, 24, etc.
      } else {
        // Natal plus grand et plus visible
        planetSize = 18;
        fontSize = 16;
      }

      // Mise en valeur si la planète a un aspect
      if (planet.hasAspect) {
        // Halo doré pour les planètes en aspect
        this.ctx.beginPath();
        this.ctx.arc(planetX, planetY, planetSize + 6, 0, 2 * Math.PI);
        this.ctx.fillStyle = 'rgba(255, 215, 0, 0.4)'; // Or transparent
        this.ctx.fill();

        // Bordure dorée
        this.ctx.strokeStyle = '#FFD700'; // Or
        this.ctx.lineWidth = 3;
        this.ctx.stroke();
      }

      // Dessiner la planète avec taille variable
      this.ctx.beginPath();
      this.ctx.arc(planetX, planetY, planetSize, 0, 2 * Math.PI);
      this.ctx.fillStyle = color;
      this.ctx.fill();
      this.ctx.strokeStyle = '#fff';
      this.ctx.lineWidth = 2;
      this.ctx.stroke();

      // Symbole de la planète avec taille variable
      this.ctx.fillStyle = '#fff';
      this.ctx.font = `bold ${fontSize}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(planet.symbol, planetX, planetY);
    });
  }

  drawTransitLegend(width, height) {
    if (!this.isCircularMode) return; // Seulement en mode circulaire

    const legendX = 20;
    const legendY = height - 120;
    const legendWidth = 200;
    const legendHeight = 100;

    // Fond de la légende
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
    this.ctx.fillRect(legendX, legendY, legendWidth, legendHeight);
    this.ctx.strokeStyle = '#ddd';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(legendX, legendY, legendWidth, legendHeight);

    // Titre de la légende
    this.ctx.fillStyle = '#333';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('Transits :', legendX + 10, legendY + 10);

    // Couleurs des transits
    const transitColors = [
      '#F44336', // Rouge pour natal
      '#1976D2', // Bleu foncé pour transit actuel
      '#2196F3', // Bleu standard
      '#42A5F5', // Bleu moyen
      '#64B5F6', // Bleu clair
      '#90CAF9', // Bleu très clair
      '#BBDEFB'  // Bleu pastel
    ];

    const labels = ['Natal', 'Transit actuel'];

    // Ajouter les labels des multi-transits
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.multiTransits.forEach((transit, index) => {
        const date = new Date(transit.date);
        labels.push(`${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`);
      });
    }

    // Dessiner les éléments de la légende
    labels.forEach((label, index) => {
      if (index < transitColors.length) {
        const itemY = legendY + 30 + (index * 12);

        // Cercle coloré (taille cohérente avec les planètes)
        this.ctx.beginPath();
        this.ctx.arc(legendX + 20, itemY, 7, 0, 2 * Math.PI); // Légèrement plus grand pour la visibilité
        this.ctx.fillStyle = transitColors[index];
        this.ctx.fill();
        this.ctx.strokeStyle = '#fff';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();

        // Label
        this.ctx.fillStyle = '#333';
        this.ctx.font = '11px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(label, legendX + 35, itemY);
      }
    });
  }

  drawTransitLegendWithAspects(width, height, transitAspects = []) {
    if (!this.isCircularMode) return; // Seulement en mode circulaire

    const legendX = 20;
    const legendY = height - 180; // Plus haut pour faire de la place aux aspects
    const legendWidth = 280; // Plus large pour les aspects
    const legendHeight = 160; // Plus haut pour les aspects

    // Fond de la légende
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
    this.ctx.fillRect(legendX, legendY, legendWidth, legendHeight);
    this.ctx.strokeStyle = '#ddd';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(legendX, legendY, legendWidth, legendHeight);

    // Titre de la légende
    this.ctx.fillStyle = '#333';
    this.ctx.font = 'bold 14px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText('Transits :', legendX + 10, legendY + 10);

    // Couleurs des transits
    const transitColors = [
      '#F44336', // Rouge pour natal
      '#1976D2', // Bleu foncé pour transit actuel
      '#2196F3', // Bleu standard
      '#42A5F5', // Bleu moyen
      '#64B5F6', // Bleu clair
      '#90CAF9', // Bleu très clair
      '#BBDEFB'  // Bleu pastel
    ];

    const labels = ['Natal', 'Transit actuel'];

    // Ajouter les labels des multi-transits
    if (this.multiTransits && this.multiTransits.length > 0) {
      this.multiTransits.forEach((transit, index) => {
        const date = new Date(transit.date);
        labels.push(`${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`);
      });
    }

    // Dessiner les éléments de la légende
    labels.forEach((label, index) => {
      if (index < transitColors.length) {
        const itemY = legendY + 30 + (index * 12);

        // Cercle coloré (taille cohérente avec les planètes)
        this.ctx.beginPath();
        this.ctx.arc(legendX + 20, itemY, 7, 0, 2 * Math.PI);
        this.ctx.fillStyle = transitColors[index];
        this.ctx.fill();
        this.ctx.strokeStyle = '#fff';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();

        // Label
        this.ctx.fillStyle = '#333';
        this.ctx.font = '11px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(label, legendX + 35, itemY);
      }
    });

    // Section des aspects
    if (transitAspects.length > 0) {
      const aspectsStartY = legendY + 30 + (labels.length * 12) + 15;

      // Titre des aspects
      this.ctx.fillStyle = '#333';
      this.ctx.font = 'bold 12px Arial';
      this.ctx.fillText('Aspects entre transits :', legendX + 10, aspectsStartY);

      // Symbole de mise en valeur
      this.ctx.beginPath();
      this.ctx.arc(legendX + 20, aspectsStartY + 20, 10, 0, 2 * Math.PI);
      this.ctx.fillStyle = 'rgba(255, 215, 0, 0.4)'; // Or transparent
      this.ctx.fill();
      this.ctx.strokeStyle = '#FFD700'; // Or
      this.ctx.lineWidth = 2;
      this.ctx.stroke();

      this.ctx.fillStyle = '#333';
      this.ctx.font = '10px Arial';
      this.ctx.fillText('Planètes en aspect (orb ≤ 0.5°)', legendX + 35, aspectsStartY + 20);

      // Lister les aspects trouvés
      transitAspects.slice(0, 3).forEach((aspect, index) => { // Limiter à 3 aspects pour l'espace
        const aspectY = aspectsStartY + 40 + (index * 12);
        const planetSymbols = {
          'sun': '☉', 'moon': '☽', 'mercury': '☿', 'venus': '♀', 'mars': '♂',
          'jupiter': '♃', 'saturn': '♄', 'uranus': '♅', 'neptune': '♆',
          'pluto': '♇', 'north_node': '☊', 'south_node': '☋'
        };

        const symbol1 = planetSymbols[aspect.planet1] || aspect.planet1;
        const symbol2 = planetSymbols[aspect.planet2] || aspect.planet2;
        const aspectSymbol = aspect.aspectType === 'conjunction' ? '☌' : '☍';
        const aspectText = `${symbol1} ${aspectSymbol} ${symbol2} (${aspect.orb}°)`;

        this.ctx.fillStyle = '#666';
        this.ctx.font = '9px Arial';
        this.ctx.fillText(aspectText, legendX + 10, aspectY);
      });

      if (transitAspects.length > 3) {
        const moreY = aspectsStartY + 40 + (3 * 12);
        this.ctx.fillStyle = '#999';
        this.ctx.font = '9px Arial';
        this.ctx.fillText(`... et ${transitAspects.length - 3} autre(s)`, legendX + 10, moreY);
      }
    }
  }

  drawZodiacBackground(x, y, width, height) {
    // Fond principal
    this.ctx.fillStyle = '#f8f9fa';
    this.ctx.fillRect(x, y, width, height);

    // Bordure
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(x, y, width, height);
  }

  drawZodiacSectors(x, y, width, height) {
    const sectors = [
      { number: 1, start: 0, end: 2.5 },
      { number: 2, start: 2.5, end: 5 },
      { number: 3, start: 5, end: 7.5 },
      { number: 4, start: 7.5, end: 10 },
      { number: 5, start: 10, end: 12.5 },
      { number: 6, start: 12.5, end: 15 },
      { number: 7, start: 15, end: 17.5 },
      { number: 8, start: 17.5, end: 20 },
      { number: 9, start: 20, end: 22.5 },
      { number: 10, start: 22.5, end: 25 },
      { number: 11, start: 25, end: 27.5 },
      { number: 12, start: 27.5, end: 30 }
    ];

    sectors.forEach((sector, index) => {
      const sectorX = x + (sector.start / 30) * width;
      const sectorWidth = ((sector.end - sector.start) / 30) * width;

      // Vérifier si ce secteur est marqué
      const marking = this.markedSectors.get(sector.number);

      // Couleur du secteur (marqué ou alternée normale)
      if (marking) {
        // Secteur marqué avec couleur personnalisée et transparence
        this.ctx.fillStyle = marking.color + '40'; // Ajouter transparence (40 = 25% opacité)
      } else {
        // Couleur alternée normale
        this.ctx.fillStyle = index % 2 === 0 ? '#ffffff' : '#f0f0f0';
      }
      this.ctx.fillRect(sectorX, y, sectorWidth, height);

      // Bordure du secteur (plus épaisse si marqué)
      if (marking) {
        this.ctx.strokeStyle = marking.color;
        this.ctx.lineWidth = 3;
      } else {
        this.ctx.strokeStyle = '#dee2e6';
        this.ctx.lineWidth = 1;
      }
      this.ctx.strokeRect(sectorX, y, sectorWidth, height);

      // Degrés seulement (suppression du numéro du secteur pour éviter le doublon)
      this.ctx.font = '8px Arial';
      this.ctx.fillStyle = '#6c757d';
      this.ctx.fillText(`${sector.start}°`, sectorX + 2, y + 12);
      this.ctx.fillText(`${sector.end}°`, sectorX + sectorWidth - 15, y + 12);

      // Indicateur visuel pour les secteurs marqués
      if (marking) {
        // Icône de surveillance en haut du secteur
        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = marking.color;
        this.ctx.textAlign = 'center';
        this.ctx.fillText('👁️', sectorX + sectorWidth / 2, y + 25);

        // Nombre de planètes surveillées
        this.ctx.font = '8px Arial';
        this.ctx.fillStyle = marking.color;
        this.ctx.fillText(`${marking.planets.size}`, sectorX + sectorWidth / 2, y + 35);

        // Réinitialiser l'alignement du texte
        this.ctx.textAlign = 'left';
      }
    });
  }

  drawZodiacSectors360(x, y, width, height) {
    // 12 signes zodiacaux de 30° chacun
    const signs = [
      { name: 'Bélier', start: 0, end: 30 },
      { name: 'Taureau', start: 30, end: 60 },
      { name: 'Gémeaux', start: 60, end: 90 },
      { name: 'Cancer', start: 90, end: 120 },
      { name: 'Lion', start: 120, end: 150 },
      { name: 'Vierge', start: 150, end: 180 },
      { name: 'Balance', start: 180, end: 210 },
      { name: 'Scorpion', start: 210, end: 240 },
      { name: 'Sagittaire', start: 240, end: 270 },
      { name: 'Capricorne', start: 270, end: 300 },
      { name: 'Verseau', start: 300, end: 330 },
      { name: 'Poissons', start: 330, end: 360 }
    ];

    signs.forEach((sign, index) => {
      const signX = x + (sign.start / 360) * width;
      const signWidth = ((sign.end - sign.start) / 360) * width;

      // Couleur alternée pour les signes
      this.ctx.fillStyle = index % 2 === 0 ? '#ffffff' : '#f0f0f0';
      this.ctx.fillRect(signX, y, signWidth, height);

      // Bordure du signe
      this.ctx.strokeStyle = '#dee2e6';
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(signX, y, signWidth, height);

      // Degrés aux extrémités
      this.ctx.font = '8px Arial';
      this.ctx.fillStyle = '#6c757d';
      this.ctx.fillText(`${sign.start}°`, signX + 2, y + 12);
      if (index === signs.length - 1) {
        this.ctx.fillText(`${sign.end}°`, signX + signWidth - 15, y + 12);
      }
    });
  }

  drawSignNames360(x, y, width) {
    const signs = [
      { name: 'Bélier', start: 0, end: 30 },
      { name: 'Taureau', start: 30, end: 60 },
      { name: 'Gémeaux', start: 60, end: 90 },
      { name: 'Cancer', start: 90, end: 120 },
      { name: 'Lion', start: 120, end: 150 },
      { name: 'Vierge', start: 150, end: 180 },
      { name: 'Balance', start: 180, end: 210 },
      { name: 'Scorpion', start: 210, end: 240 },
      { name: 'Sagittaire', start: 240, end: 270 },
      { name: 'Capricorne', start: 270, end: 300 },
      { name: 'Verseau', start: 300, end: 330 },
      { name: 'Poissons', start: 330, end: 360 }
    ];

    // Dessiner une ligne de séparation
    this.ctx.beginPath();
    this.ctx.moveTo(x, y + 5);
    this.ctx.lineTo(x + width, y + 5);
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Dessiner les noms des signes en dessous
    signs.forEach((sign, index) => {
      const signX = x + (sign.start / 360) * width;
      const signWidth = ((sign.end - sign.start) / 360) * width;

      // Fond alterné pour les noms
      this.ctx.fillStyle = index % 2 === 0 ? '#f8f9fa' : '#ffffff';
      this.ctx.fillRect(signX, y + 5, signWidth, 25);

      // Bordure légère
      this.ctx.strokeStyle = '#e9ecef';
      this.ctx.lineWidth = 0.5;
      this.ctx.strokeRect(signX, y + 5, signWidth, 25);

      // Nom du signe
      this.ctx.font = '10px Arial';
      this.ctx.fillStyle = '#495057';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(sign.name, signX + signWidth / 2, y + 17);
    });
  }

  drawSectorNames(x, y, width) {
    const sectors = [
      { number: 1, start: 0, end: 2.5 },
      { number: 2, start: 2.5, end: 5 },
      { number: 3, start: 5, end: 7.5 },
      { number: 4, start: 7.5, end: 10 },
      { number: 5, start: 10, end: 12.5 },
      { number: 6, start: 12.5, end: 15 },
      { number: 7, start: 15, end: 17.5 },
      { number: 8, start: 17.5, end: 20 },
      { number: 9, start: 20, end: 22.5 },
      { number: 10, start: 22.5, end: 25 },
      { number: 11, start: 25, end: 27.5 },
      { number: 12, start: 27.5, end: 30 }
    ];

    // Dessiner une ligne de séparation
    this.ctx.beginPath();
    this.ctx.moveTo(x, y + 5);
    this.ctx.lineTo(x + width, y + 5);
    this.ctx.strokeStyle = '#dee2e6';
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Dessiner les noms des secteurs en dessous
    sectors.forEach((sector, index) => {
      const sectorX = x + (sector.start / 30) * width;
      const sectorWidth = ((sector.end - sector.start) / 30) * width;

      // Fond alterné pour les noms
      this.ctx.fillStyle = index % 2 === 0 ? '#f8f9fa' : '#ffffff';
      this.ctx.fillRect(sectorX, y + 5, sectorWidth, 25);

      // Bordure légère
      this.ctx.strokeStyle = '#e9ecef';
      this.ctx.lineWidth = 0.5;
      this.ctx.strokeRect(sectorX, y + 5, sectorWidth, 25);

      // Nom du secteur (déplacé en dessous)
      this.ctx.font = '10px Arial';
      this.ctx.fillStyle = '#495057';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(`Secteur ${sector.number}`, sectorX + sectorWidth / 2, y + 17);
    });
  }

  drawPlanets(positions, x, y, width, isTransit) {
    if (!positions) return;

    // Définir les symboles des planètes
    const planetSymbols = {
      'sun': '☉',
      'moon': '☽',
      'mercury': '☿',
      'venus': '♀',
      'mars': '♂',
      'jupiter': '♃',
      'saturn': '♄',
      'uranus': '♅',
      'neptune': '♆',
      'pluto': '♇',
      'north_node': '☊',
      'south_node': '☋'
    };

    const planetNames = {
      'sun': 'Soleil',
      'moon': 'Lune',
      'mercury': 'Mercure',
      'venus': 'Vénus',
      'mars': 'Mars',
      'jupiter': 'Jupiter',
      'saturn': 'Saturne',
      'uranus': 'Uranus',
      'neptune': 'Neptune',
      'pluto': 'Pluton',
      'north_node': 'Nœud Nord',
      'south_node': 'Nœud Sud'
    };

    // Préparer les planètes avec leurs positions
    const planetsToPlace = [];
    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position && planetSymbols[planetKey]) { // Ignorer les planètes non reconnues
        // Calculer la position selon le mode (30° ou 360°)
        let proportionalPosition;

        if (this.is360Mode) {
          proportionalPosition = this.get360Position(position);
        } else {
          if (typeof position.proportionalPosition === 'number') {
            proportionalPosition = position.proportionalPosition;
          } else if (position.degree !== undefined) {
            proportionalPosition = this.getProportionalPosition(position);
          } else {
            return;
          }
        }

        const maxDegree = this.is360Mode ? 360 : 30;
        const planetX = x + (proportionalPosition / maxDegree) * width;

        // Créer un objet planète enrichi
        const enrichedPosition = {
          ...position,
          proportionalPosition: proportionalPosition,
          symbol: planetSymbols[planetKey],
          name: planetNames[planetKey] || planetKey,
          key: planetKey,
          x: planetX
        };

        planetsToPlace.push(enrichedPosition);
      }
    });

    // Trier les planètes par position X
    planetsToPlace.sort((a, b) => a.x - b.x);

    // Calculer les niveaux pour éviter les chevauchements
    const planetLevels = this.calculatePlanetLevels(planetsToPlace, width);

    // Dessiner les planètes avec leurs niveaux calculés
    planetsToPlace.forEach((planet, index) => {
      const level = planetLevels[index];
      const adjustedY = y + (level * 35); // 35px d'espacement vertical entre les niveaux
      this.drawPlanet(planet.x, adjustedY, planet, isTransit);
    });
  }

  calculatePlanetLevels(planets, width) {
    const levels = new Array(planets.length).fill(0);
    // Ajuster la distance minimale selon le mode
    const maxDegree = this.is360Mode ? 360 : 30;
    const minDistance = (0.5 / maxDegree) * width; // Distance minimale en pixels pour 0.5°

    for (let i = 0; i < planets.length; i++) {
      let currentLevel = 0;
      let placed = false;

      while (!placed) {
        let canPlace = true;

        // Vérifier si on peut placer la planète à ce niveau
        for (let j = 0; j < i; j++) {
          if (levels[j] === currentLevel) {
            const distance = Math.abs(planets[i].x - planets[j].x);
            if (distance < minDistance) {
              canPlace = false;
              break;
            }
          }
        }

        if (canPlace) {
          levels[i] = currentLevel;
          placed = true;
        } else {
          currentLevel++;
        }
      }
    }

    return levels;
  }

  // Fonction pour calculer la position proportionnelle (0-30°)
  // UTILISE LA MÊME LOGIQUE QUE LE ZODIAQUE CIRCULAIRE
  getProportionalPosition(planetData) {
    if (!planetData) return 0;

    // Si c'est déjà un nombre, le retourner
    if (typeof planetData === 'number') {
      return planetData % 30;
    }

    // CORRECTION : Utiliser la même logique que le zodiaque circulaire
    if (planetData.sign && planetData.degree !== undefined) {
      // Utiliser les mêmes signes que dans sidepanel.js
      const ZODIAC_SIGNS = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                           'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];

      const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);

      if (signIndex !== -1) {
        // MÊME CALCUL que dans sidepanel.js lignes 6718-6719 et 6738-6739
        const positionInSign = parseFloat(planetData.degree);
        const totalPosition = (signIndex * 30) + positionInSign;
        const normalizedPosition = totalPosition % 30;  // CORRECTION : pas de division par 12 !

        return normalizedPosition;
      }
    }

    // Si on a seulement degree et que c'est déjà en mode 30°
    if (planetData.degree !== undefined) {
      const degree = parseFloat(planetData.degree);
      if (!isNaN(degree) && degree <= 30) {
        return degree % 30;
      }
    }

    return 0;
  }

  // Fonction pour calculer la position en mode 360°
  get360Position(planetData) {
    if (!planetData) return 0;

    // Si c'est déjà un nombre, le retourner
    if (typeof planetData === 'number') {
      return planetData % 360;
    }

    // Utiliser la même logique que le zodiaque circulaire pour 360°
    if (planetData.sign && planetData.degree !== undefined) {
      const ZODIAC_SIGNS = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
                           'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];

      const signIndex = ZODIAC_SIGNS.indexOf(planetData.sign);

      if (signIndex !== -1) {
        // Calcul pour 360° : position absolue dans le zodiaque
        const positionInSign = parseFloat(planetData.degree);
        const totalPosition = (signIndex * 30) + positionInSign;
        return totalPosition % 360;
      }
    }

    // Si on a seulement degree et que c'est déjà en mode 360°
    if (planetData.degree !== undefined) {
      const degree = parseFloat(planetData.degree);
      if (!isNaN(degree)) {
        return degree % 360;
      }
    }

    return 0;
  }

  drawPlanet(x, y, position, isTransit) {
    const radius = 15; // Plus gros pour meilleure visibilité

    // Couleurs : Rouge pour natal, Bleu pour transit
    const color = isTransit ? '#2196F3' : '#F44336'; // Bleu pour transit, rouge pour natal
    const borderColor = isTransit ? '#1976D2' : '#D32F2F';

    // Ombre portée pour effet 3D
    this.ctx.beginPath();
    this.ctx.arc(x + 2, y + 2, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fill();

    // Cercle principal de la planète
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();

    // Bordure blanche épaisse pour contraste
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 3;
    this.ctx.stroke();

    // Bordure colorée fine
    this.ctx.strokeStyle = borderColor;
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Symbole de la planète plus gros et plus net
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(position.symbol, x, y);

    // Nom de la planète en dessous plus visible
    this.ctx.fillStyle = color;
    this.ctx.font = 'bold 10px Arial';
    this.ctx.fillText(position.name, x, y + radius + 15);

    // Position en degrés plus lisible
    this.ctx.font = 'bold 9px Arial';
    this.ctx.fillStyle = '#495057';
    this.ctx.fillText(`${position.proportionalPosition.toFixed(1)}°`, x, y + radius + 28);
  }

  drawMultiTransits(x, y, width, height) {
    // Dessiner les transits multiples sur des lignes séparées au-dessus - plus d'espace pour les niveaux
    this.multiTransits.forEach((transit, index) => {
      const transitY = y - 80 - (index * 80); // Plus d'espace entre les lignes pour les niveaux

      // Ligne de fond pour ce transit avec couleur différente par transit
      const colors = ['#FF6B35', '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'];
      const color = colors[index % colors.length];

      this.ctx.fillStyle = `${color}20`; // Transparence 20%
      this.ctx.fillRect(x, transitY - 35, width, 70); // Plus d'espace pour les niveaux multiples

      // Bordure de la ligne de transit
      this.ctx.strokeStyle = color;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(x, transitY - 35, width, 70);

      // Label du transit avec date formatée
      this.ctx.fillStyle = color;
      this.ctx.font = 'bold 9px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'middle';
      const dateStr = this.formatTransitDate(transit.date);
      this.ctx.fillText(`T${index + 1}: ${dateStr}`, x + 5, transitY - 30);

      // Dessiner les planètes de ce transit
      if (transit.positions) {
        this.drawMultiTransitPlanets(transit.positions, x, transitY, width, color, index);
      }
    });
  }

  formatTransitDate(dateStr) {
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      });
    } catch (e) {
      return dateStr;
    }
  }

  drawMultiTransitPlanets(positions, x, y, width, color, transitIndex) {
    if (!positions) return;

    // Définir les symboles des planètes
    const planetSymbols = {
      'sun': '☉',
      'moon': '☽',
      'mercury': '☿',
      'venus': '♀',
      'mars': '♂',
      'jupiter': '♃',
      'saturn': '♄',
      'uranus': '♅',
      'neptune': '♆',
      'pluto': '♇',
      'north_node': '☊',
      'south_node': '☋'
    };

    // Préparer les planètes avec leurs positions
    const planetsToPlace = [];
    Object.entries(positions).forEach(([planetKey, position]) => {
      if (position && planetSymbols[planetKey]) { // Ignorer les planètes non reconnues
        // Calculer la position selon le mode (30° ou 360°)
        let proportionalPosition;

        if (this.is360Mode) {
          proportionalPosition = this.get360Position(position);
        } else {
          if (typeof position.proportionalPosition === 'number') {
            proportionalPosition = position.proportionalPosition;
          } else if (position.degree !== undefined) {
            proportionalPosition = this.getProportionalPosition(position);
          } else {
            return;
          }
        }

        const maxDegree = this.is360Mode ? 360 : 30;
        const planetX = x + (proportionalPosition / maxDegree) * width;

        // Créer un objet planète enrichi
        const enrichedPosition = {
          ...position,
          proportionalPosition: proportionalPosition,
          symbol: planetSymbols[planetKey],
          x: planetX
        };

        planetsToPlace.push(enrichedPosition);
      }
    });

    // Trier les planètes par position X
    planetsToPlace.sort((a, b) => a.x - b.x);

    // Calculer les niveaux pour éviter les chevauchements
    const planetLevels = this.calculatePlanetLevels(planetsToPlace, width);

    // Dessiner les planètes avec leurs niveaux calculés
    planetsToPlace.forEach((planet, index) => {
      const level = planetLevels[index];
      const adjustedY = y + (level * 35); // 35px d'espacement vertical entre les niveaux
      this.drawMultiTransitPlanet(planet.x, adjustedY, planet, color, transitIndex);
    });
  }

  drawMultiTransitPlanet(x, y, position, color, transitIndex) {
    const radius = 15; // MÊME TAILLE que les marqueurs principaux

    // Ombre portée pour effet 3D (comme les marqueurs principaux)
    this.ctx.beginPath();
    this.ctx.arc(x + 2, y + 2, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    this.ctx.fill();

    // Cercle principal de la planète avec couleur spécifique
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = color;
    this.ctx.fill();

    // Bordure blanche épaisse pour contraste (comme les marqueurs principaux)
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 3;
    this.ctx.stroke();

    // Bordure colorée fine
    this.ctx.strokeStyle = color;
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    // Symbole de la planète MÊME TAILLE que les marqueurs principaux
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(position.symbol, x, y);

    // Position en degrés plus lisible (même style que les marqueurs principaux)
    this.ctx.font = 'bold 9px Arial';
    this.ctx.fillStyle = '#495057';
    this.ctx.fillText(`${position.proportionalPosition.toFixed(1)}°`, x, y + radius + 28);
  }

  drawAspects(x, y, width, height) {
    // Dessiner les lignes d'aspects entre planètes natales et de transit
    if (!this.currentBirthPositions || !this.currentTransitPositions) return;

    // Calculer et dessiner les aspects
    Object.entries(this.currentBirthPositions).forEach(([natalKey, natalPos]) => {
      Object.entries(this.currentTransitPositions).forEach(([transitKey, transitPos]) => {
        if (natalPos && transitPos) {
          // Calculer les positions proportionnelles
          const natalProportionalPos = typeof natalPos.proportionalPosition === 'number'
            ? natalPos.proportionalPosition
            : this.getProportionalPosition(natalPos);

          const transitProportionalPos = typeof transitPos.proportionalPosition === 'number'
            ? transitPos.proportionalPosition
            : this.getProportionalPosition(transitPos);

          const aspect = this.calculateAspect(natalProportionalPos, transitProportionalPos);
          if (aspect) {
            // Créer des objets enrichis avec les positions calculées
            const enrichedNatalPos = { ...natalPos, proportionalPosition: natalProportionalPos };
            const enrichedTransitPos = { ...transitPos, proportionalPosition: transitProportionalPos };

            this.drawAspectLine(x, y, width, height, enrichedNatalPos, enrichedTransitPos, aspect);
          }
        }
      });
    });
  }

  calculateAspect(pos1, pos2) {
    const diff = Math.abs(pos1 - pos2);
    const wrappedDiff = Math.min(diff, 30 - diff);

    // Aspects proportionnels 30° avec orb plus permissif pour test
    const aspects = [
      { name: 'Conjonction', angle: 0, orb: 1.0, color: '#ff0000' },
      { name: 'Sextile', angle: 5, orb: 1.0, color: '#00aa00' },
      { name: 'Carré', angle: 7.5, orb: 1.0, color: '#ff6600' },
      { name: 'Trigone', angle: 10, orb: 1.0, color: '#0066ff' },
      { name: 'Opposition', angle: 15, orb: 1.0, color: '#aa0000' }
    ];

    for (const aspect of aspects) {
      if (Math.abs(wrappedDiff - aspect.angle) <= aspect.orb) {
        return {
          ...aspect,
          orb: Math.abs(wrappedDiff - aspect.angle).toFixed(2)
        };
      }
    }
    return null;
  }

  drawAspectLine(x, y, width, height, natalPos, transitPos, aspect) {
    const natalX = x + (natalPos.proportionalPosition / 30) * width;
    const transitX = x + (transitPos.proportionalPosition / 30) * width;
    const natalY = y + height - 30; // Ajusté pour être plus proche des planètes natales
    const transitY = y + 20; // Ajusté pour être plus proche des planètes de transit

    // Ligne d'aspect plus visible
    this.ctx.beginPath();
    this.ctx.moveTo(natalX, natalY);
    this.ctx.lineTo(transitX, transitY);
    this.ctx.strokeStyle = aspect.color;
    this.ctx.lineWidth = 3; // Plus épais
    this.ctx.setLineDash([8, 4]); // Tirets plus visibles
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Fond blanc pour le label
    const midX = (natalX + transitX) / 2;
    const midY = (natalY + transitY) / 2;

    const labelText = `${aspect.name} (${aspect.orb}°)`;
    this.ctx.font = 'bold 9px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';

    // Mesurer le texte pour le fond
    const textMetrics = this.ctx.measureText(labelText);
    const textWidth = textMetrics.width + 6;
    const textHeight = 14;

    // Fond blanc
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    this.ctx.fillRect(midX - textWidth/2, midY - textHeight/2, textWidth, textHeight);

    // Bordure du fond
    this.ctx.strokeStyle = aspect.color;
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(midX - textWidth/2, midY - textHeight/2, textWidth, textHeight);

    // Texte de l'aspect
    this.ctx.fillStyle = aspect.color;
    this.ctx.fillText(labelText, midX, midY);
  }
}

// Initialiser le système de zodiaque linéaire
let linearZodiacSystem;

document.addEventListener('DOMContentLoaded', function() {
  linearZodiacSystem = new LinearZodiacSystem();

  // Synchroniser avec les changements du zodiaque principal
  const originalDrawFunction = window.drawCircularProportionalZodiac;
  if (originalDrawFunction) {
    window.drawCircularProportionalZodiac = function(birthPositions, transitPositions) {
      // Sauvegarder les modes actuels du zodiaque linéaire avant la synchronisation
      const saved360Mode = linearZodiacSystem ? linearZodiacSystem.is360Mode : false;
      const savedCircularMode = linearZodiacSystem ? linearZodiacSystem.isCircularMode : false;

      // Appeler la fonction originale
      const result = originalDrawFunction.call(this, birthPositions, transitPositions);

      // Mettre à jour le zodiaque linéaire s'il est visible, en préservant ses modes
      if (linearZodiacSystem && linearZodiacSystem.isVisible) {
        setTimeout(() => {
          // Restaurer les modes avant le refresh
          linearZodiacSystem.is360Mode = saved360Mode;
          linearZodiacSystem.isCircularMode = savedCircularMode;

          // Effectuer le refresh qui préservera maintenant les modes
          linearZodiacSystem.refresh();
        }, 100);
      }

      return result;
    };
  }
});

// Fonction globale pour ouvrir le zodiaque linéaire depuis l'extérieur
window.showLinearZodiac = function() {
  if (linearZodiacSystem) {
    linearZodiacSystem.show();
  }
};
