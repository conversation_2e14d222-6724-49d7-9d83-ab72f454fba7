/**
 * Système de migration et synchronisation des données
 * Migre les données de localStorage vers chrome.storage.sync pour persistance
 */

class StorageMigration {
  constructor() {
    this.storageKeys = [
      'astrologicalRules',
      'miniCalendarColors', 
      'customInterpretations',
      'birthDate',
      'birthHour',
      'birthMinute'
    ];
    this.init();
  }

  async init() {
    await this.migrateFromLocalStorage();
    this.setupStorageWrappers();
  }

  // Migrer les données de localStorage vers chrome.storage.sync
  async migrateFromLocalStorage() {
    console.log('Début de la migration des données...');
    
    const dataToMigrate = {};
    let hasDataToMigrate = false;

    // Vérifier chaque clé dans localStorage
    for (const key of this.storageKeys) {
      const localData = localStorage.getItem(key);
      if (localData) {
        try {
          // Essayer de parser en JSON, sinon garder comme string
          const parsedData = key === 'birthDate' ? localData : JSON.parse(localData);
          dataToMigrate[key] = parsedData;
          hasDataToMigrate = true;
          console.log(`Données trouvées pour ${key}:`, parsedData);
        } catch (error) {
          // Si ce n'est pas du JSON valide, garder comme string
          dataToMigrate[key] = localData;
          hasDataToMigrate = true;
          console.log(`Données string trouvées pour ${key}:`, localData);
        }
      }
    }

    if (hasDataToMigrate) {
      // Vérifier si les données existent déjà dans chrome.storage.sync
      const existingData = await this.getChromeStorageData(this.storageKeys);
      
      // Fusionner les données (localStorage a priorité pour la migration initiale)
      const mergedData = { ...existingData, ...dataToMigrate };
      
      // Sauvegarder dans chrome.storage.sync
      await this.setChromeStorageData(mergedData);
      
      console.log('Migration terminée. Données sauvegardées dans chrome.storage.sync');
      
      // Optionnel: nettoyer localStorage après migration réussie
      // this.clearLocalStorageData();
    } else {
      console.log('Aucune donnée à migrer depuis localStorage');
    }
  }

  // Obtenir des données depuis chrome.storage.sync
  getChromeStorageData(keys) {
    return new Promise((resolve) => {
      chrome.storage.sync.get(keys, (result) => {
        resolve(result);
      });
    });
  }

  // Sauvegarder des données dans chrome.storage.sync
  setChromeStorageData(data) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  // Nettoyer localStorage (optionnel)
  clearLocalStorageData() {
    for (const key of this.storageKeys) {
      localStorage.removeItem(key);
    }
    console.log('localStorage nettoyé après migration');
  }

  // Configurer les wrappers pour remplacer localStorage
  setupStorageWrappers() {
    // Créer des fonctions globales pour remplacer localStorage
    window.setStorageItem = async (key, value) => {
      const data = {};
      data[key] = typeof value === 'string' ? value : JSON.parse(value);
      await this.setChromeStorageData(data);
    };

    window.getStorageItem = async (key) => {
      const result = await this.getChromeStorageData([key]);
      const value = result[key];
      return typeof value === 'object' ? JSON.stringify(value) : value;
    };

    window.removeStorageItem = async (key) => {
      chrome.storage.sync.remove([key]);
    };
  }

  // Fonction pour sauvegarder les règles astrologiques
  static async saveAstrologicalRules(rules) {
    const data = { astrologicalRules: rules };
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  // Fonction pour charger les règles astrologiques
  static async loadAstrologicalRules() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['astrologicalRules'], (result) => {
        resolve(result.astrologicalRules || []);
      });
    });
  }

  // Fonction pour sauvegarder les couleurs du calendrier
  static async saveCalendarColors(colors) {
    const data = { miniCalendarColors: colors };
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  // Fonction pour charger les couleurs du calendrier
  static async loadCalendarColors() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['miniCalendarColors'], (result) => {
        resolve(result.miniCalendarColors || {});
      });
    });
  }

  // Fonction pour sauvegarder les interprétations personnalisées
  static async saveCustomInterpretations(interpretations) {
    const data = { customInterpretations: interpretations };
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  // Fonction pour charger les interprétations personnalisées
  static async loadCustomInterpretations() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['customInterpretations'], (result) => {
        resolve(result.customInterpretations || {});
      });
    });
  }

  // Fonction pour sauvegarder les données de naissance
  static async saveBirthData(birthDate, birthHour, birthMinute) {
    const data = { 
      birthDate: birthDate,
      birthHour: birthHour,
      birthMinute: birthMinute
    };
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  // Fonction pour charger les données de naissance
  static async loadBirthData() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['birthDate', 'birthHour', 'birthMinute'], (result) => {
        resolve({
          birthDate: result.birthDate || '1991-12-17',
          birthHour: result.birthHour || 7,
          birthMinute: result.birthMinute || 27
        });
      });
    });
  }

  // Fonction pour exporter toutes les données
  static async exportAllData() {
    const allKeys = [
      'astrologicalRules',
      'miniCalendarColors', 
      'customInterpretations',
      'birthDate',
      'birthHour',
      'birthMinute'
    ];
    
    return new Promise((resolve) => {
      chrome.storage.sync.get(allKeys, (result) => {
        resolve(result);
      });
    });
  }

  // Fonction pour importer toutes les données
  static async importAllData(data) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set(data, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }
}

// Initialiser la migration au chargement
if (typeof chrome !== 'undefined' && chrome.storage) {
  const migration = new StorageMigration();
}

// Exporter pour utilisation dans d'autres fichiers
window.StorageMigration = StorageMigration;
