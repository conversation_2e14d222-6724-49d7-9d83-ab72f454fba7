document.addEventListener('DOMContentLoaded', function() {
  // Initialisation des onglets
  initTabs();

  // Initialisation des graphiques
  initCharts();

  // Écouteur d'événement pour le bouton de calcul
  document.getElementById('calculate-chart').addEventListener('click', calculateChart);

  // Définir la date de naissance par défaut (17/12/1991 7h27)
  setDefaultBirthData();

  // Charger les données sauvegardées ou utiliser les valeurs par défaut
  loadSavedData();

  // Calculer la carte immédiatement
  calculateChart();
});

// Fonction pour définir les données de naissance par défaut
function setDefaultBirthData() {
  const birthDateInput = document.getElementById('birth-date');
  const birthTimeInput = document.getElementById('birth-time');
  const birthLocationInput = document.getElementById('birth-location');

  // Définir la date de naissance par défaut (17/12/1991)
  if (!birthDateInput.value) {
    birthDateInput.value = '1991-12-17';
  }

  // Définir l'heure de naissance par défaut (7h27)
  if (!birthTimeInput.value) {
    birthTimeInput.value = '07:27';
  }

  // Définir le lieu de naissance par défaut
  if (!birthLocationInput.value) {
    birthLocationInput.value = 'Paris, France';
  }
}

// Fonction pour initialiser les onglets
function initTabs() {
  const tabs = document.querySelectorAll('.tab-button');
  const charts = document.querySelectorAll('.chart');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Retirer la classe active de tous les onglets et graphiques
      tabs.forEach(t => t.classList.remove('active'));
      charts.forEach(c => c.classList.remove('active'));

      // Ajouter la classe active à l'onglet cliqué
      tab.classList.add('active');

      // Activer le graphique correspondant
      const chartId = tab.id.replace('tab-', 'chart-');
      document.getElementById(chartId).classList.add('active');
    });
  });
}

// Fonction pour initialiser les graphiques
function initCharts() {
  // Initialiser les graphiques vides
  initChart360();
  initChart30();
  initChartNakshatra();
}

// Fonction pour initialiser le graphique 360°
function initChart360() {
  try {
    const svgContainer = document.getElementById('chart-360-svg');
    if (!svgContainer) {
      console.error("Conteneur SVG non trouvé pour le graphique 360°");
      return;
    }

    // Définir des dimensions par défaut si le conteneur n'a pas encore de dimensions
    const containerWidth = svgContainer.clientWidth || 400;
    const containerHeight = svgContainer.clientHeight || 400;

    // Créer l'élément SVG s'il n'existe pas déjà
    let svgElement = svgContainer.querySelector('svg');
    if (!svgElement) {
      svgElement = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svgContainer.appendChild(svgElement);
    }

    // Définir les dimensions du SVG
    svgElement.setAttribute('width', containerWidth);
    svgElement.setAttribute('height', containerHeight);

    // Effacer le contenu existant
    while (svgElement.firstChild) {
      svgElement.removeChild(svgElement.firstChild);
    }

    // Calculer le rayon
    const radius = Math.min(containerWidth, containerHeight) / 2 - 40;

    // Créer le groupe principal centré
    const g = document.createElementNS("http://www.w3.org/2000/svg", "g");
    g.setAttribute('transform', `translate(${containerWidth / 2}, ${containerHeight / 2})`);
    svgElement.appendChild(g);

    // Dessiner le cercle extérieur
    const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    circle.setAttribute('r', radius);
    circle.setAttribute('fill', 'none');
    circle.setAttribute('stroke', '#ccc');
    circle.setAttribute('stroke-width', 1);
    g.appendChild(circle);

    // Dessiner les lignes des signes (12 sections)
    for (let i = 0; i < 12; i++) {
      const angle = (i * 30) * (Math.PI / 180);
      const x2 = radius * Math.cos(angle - Math.PI / 2);
      const y2 = radius * Math.sin(angle - Math.PI / 2);

      const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
      line.setAttribute('x1', 0);
      line.setAttribute('y1', 0);
      line.setAttribute('x2', x2);
      line.setAttribute('y2', y2);
      line.setAttribute('stroke', '#ccc');
      line.setAttribute('stroke-width', 1);
      g.appendChild(line);
    }

    // Ajouter les symboles des signes
    const signSymbols = ['♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓'];

    for (let i = 0; i < 12; i++) {
      const angle = ((i * 30) + 15) * (Math.PI / 180);
      const x = (radius - 20) * Math.cos(angle - Math.PI / 2);
      const y = (radius - 20) * Math.sin(angle - Math.PI / 2);

      const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
      text.setAttribute('x', x);
      text.setAttribute('y', y);
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('dominant-baseline', 'central');
      text.setAttribute('font-size', '16px');
      text.textContent = signSymbols[i];
      g.appendChild(text);
    }

    console.log("Graphique 360° initialisé avec succès");
  } catch (error) {
    console.error("Erreur lors de l'initialisation du graphique 360°:", error);
  }
}

// Fonction pour initialiser le graphique 30°
function initChart30() {
  try {
    const svgContainer = document.getElementById('chart-30-svg');
    if (!svgContainer) {
      console.error("Conteneur SVG non trouvé pour le graphique 30°");
      return;
    }

    // Définir des dimensions par défaut si le conteneur n'a pas encore de dimensions
    const containerWidth = svgContainer.clientWidth || 400;
    const containerHeight = svgContainer.clientHeight || 400;

    // Créer l'élément SVG s'il n'existe pas déjà
    let svgElement = svgContainer.querySelector('svg');
    if (!svgElement) {
      svgElement = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svgContainer.appendChild(svgElement);
    }

    // Définir les dimensions du SVG
    svgElement.setAttribute('width', containerWidth);
    svgElement.setAttribute('height', containerHeight);

    // Effacer le contenu existant
    while (svgElement.firstChild) {
      svgElement.removeChild(svgElement.firstChild);
    }

    // Calculer le rayon
    const radius = Math.min(containerWidth, containerHeight) / 2 - 40;

    // Créer le groupe principal centré
    const g = document.createElementNS("http://www.w3.org/2000/svg", "g");
    g.setAttribute('transform', `translate(${containerWidth / 2}, ${containerHeight / 2})`);
    svgElement.appendChild(g);

    // Dessiner le cercle extérieur
    const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    circle.setAttribute('r', radius);
    circle.setAttribute('fill', 'none');
    circle.setAttribute('stroke', '#ccc');
    circle.setAttribute('stroke-width', 1);
    g.appendChild(circle);

    // Dessiner les marqueurs de degrés (tous les 10 degrés)
    for (let i = 0; i < 36; i++) {
      const angle = (i * 10) * (Math.PI / 180);
      const innerRadius = i % 3 === 0 ? radius - 10 : radius - 5;
      const x1 = radius * Math.cos(angle - Math.PI / 2);
      const y1 = radius * Math.sin(angle - Math.PI / 2);
      const x2 = innerRadius * Math.cos(angle - Math.PI / 2);
      const y2 = innerRadius * Math.sin(angle - Math.PI / 2);

      const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
      line.setAttribute('x1', x1);
      line.setAttribute('y1', y1);
      line.setAttribute('x2', x2);
      line.setAttribute('y2', y2);
      line.setAttribute('stroke', '#ccc');
      line.setAttribute('stroke-width', 1);
      g.appendChild(line);

      // Ajouter les étiquettes pour les multiples de 10°
      if (i % 3 === 0) {
        const labelRadius = radius - 25;
        const labelX = labelRadius * Math.cos(angle - Math.PI / 2);
        const labelY = labelRadius * Math.sin(angle - Math.PI / 2);

        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
        text.setAttribute('x', labelX);
        text.setAttribute('y', labelY);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('dominant-baseline', 'central');
        text.setAttribute('font-size', '12px');
        text.textContent = `${i * 10}°`;
        g.appendChild(text);
      }
    }

    console.log("Graphique 30° initialisé avec succès");
  } catch (error) {
    console.error("Erreur lors de l'initialisation du graphique 30°:", error);
  }
}

// Fonction pour initialiser le graphique Nakshatra
function initChartNakshatra() {
  try {
    const svgContainer = document.getElementById('chart-nakshatra-svg');
    if (!svgContainer) {
      console.error("Conteneur SVG non trouvé pour le graphique Nakshatra");
      return;
    }

    // Définir des dimensions par défaut si le conteneur n'a pas encore de dimensions
    const containerWidth = svgContainer.clientWidth || 400;
    const containerHeight = svgContainer.clientHeight || 400;

    // Créer l'élément SVG s'il n'existe pas déjà
    let svgElement = svgContainer.querySelector('svg');
    if (!svgElement) {
      svgElement = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svgContainer.appendChild(svgElement);
    }

    // Définir les dimensions du SVG
    svgElement.setAttribute('width', containerWidth);
    svgElement.setAttribute('height', containerHeight);

    // Effacer le contenu existant
    while (svgElement.firstChild) {
      svgElement.removeChild(svgElement.firstChild);
    }

    // Calculer le rayon
    const radius = Math.min(containerWidth, containerHeight) / 2 - 40;

    // Créer le groupe principal centré
    const g = document.createElementNS("http://www.w3.org/2000/svg", "g");
    g.setAttribute('transform', `translate(${containerWidth / 2}, ${containerHeight / 2})`);
    svgElement.appendChild(g);

    // Dessiner le cercle extérieur
    const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    circle.setAttribute('r', radius);
    circle.setAttribute('fill', 'none');
    circle.setAttribute('stroke', '#ccc');
    circle.setAttribute('stroke-width', 1);
    g.appendChild(circle);

    // Dessiner les marqueurs pour les segments de Nakshatra (tous les 1.2°)
    for (let i = 0; i < 11; i++) {
      const angle = (i * 1.2) * (Math.PI / 180);
      const innerRadius = i % 5 === 0 ? radius - 10 : radius - 5;
      const x1 = radius * Math.cos(angle - Math.PI / 2);
      const y1 = radius * Math.sin(angle - Math.PI / 2);
      const x2 = innerRadius * Math.cos(angle - Math.PI / 2);
      const y2 = innerRadius * Math.sin(angle - Math.PI / 2);

      const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
      line.setAttribute('x1', x1);
      line.setAttribute('y1', y1);
      line.setAttribute('x2', x2);
      line.setAttribute('y2', y2);
      line.setAttribute('stroke', '#ccc');
      line.setAttribute('stroke-width', 1);
      g.appendChild(line);

      // Ajouter les étiquettes pour les multiples de 5°
      if (i % 5 === 0) {
        const labelRadius = radius - 25;
        const labelX = labelRadius * Math.cos(angle - Math.PI / 2);
        const labelY = labelRadius * Math.sin(angle - Math.PI / 2);

        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
        text.setAttribute('x', labelX);
        text.setAttribute('y', labelY);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('dominant-baseline', 'central');
        text.setAttribute('font-size', '12px');
        text.textContent = `${i * 1.2}°`;
        g.appendChild(text);
      }
    }

    console.log("Graphique Nakshatra initialisé avec succès");
  } catch (error) {
    console.error("Erreur lors de l'initialisation du graphique Nakshatra:", error);
  }
}

// Fonction pour calculer la carte astrologique
function calculateChart() {
  try {
    console.log("Début du calcul de la carte astrologique");

    const birthDate = document.getElementById('birth-date').value;
    const birthTime = document.getElementById('birth-time').value;
    const birthLocation = document.getElementById('birth-location').value;

    console.log("Données de naissance:", birthDate, birthTime, birthLocation);

    if (!birthDate || !birthTime || !birthLocation) {
      alert('Veuillez remplir tous les champs de données de naissance.');
      return;
    }

    // Sauvegarder les données
    saveData(birthDate, birthTime, birthLocation);

    // Calculer les positions planétaires en utilisant Swiss Ephemeris
    console.log("Calcul de la carte de naissance...");
    const birthChart = calculateBirthChart(birthDate, birthTime, birthLocation);
    console.log("Carte de naissance calculée:", birthChart);

    console.log("Calcul des transits actuels...");
    const currentTransits = calculateCurrentTransits();
    console.log("Transits actuels calculés:", currentTransits);

    // Mettre à jour les graphiques avec les données calculées
    console.log("Mise à jour du graphique 360°...");
    updateChart360(birthChart, currentTransits);

    console.log("Mise à jour du graphique 30°...");
    updateChart30(birthChart, currentTransits);

    console.log("Mise à jour du graphique Nakshatra...");
    updateChartNakshatra(birthChart, currentTransits);

    console.log("Calcul de la carte astrologique terminé avec succès");
  } catch (error) {
    console.error("Erreur lors du calcul de la carte astrologique:", error);
  }
}

// Fonction pour calculer la carte de naissance
function calculateBirthChart(birthDate, birthTime, birthLocation) {
  // Utiliser notre bibliothèque Swiss Ephemeris simulée
  const tropicalPositions = SwissEph.calculatePlanetPositions(birthDate, birthTime, 0, 0);

  // Convertir les positions tropicales en positions sidérales
  // Utiliser un ayanamsa d'environ 24 degrés (approximation pour l'ayanamsa de Lahiri)
  const siderealPositions = SwissEph.tropicalToSidereal(tropicalPositions, 24);

  return siderealPositions;
}

// Fonction pour calculer les transits actuels
function calculateCurrentTransits() {
  // Obtenir les positions planétaires actuelles
  const tropicalPositions = SwissEph.getCurrentPlanetPositions();

  // Convertir en positions sidérales
  const siderealPositions = SwissEph.tropicalToSidereal(tropicalPositions, 24);

  return siderealPositions;
}

// Fonction pour mettre à jour le graphique 360°
function updateChart360(birthChart, currentTransits) {
  try {
    const svgContainer = document.getElementById('chart-360-svg');
    if (!svgContainer) {
      console.error("Conteneur SVG non trouvé pour le graphique 360°");
      return;
    }

    // Trouver l'élément SVG
    const svgElement = svgContainer.querySelector('svg');
    if (!svgElement) {
      console.error("Élément SVG non trouvé, initialisation du graphique 360° nécessaire");
      initChart360();
      return;
    }

    // Trouver le groupe principal
    let g = svgElement.querySelector('g');
    if (!g) {
      console.error("Groupe principal non trouvé, initialisation du graphique 360° nécessaire");
      initChart360();
      return;
    }

    // Supprimer les planètes existantes
    const planetSymbols = svgElement.querySelectorAll('.planet-symbol');
    planetSymbols.forEach(symbol => symbol.remove());

    // Définir les dimensions
    const width = svgElement.getAttribute('width');
    const height = svgElement.getAttribute('height');
    const radius = Math.min(width, height) / 2 - 40;

    // Ajouter les planètes de la carte de naissance (cercles rouges)
    addPlanetsToChart360(g, birthChart, radius * 0.7, 'birth-chart', true);

    // Ajouter les planètes des transits actuels (cercles bleus)
    addPlanetsToChart360(g, currentTransits, radius * 0.5, 'current-transits', false);

    console.log('Graphique 360° mis à jour avec:', birthChart, currentTransits);
  } catch (error) {
    console.error("Erreur lors de la mise à jour du graphique 360°:", error);
  }
}

// Fonction pour mettre à jour le graphique 30°
function updateChart30(birthChart, currentTransits) {
  try {
    const svgContainer = document.getElementById('chart-30-svg');
    if (!svgContainer) {
      console.error("Conteneur SVG non trouvé pour le graphique 30°");
      return;
    }

    // Trouver l'élément SVG
    const svgElement = svgContainer.querySelector('svg');
    if (!svgElement) {
      console.error("Élément SVG non trouvé, initialisation du graphique 30° nécessaire");
      initChart30();
      return;
    }

    // Trouver le groupe principal
    let g = svgElement.querySelector('g');
    if (!g) {
      console.error("Groupe principal non trouvé, initialisation du graphique 30° nécessaire");
      initChart30();
      return;
    }

    // Supprimer les planètes existantes
    const planetSymbols = svgElement.querySelectorAll('.planet-symbol');
    planetSymbols.forEach(symbol => symbol.remove());

    // Définir les dimensions
    const width = svgElement.getAttribute('width');
    const height = svgElement.getAttribute('height');
    const radius = Math.min(width, height) / 2 - 40;

    // Ajouter les planètes de la carte de naissance (cercles rouges)
    // Pour le mode 30°, nous n'utilisons que le degré dans le signe
    addPlanetsToChart30DOM(g, birthChart, radius * 0.7, 'birth-chart', true);

    // Ajouter les planètes des transits actuels (cercles bleus)
    addPlanetsToChart30DOM(g, currentTransits, radius * 0.5, 'current-transits', false);

    console.log('Graphique 30° mis à jour avec:', birthChart, currentTransits);
  } catch (error) {
    console.error("Erreur lors de la mise à jour du graphique 30°:", error);
  }
}

// Fonction pour mettre à jour le graphique Nakshatra
function updateChartNakshatra(birthChart, currentTransits) {
  try {
    const svgContainer = document.getElementById('chart-nakshatra-svg');
    if (!svgContainer) {
      console.error("Conteneur SVG non trouvé pour le graphique Nakshatra");
      return;
    }

    // Trouver l'élément SVG
    const svgElement = svgContainer.querySelector('svg');
    if (!svgElement) {
      console.error("Élément SVG non trouvé, initialisation du graphique Nakshatra nécessaire");
      initChartNakshatra();
      return;
    }

    // Trouver le groupe principal
    let g = svgElement.querySelector('g');
    if (!g) {
      console.error("Groupe principal non trouvé, initialisation du graphique Nakshatra nécessaire");
      initChartNakshatra();
      return;
    }

    // Supprimer les planètes existantes
    const planetSymbols = svgElement.querySelectorAll('.planet-symbol');
    planetSymbols.forEach(symbol => symbol.remove());

    // Définir les dimensions
    const width = svgElement.getAttribute('width');
    const height = svgElement.getAttribute('height');
    const radius = Math.min(width, height) / 2 - 40;

    // Calculer les positions Nakshatra pour chaque planète
    const birthChartNakshatras = {};
    const currentTransitsNakshatras = {};

    for (const planetId in birthChart) {
      const position = birthChart[planetId];
      birthChartNakshatras[planetId] = SwissEph.calculateNakshatra(position.longitude);
    }

    for (const planetId in currentTransits) {
      const position = currentTransits[planetId];
      currentTransitsNakshatras[planetId] = SwissEph.calculateNakshatra(position.longitude);
    }

    // Ajouter les planètes de la carte de naissance (cercles rouges)
    addPlanetsToChartNakshatraDOM(g, birthChartNakshatras, radius * 0.7, 'birth-chart', true);

    // Ajouter les planètes des transits actuels (cercles bleus)
    addPlanetsToChartNakshatraDOM(g, currentTransitsNakshatras, radius * 0.5, 'current-transits', false);

    console.log('Graphique Nakshatra mis à jour avec:', birthChartNakshatras, currentTransitsNakshatras);
  } catch (error) {
    console.error("Erreur lors de la mise à jour du graphique Nakshatra:", error);
  }
}

// Fonction pour ajouter les planètes au graphique 360°
function addPlanetsToChart360(g, planetPositions, orbitRadius, className, isBirthChart) {
  try {
    const borderColor = isBirthChart ? '#e74c3c' : '#3498db';

    for (const planetId in planetPositions) {
      const position = planetPositions[planetId];

      // Calculer l'angle en radians (0° est à 3 heures, nous ajustons pour que 0° soit à 12 heures)
      const angle = (position.longitude * (Math.PI / 180)) - (Math.PI / 2);

      // Calculer les coordonnées x et y
      const x = orbitRadius * Math.cos(angle);
      const y = orbitRadius * Math.sin(angle);

      // Créer un groupe pour la planète
      const planetGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
      planetGroup.setAttribute('class', `planet-symbol ${className}`);
      planetGroup.setAttribute('transform', `translate(${x}, ${y})`);

      // Ajouter un cercle autour du symbole
      const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      circle.setAttribute('r', 15);
      circle.setAttribute('fill', 'white');
      circle.setAttribute('stroke', borderColor);
      circle.setAttribute('stroke-width', 2);
      planetGroup.appendChild(circle);

      // Ajouter le symbole de la planète
      const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('dominant-baseline', 'central');
      text.setAttribute('font-size', '14px');
      text.textContent = SwissEph.PLANET_SYMBOLS[planetId];
      planetGroup.appendChild(text);

      // Ajouter le groupe au graphique
      g.appendChild(planetGroup);
    }
  } catch (error) {
    console.error("Erreur lors de l'ajout des planètes au graphique 360°:", error);
  }
}

// Fonction pour ajouter les planètes au graphique 30°
function addPlanetsToChart30DOM(g, planetPositions, orbitRadius, className, isBirthChart) {
  try {
    const borderColor = isBirthChart ? '#e74c3c' : '#3498db';

    for (const planetId in planetPositions) {
      const position = planetPositions[planetId];

      // Pour le mode 30°, nous n'utilisons que le degré dans le signe (0-30)
      // Calculer l'angle en radians (0° est à 3 heures, nous ajustons pour que 0° soit à 12 heures)
      const angle = (position.degree * (Math.PI / 6)) - (Math.PI / 2);

      // Calculer les coordonnées x et y
      const x = orbitRadius * Math.cos(angle);
      const y = orbitRadius * Math.sin(angle);

      // Créer un groupe pour la planète
      const planetGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
      planetGroup.setAttribute('class', `planet-symbol ${className}`);
      planetGroup.setAttribute('transform', `translate(${x}, ${y})`);

      // Ajouter un cercle autour du symbole
      const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      circle.setAttribute('r', 15);
      circle.setAttribute('fill', 'white');
      circle.setAttribute('stroke', borderColor);
      circle.setAttribute('stroke-width', 2);
      planetGroup.appendChild(circle);

      // Ajouter le symbole de la planète
      const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('dominant-baseline', 'central');
      text.setAttribute('font-size', '14px');
      text.textContent = SwissEph.PLANET_SYMBOLS[planetId];
      planetGroup.appendChild(text);

      // Ajouter le groupe au graphique
      g.appendChild(planetGroup);
    }
  } catch (error) {
    console.error("Erreur lors de l'ajout des planètes au graphique 30°:", error);
  }
}

// Fonction pour ajouter les planètes au graphique Nakshatra
function addPlanetsToChartNakshatraDOM(g, planetNakshatras, orbitRadius, className, isBirthChart) {
  try {
    const borderColor = isBirthChart ? '#e74c3c' : '#3498db';

    for (const planetId in planetNakshatras) {
      const nakshatra = planetNakshatras[planetId];

      // Pour le mode Nakshatra, nous utilisons le degré dans le segment de nakshatra (0-13.20)
      // Calculer l'angle en radians (0° est à 3 heures, nous ajustons pour que 0° soit à 12 heures)
      const angle = (nakshatra.degree * (Math.PI / 6.6)) - (Math.PI / 2);

      // Calculer les coordonnées x et y
      const x = orbitRadius * Math.cos(angle);
      const y = orbitRadius * Math.sin(angle);

      // Créer un groupe pour la planète
      const planetGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
      planetGroup.setAttribute('class', `planet-symbol ${className}`);
      planetGroup.setAttribute('transform', `translate(${x}, ${y})`);

      // Ajouter un cercle autour du symbole
      const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      circle.setAttribute('r', 15);
      circle.setAttribute('fill', 'white');
      circle.setAttribute('stroke', borderColor);
      circle.setAttribute('stroke-width', 2);
      planetGroup.appendChild(circle);

      // Ajouter le symbole de la planète
      const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
      text.setAttribute('text-anchor', 'middle');
      text.setAttribute('dominant-baseline', 'central');
      text.setAttribute('font-size', '14px');
      text.textContent = SwissEph.PLANET_SYMBOLS[planetId];
      planetGroup.appendChild(text);

      // Ajouter le groupe au graphique
      g.appendChild(planetGroup);
    }
  } catch (error) {
    console.error("Erreur lors de l'ajout des planètes au graphique Nakshatra:", error);
  }
}

// Fonction pour sauvegarder les données
function saveData(birthDate, birthTime, birthLocation) {
  chrome.storage.sync.set({
    birthDate: birthDate,
    birthTime: birthTime,
    birthLocation: birthLocation
  });
}

// Fonction pour charger les données sauvegardées
function loadSavedData() {
  chrome.storage.sync.get(['birthDate', 'birthTime', 'birthLocation'], function(data) {
    const birthDateInput = document.getElementById('birth-date');
    const birthTimeInput = document.getElementById('birth-time');
    const birthLocationInput = document.getElementById('birth-location');

    // Utiliser les données sauvegardées si elles existent, sinon conserver les valeurs par défaut
    if (data.birthDate) {
      birthDateInput.value = data.birthDate;
    }
    if (data.birthTime) {
      birthTimeInput.value = data.birthTime;
    }
    if (data.birthLocation) {
      birthLocationInput.value = data.birthLocation;
    }
  });
}
