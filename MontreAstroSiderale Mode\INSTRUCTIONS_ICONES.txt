INSTRUCTIONS POUR LES ICÔNES DE L'EXTENSION

Pour que l'extension fonctionne correctement, vous devez créer trois icônes aux dimensions suivantes :

1. icon16.png - 16x16 pixels
2. icon48.png - 48x48 pixels
3. icon128.png - 128x128 pixels

Placez ces icônes dans le dossier "images" de l'extension.

Si vous n'avez pas encore créé ces icônes, vous pouvez temporairement utiliser des icônes génériques ou des images simples aux bonnes dimensions.

PROBLÈME AVEC LE PANNEAU LATÉRAL

L'erreur "Error at key 'side_panel.default_path': Manifest key is required" indique que Chrome essaie d'ouvrir l'extension dans le panneau latéral plutôt que dans une popup.

Pour résoudre ce problème, nous avons :
1. Supprimé la section "side_panel" du manifest.json
2. Simplifié le manifest.json pour n'inclure que les éléments essentiels
3. C<PERSON><PERSON> un fichier test-popup.html simple pour tester si l'extension s'ouvre correctement

Si l'extension continue à s'ouvrir dans le panneau latéral après ces modifications, essayez les solutions suivantes :

1. Assurez-vous que les icônes existent et sont aux bonnes dimensions
2. Essayez de désinstaller complètement l'extension et de la réinstaller
3. Essayez de créer une nouvelle extension avec un manifest.json minimal
4. Vérifiez si d'autres extensions installées pourraient interférer avec le comportement de votre extension
