// D3.js v7.8.5 (minified)
// Copyright 2010-2023 <PERSON>
// https://d3js.org
// This is a placeholder for the actual D3.js library
// In a real implementation, you would download the full D3.js library from https://d3js.org/
// For the purpose of this prototype, we're using a placeholder
console.log("D3.js library loaded (placeholder)");

// Basic D3 functionality for our prototype
window.d3 = {
  select: function(selector) {
    let element;

    // Si le sélecteur est déjà un élément DOM, l'utiliser directement
    if (selector instanceof Element) {
      element = selector;
    }
    // <PERSON><PERSON>, essayer de le sélectionner dans le document
    else if (typeof selector === 'string') {
      try {
        element = document.querySelector(selector);
      } catch (e) {
        console.error("Erreur de sélection:", e);
        element = null;
      }
    } else {
      console.error("Sélecteur invalide:", selector);
      element = null;
    }

    return {
      node: function() { return element; },
      selectAll: function(childSelector) { return d3.selectAll(childSelector, element); },
      append: function(tagName) {
        if (!element) {
          console.error("Impossible d'ajouter un élément à un élément null");
          return d3.select(document.createElement(tagName));
        }
        const newElement = document.createElement(tagName);
        element.appendChild(newElement);
        return d3.select(newElement);
      },
      attr: function(name, value) {
        if (element) {
          if (typeof value === 'function') {
            element.setAttribute(name, value());
          } else {
            element.setAttribute(name, value);
          }
        }
        return this;
      },
      style: function(name, value) {
        if (element) {
          element.style[name] = value;
        }
        return this;
      },
      text: function(value) {
        if (element) {
          element.textContent = value;
        }
        return this;
      },
      html: function(value) {
        if (element) {
          element.innerHTML = value;
        }
        return this;
      },
      on: function(eventName, handler) {
        if (element) {
          element.addEventListener(eventName, handler);
        }
        return this;
      },
      remove: function() {
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
        }
        return this;
      }
    };
  },
  selectAll: function(selector, parent) {
    const container = parent || document;
    let elements = [];

    try {
      if (typeof selector === 'string') {
        elements = container.querySelectorAll(selector);
      } else {
        console.error("Sélecteur invalide pour selectAll:", selector);
      }
    } catch (e) {
      console.error("Erreur lors de la sélection multiple:", e);
    }

    return {
      size: function() { return elements.length; },
      empty: function() { return elements.length === 0; },
      remove: function() {
        Array.from(elements).forEach(el => {
          if (el && el.parentNode) {
            el.parentNode.removeChild(el);
          }
        });
        return this;
      },
      each: function(callback) {
        Array.from(elements).forEach((el, i) => {
          callback.call(el, el, i);
        });
        return this;
      }
    };
  }
};
